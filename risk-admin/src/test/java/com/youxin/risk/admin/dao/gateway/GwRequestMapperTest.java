package com.youxin.risk.admin.dao.gateway;

import com.google.common.collect.Lists;
import com.youxin.risk.admin.dao.engine.EngineAsyncRequestLogMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.ArrayList;

import static org.junit.Assert.*;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations={
        "classpath:spring/spring-config.xml"
})
public class GwRequestMapperTest {
    @Autowired
    private GwRequestMapper gwRequestMapper;

    @Test
    public void selectAllBySessionIdList() {
        gwRequestMapper.selectAllBySessionIdList(Lists.newArrayList("20240827103622_4996412613723959793","20240827101536_7399307300195433039"));
    }
}