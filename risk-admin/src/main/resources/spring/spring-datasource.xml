<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns="http://www.springframework.org/schema/beans" xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:context="http://www.springframework.org/schema/context" xmlns:tx="http://www.springframework.org/schema/tx"
       xmlns:cache="http://www.springframework.org/schema/cache" xmlns:p="http://www.springframework.org/schema/p"
       xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
     http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
     http://www.springframework.org/schema/aop
     http://www.springframework.org/schema/aop/spring-aop-4.0.xsd
     http://www.springframework.org/schema/context
     http://www.springframework.org/schema/context/spring-context-4.0.xsd
     http://www.springframework.org/schema/util
     http://www.springframework.org/schema/util/spring-util-4.0.xsd 
     http://www.springframework.org/schema/tx
     http://www.springframework.org/schema/tx/spring-tx-4.0.xsd
     http://www.springframework.org/schema/cache http://www.springframework.org/schema/cache/spring-cache-4.0.xsd">

    <bean id="baseDruidDataSource" class="com.alibaba.druid.pool.DruidDataSource">
        <property name="maxActive" value="${datasource.maxActive}" />
        <property name="initialSize" value="${datasource.initialSize}" />
        <property name="minIdle" value="${datasource.minIdle}" />
        <property name="maxWait" value="${datasource.maxWait}" />
        <property name="testOnBorrow" value="${datasource.testOnBorrow}" />
        <property name="defaultTransactionIsolation" value="${datasource.defaultTransactionIsolation}" />
        <property name="timeBetweenEvictionRunsMillis" value="${datasource.timeBetweenEvictionRunsMillis}" />
        <property name="minEvictableIdleTimeMillis" value="${datasource.minEvictableIdleTimeMillis}" />
        <property name="timeBetweenLogStatsMillis" value="${datasource.timeBetweenLogStatsMillis}" />
        <property name="removeAbandoned" value="${datasource.druid.remove.abandoned}" />
        <property name="removeAbandonedTimeout" value="${datasource.druid.remove.abandoned.timeout}" />
        <property name="logAbandoned" value="${datasource.druid.log.abandoned}" />
        <property name="filters" value="${datasource.filters}"/>
        <property name="connectProperties">
            <props>
                <prop key="connectTimeout">${datasource.connectProperties.connectTimeout}</prop>
                <prop key="socketTimeout">${datasource.connectProperties.socketTimeout}</prop>
            </props>
        </property>
    </bean>

    <bean id="baseAdminDruidDataSource" class="com.alibaba.druid.pool.DruidDataSource">
        <property name="maxActive" value="${datasource.admin.maxActive}" />
        <property name="initialSize" value="${datasource.admin.initialSize}" />
        <property name="minIdle" value="${datasource.admin.minIdle}" />
        <property name="maxWait" value="${datasource.maxWait}" />
        <property name="testOnBorrow" value="${datasource.testOnBorrow}" />
        <property name="defaultTransactionIsolation" value="${datasource.defaultTransactionIsolation}" />
        <property name="timeBetweenEvictionRunsMillis" value="${datasource.timeBetweenEvictionRunsMillis}" />
        <property name="minEvictableIdleTimeMillis" value="${datasource.minEvictableIdleTimeMillis}" />
        <property name="timeBetweenLogStatsMillis" value="${datasource.timeBetweenLogStatsMillis}" />
        <property name="removeAbandoned" value="${datasource.druid.remove.abandoned}" />
        <property name="removeAbandonedTimeout" value="${datasource.druid.remove.abandoned.timeout}" />
        <property name="logAbandoned" value="${datasource.druid.log.abandoned}" />
        <property name="filters" value="${datasource.filters}"/>
        <property name="connectProperties">
            <props>
                <prop key="connectTimeout">${datasource.connectProperties.connectTimeout}</prop>
                <prop key="socketTimeout">${datasource.connectProperties.socketTimeout}</prop>
            </props>
        </property>
    </bean>

    <bean id="baseEngineDruidDataSource" class="com.alibaba.druid.pool.DruidDataSource">
        <property name="maxActive" value="${datasource.engine.maxActive}" />
        <property name="initialSize" value="${datasource.engine.initialSize}" />
        <property name="minIdle" value="${datasource.engine.minIdle}" />
        <property name="maxWait" value="${datasource.maxWait}" />
        <property name="testOnBorrow" value="${datasource.testOnBorrow}" />
        <property name="defaultTransactionIsolation" value="${datasource.defaultTransactionIsolation}" />
        <property name="timeBetweenEvictionRunsMillis" value="${datasource.timeBetweenEvictionRunsMillis}" />
        <property name="minEvictableIdleTimeMillis" value="${datasource.minEvictableIdleTimeMillis}" />
        <property name="timeBetweenLogStatsMillis" value="${datasource.timeBetweenLogStatsMillis}" />
        <property name="removeAbandoned" value="${datasource.druid.remove.abandoned}" />
        <property name="removeAbandonedTimeout" value="${datasource.druid.remove.abandoned.timeout}" />
        <property name="logAbandoned" value="${datasource.druid.log.abandoned}" />
        <property name="filters" value="${datasource.filters}"/>
        <property name="connectProperties">
            <props>
                <prop key="connectTimeout">${datasource.connectProperties.connectTimeout}</prop>
                <prop key="socketTimeout">${datasource.connectProperties.socketTimeout}</prop>
            </props>
        </property>
    </bean>

    <!-- admin datasource -->
    <bean id="adminDataSource" class="com.alibaba.druid.pool.DruidDataSource" parent="baseAdminDruidDataSource">
        <property name="name" value="adminDataSource" />
        <property name="username" value="${admin.datasource.username}" />
        <property name="password" value="${admin.datasource.pwd}" />
        <property name="url" value="${admin.datasource.url}" />
    </bean>

    <bean id="adminSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="adminDataSource"/>
        <property name="configLocation" value="classpath:mybatis-config.xml"/>
        <property name="mapperLocations">
            <list>
                <value>classpath*:mapper/admin/*.xml</value>
            </list>
        </property>
        <property name="typeAliasesPackage" value="com.youxin.risk.commons.model"/>
        <!-- 分页插件 -->
        <property name="plugins">
            <array>
                <bean class="com.github.pagehelper.PageHelper">
                    <property name="properties">
                        <value>
                            dialect=mysql
                        </value>
                    </property>
                </bean>
            </array>
        </property>
    </bean>
    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.youxin.risk.admin.dao.admin"/>
        <property name="sqlSessionFactoryBeanName" value="adminSqlSessionFactory"/>
    </bean>

    <!-- admin common datasource -->
    <bean id="adminConfigDataSource" class="com.alibaba.druid.pool.DruidDataSource" parent="baseAdminDruidDataSource">
        <property name="name" value="adminConfigDataSource" />
        <property name="username" value="${admin.datasource.username}" />
        <property name="password" value="${admin.datasource.pwd}" />
        <property name="url" value="${admin.datasource.url}" />
    </bean>

    <bean id="datasourceTemplateRouteInterceptor" class="com.youxin.risk.commons.dao.interceptor.DatasourceTemplateRouteInterceptor"/>

    <bean id="adminConfigSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="adminConfigDataSource"/>
        <property name="configLocation" value="classpath:mybatis-config.xml"/>
        <property name="mapperLocations">
            <list>
                <value>classpath*:mapper/admin/*.xml</value>
            </list>
        </property>
        <property name="plugins">
            <array>
                <ref bean="datasourceTemplateRouteInterceptor"/>
            </array>
        </property>
        <property name="typeAliasesPackage" value="com.youxin.risk.commons.model"/>
    </bean>
    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.youxin.risk.commons.dao.admin"/>
        <property name="sqlSessionFactoryBeanName" value="adminConfigSqlSessionFactory"/>
    </bean>
    <!--<bean id="dictionaryMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="com.youxin.risk.commons.dao.admin.DictionaryMapper" />
        <property name="sqlSessionFactory" ref="adminConfigSqlSessionFactory" />
    </bean>
    <bean id="sysUpdateTimeMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="com.youxin.risk.commons.dao.admin.SysUpdateTimeMapper" />
        <property name="sqlSessionFactory" ref="adminConfigSqlSessionFactory" />
    </bean>
    <bean id="adminAgencyMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">
        <property name="mapperInterface" value="com.youxin.risk.commons.dao.admin.AdminAgencyMapper" />
        <property name="sqlSessionFactory" ref="adminConfigSqlSessionFactory" />
    </bean>-->

    <!-- di datasource -->
    <bean id="diDataSource" class="com.alibaba.druid.pool.DruidDataSource" parent="baseDruidDataSource">
        <property name="name" value="diDataSource" />
        <property name="username" value="${di.datasource.username}" />
        <property name="password" value="${di.datasource.pwd}" />
        <property name="url" value="${di.datasource.url}" />
    </bean>
    <bean id="diSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="diDataSource"/>
        <property name="configLocation" value="classpath:mybatis-config.xml"/>
        <property name="mapperLocations">
            <list>
                <value>classpath*:mapper/di/*.xml</value>
            </list>
        </property>
        <property name="typeAliasesPackage" value="com.youxin.risk.commons.model"/>
    </bean>
    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.youxin.risk.admin.dao.di"/>
        <property name="sqlSessionFactoryBeanName" value="diSqlSessionFactory"/>
    </bean>
    <!-- channel datasource -->
    <bean id="channelDataSource" class="com.alibaba.druid.pool.DruidDataSource" parent="baseDruidDataSource">
        <property name="name" value="channelDataSource" />
        <property name="username" value="${channel.datasource.username}" />
        <property name="password" value="${channel.datasource.pwd}" />
        <property name="url" value="${channel.datasource.url}" />
    </bean>
    <bean id="channelSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="channelDataSource"/>
        <property name="configLocation" value="classpath:mybatis-config.xml"/>
        <property name="mapperLocations">
            <list>
                <value>classpath*:mapper/channel/*.xml</value>
            </list>
        </property>
        <property name="typeAliasesPackage" value="com.youxin.risk.commons.model"/>
    </bean>
    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.youxin.risk.admin.dao.channel"/>
        <property name="sqlSessionFactoryBeanName" value="channelSqlSessionFactory"/>
    </bean>
    <!--alertLog datasource-->
    <bean id="alertLogDataSource" class="com.alibaba.druid.pool.DruidDataSource" parent="baseDruidDataSource">
        <property name="name" value="alertLogDataSource" />
        <property name="username" value="${alertlog.datasource.username}" />
        <property name="password" value="${alertlog.datasource.pwd}" />
        <property name="url" value="${alertlog.datasource.url}" />
    </bean>
    <bean id="alertLogSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="alertLogDataSource"/>
        <property name="configLocation" value="classpath:mybatis-config.xml"/>
        <property name="mapperLocations">
            <list>
                <value>classpath*:mapper/alertLog/*.xml</value>
            </list>
        </property>
        <property name="typeAliasesPackage" value="com.youxin.risk.commons.model"/>
        <!-- 分页插件 -->
        <property name="plugins">
            <array>
                <bean class="com.github.pagehelper.PageHelper">
                    <property name="properties">
                        <value>
                            dialect=mysql
                        </value>
                    </property>
                </bean>
            </array>
        </property>
    </bean>
    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.youxin.risk.admin.dao.alertLog"/>
        <property name="sqlSessionFactoryBeanName" value="alertLogSqlSessionFactory"/>
    </bean>

    <!-- gateway datasource -->
    <bean id="gatewayDataSource" class="com.alibaba.druid.pool.DruidDataSource" parent="baseDruidDataSource">
        <property name="name" value="gatewayDataSource" />
        <property name="username" value="${gw.datasource.username}" />
        <property name="password" value="${gw.datasource.pwd}" />
        <property name="url" value="${gw.datasource.url}" />
    </bean>
    <bean id="gwSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="gatewayDataSource"/>
        <property name="configLocation" value="classpath:mybatis-config.xml"/>
        <property name="mapperLocations">
            <list>
                <value>classpath*:mapper/gateway/*.xml</value>
            </list>
        </property>
        <property name="typeAliasesPackage" value="com.youxin.risk.commons.model"/>
    </bean>
    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.youxin.risk.admin.dao.gateway"/>
        <property name="sqlSessionFactoryBeanName" value="gwSqlSessionFactory"/>
    </bean>

    <!-- engine datasource -->
    <bean id="engineDataSource" class="com.alibaba.druid.pool.DruidDataSource" parent="baseEngineDruidDataSource">
        <property name="name" value="engineDataSource" />
        <property name="username" value="${engine.datasource.username}" />
        <property name="password" value="${engine.datasource.pwd}" />
        <property name="url" value="${engine.datasource.url}" />
    </bean>
    <bean id="engineSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="engineDataSource"/>
        <property name="configLocation" value="classpath:mybatis-config.xml"/>
        <property name="mapperLocations">
            <list>
                <value>classpath*:mapper/engine/*.xml</value>
            </list>
        </property>
        <property name="typeAliasesPackage" value="com.youxin.risk.commons.model"/>
    </bean>
    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.youxin.risk.admin.dao.engine"/>
        <property name="sqlSessionFactoryBeanName" value="engineSqlSessionFactory"/>
    </bean>

    <!-- rm datasource -->
    <bean id="rmDataSource" class="com.alibaba.druid.pool.DruidDataSource" parent="baseDruidDataSource">
        <property name="name" value="rmDataSource" />
        <property name="username" value="${rm.datasource.username}" />
        <property name="password" value="${rm.datasource.pwd}" />
        <property name="url" value="${rm.datasource.url}" />
    </bean>
    <bean id="rmSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="rmDataSource"/>
        <property name="configLocation" value="classpath:mybatis-config.xml"/>
        <property name="mapperLocations">
            <list>
                <value>classpath*:mapper/rm/*.xml</value>
            </list>
        </property>
        <property name="typeAliasesPackage" value="com.youxin.risk.commons.model"/>
        <!-- 分页插件 -->
        <property name="plugins">
            <array>
                <bean class="com.github.pagehelper.PageHelper">
                    <property name="properties">
                        <value>
                            dialect=mysql
                        </value>
                    </property>
                </bean>
            </array>
        </property>
    </bean>
    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.youxin.risk.admin.dao.rm"/>
        <property name="sqlSessionFactoryBeanName" value="rmSqlSessionFactory"/>
    </bean>

    <!--rm-common datasource-->
    <bean id="rmCommonDataSource" class="com.alibaba.druid.pool.DruidDataSource" parent="baseDruidDataSource">
        <property name="name" value="rmDataSource" />
        <property name="username" value="${rm.datasource.username}" />
        <property name="password" value="${rm.datasource.pwd}" />
        <property name="url" value="${rm.datasource.url}" />
    </bean>
    <bean id="rmCommonSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="rmCommonDataSource"/>
        <property name="configLocation" value="classpath:mybatis-config.xml"/>
        <property name="mapperLocations">
            <list>
                <value>classpath*:mapper/fs/*.xml</value>
            </list>
        </property>
        <property name="typeAliasesPackage" value="com.youxin.risk.commons.model"/>
    </bean>

<!--    <bean id="rmFeatureMapper" class="org.mybatis.spring.mapper.MapperFactoryBean">-->
<!--        <property name="mapperInterface" value="com.youxin.risk.commons.dao.fs.RmFeatureMapper" />-->
<!--        <property name="sqlSessionFactory" ref="rmCommonSqlSessionFactory" />-->
<!--    </bean>-->
    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.youxin.risk.commons.dao.fs"/>
        <property name="sqlSessionFactoryBeanName" value="rmCommonSqlSessionFactory"/>
    </bean>

    <!-- admin事务 -->
    <bean id="adminTransactionManager"
          class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="adminDataSource"></property>
    </bean>
    <!-- rm事务 -->
    <bean id="rmTransactionManager"
          class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="rmDataSource"></property>
    </bean>


    <!-- cp datasource -->
    <bean id="cpDataSource" class="com.alibaba.druid.pool.DruidDataSource" parent="baseDruidDataSource">
        <property name="name" value="cpDataSource" />
        <property name="username" value="${cp.datasource.username}" />
        <property name="password" value="${cp.datasource.pwd}" />
        <property name="url" value="${cp.datasource.url}" />
    </bean>
    <bean id="cpSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="cpDataSource"/>
        <property name="configLocation" value="classpath:mybatis-config.xml"/>
        <property name="mapperLocations">
            <list>
                <value>classpath*:mapper/cp/*.xml</value>
            </list>
        </property>
        <property name="typeAliasesPackage" value="com.youxin.risk.commons.model"/>
    </bean>
    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.youxin.risk.admin.dao.cp"/>
        <property name="sqlSessionFactoryBeanName" value="cpSqlSessionFactory"/>
    </bean>

    <!-- 配置哪些方法要加入事务控制 -->
    <tx:advice id="txAdvice" transaction-manager="adminTransactionManager">
        <tx:attributes>
            <tx:method name="insert*" propagation="REQUIRED"  rollback-for="java.lang.Exception"/>
            <tx:method name="del*" propagation="REQUIRED" />
            <tx:method name="update*" propagation="REQUIRED"  rollback-for="java.lang.Exception"/>
            <tx:method name="modify*" propagation="REQUIRED" rollback-for="java.lang.Exception"/>
            <tx:method name="save*" propagation="REQUIRED" rollback-for="java.lang.Exception"/>
            <tx:method name="add*" propagation="REQUIRED"  rollback-for="java.lang.Exception"/>
            <tx:method name="remove*" propagation="REQUIRED"  rollback-for="java.lang.Exception"/>
            <tx:method name="submit*" propagation="REQUIRED" rollback-for="java.lang.Exception"/>
            <tx:method name="find*" propagation="REQUIRED" read-only="true" />
            <tx:method name="query*"  propagation="REQUIRED" read-only="true" />
            <tx:method name="get*"  propagation="REQUIRED" read-only="true" />
        </tx:attributes>
    </tx:advice>

    <aop:config>
        <aop:pointcut id="allMethods" expression="execution(* com.youxin.risk.*..service*..*(..))" />
        <aop:advisor advice-ref="txAdvice" pointcut-ref="allMethods" />
    </aop:config>

    <tx:annotation-driven/>

</beans>
