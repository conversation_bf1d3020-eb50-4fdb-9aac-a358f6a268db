<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youxin.risk.admin.dao.admin.AdminEventInfoMapper">
    <resultMap id="BaseResultMap" type="com.youxin.risk.admin.model.AdminEventInfo">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="event_code" property="eventCode" jdbcType="VARCHAR"/>
        <result column="event_name" property="eventName" jdbcType="VARCHAR"/>
        <result column="event_type" property="eventType" jdbcType="VARCHAR"/>
        <result column="process_def_id" property="processDefId" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="source_system" property="sourceSystem" jdbcType="VARCHAR" />
        <result column="version" property="version" jdbcType="INTEGER" />
        <result column="referrence_id" property="referrenceId" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="InputResultMap" type="com.youxin.risk.admin.model.AdminEventInput">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="event_code" property="eventCode" jdbcType="VARCHAR"/>
        <result column="variable_code" property="variableCode" jdbcType="VARCHAR"/>
        <result column="is_trans" property="isTrans" jdbcType="TINYINT"/>
        <result column="is_required" property="isRequired" jdbcType="TINYINT"/>
        <result column="trans_code" property="transCode" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="OutputResultMap" type="com.youxin.risk.admin.model.AdminEventOutput">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="event_code" property="eventCode" jdbcType="VARCHAR"/>
        <result column="variable_code" property="variableCode" jdbcType="VARCHAR"/>
        <result column="is_trans" property="isTrans" jdbcType="TINYINT"/>
        <result column="is_required" property="isRequired" jdbcType="TINYINT"/>
        <result column="trans_code" property="transCode" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="EventProcessInfoMap" type="com.youxin.risk.admin.model.AdminEventProcessInfo">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="event_code" property="eventCode" jdbcType="VARCHAR"/>
        <result column="process_def_id" property="processDefId" jdbcType="VARCHAR"/>
        <result column="process_content" property="processContent" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
    id, event_code, event_name, event_type, process_def_id, create_time, update_time, source_system,version,referrence_id
  </sql>

    <sql id="Input_Column_List">
    id, event_code, variable_code, is_trans, is_required, trans_code
  </sql>

    <sql id="Output_Column_List">
    id, event_code, variable_code, is_trans, is_required, trans_code
  </sql>

    <insert id="insert" parameterType="com.youxin.risk.admin.model.AdminEventInfo">
    insert into admin_event_info (
      event_code,
      event_name,
      event_type,
      process_def_id,
      source_system,
      version,
      referrence_id
    ) values (
      #{eventCode},
      #{eventName},
      #{eventType},
      #{processDefId},
      #{sourceSystem},
      #{version},
      #{referrenceId}
    )
  </insert>

    <update id="update" parameterType="com.youxin.risk.admin.model.AdminEventInfo">
        update admin_event_info
        <set>
            <if test="eventType != null">
                event_type = #{eventType},
            </if>
            <if test="eventName != null">
                event_name = #{eventName},
            </if>
            <if test="processDefId != null">
                process_def_id = #{processDefId},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="sourceSystem != null">
                source_system = #{sourceSystem},
            </if>
            <if test="version != null">
                version = #{version},
            </if>
            <if test="referrenceId != null">
                referrence_id = #{referrenceId},
            </if>
        </set>
        where id = #{id}
    </update>

    <delete id="delete" parameterType="java.lang.Long">
        delete from admin_event_info
        where id = #{id}
    </delete>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from admin_event_info
        where id = #{id}
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from admin_event_info
        order by id desc
    </select>

    <select id="selectCount" resultType="java.lang.Integer" parameterType="java.util.Map">
        select
        count(1)
        from admin_event_info
        <where>
            <if test="eventCode != null">
                and event_code like CONCAT('%',#{eventCode},'%')
            </if>
            <if test="eventName != null">
                and event_name like CONCAT('%',#{eventName},'%')
            </if>
            <if test="eventType != null">
                and event_type = #{eventType}
            </if>
            <if test="sourceSystem != null">
                and source_system = #{sourceSystem}
            </if>
        </where>
    </select>

    <select id="selectList" resultMap="BaseResultMap" parameterType="java.util.Map">
        select
        <include refid="Base_Column_List"/>
        from admin_event_info
        <where>
            <if test="eventCode != null">
                and event_code like CONCAT('%',#{eventCode},'%')
            </if>
            <if test="eventName != null">
                and event_name like CONCAT('%',#{eventName},'%')
            </if>
            <if test="eventType != null">
                and event_type = #{eventType}
            </if>
            <if test="sourceSystem != null">
                and source_system = #{sourceSystem}
            </if>
        </where>
        order by id desc
        limit #{start}, #{limit}
    </select>

    <delete id="deleteInputs" parameterType="java.lang.String">
        delete from admin_event_input
        where event_code=#{eventCode}
    </delete>

    <delete id="deleteOutputs" parameterType="java.lang.String">
        delete from admin_event_output
        where event_code=#{eventCode}
    </delete>

    <insert id="insertInputs">
        insert into admin_event_input(
        event_code,
        variable_code,
        is_trans,
        is_required,
        trans_code
        ) values
        <foreach collection="list" item="item" index="index" open="(" separator="),(" close=")">
            #{item.eventCode},
            #{item.variableCode},
            #{item.isTrans},
            #{item.isRequired},
            #{item.transCode}
        </foreach>
    </insert>

    <insert id="insertOutputs">
        insert into admin_event_output(
        event_code,
        variable_code,
        is_trans,
        is_required,
        trans_code
        ) values
        <foreach collection="list" item="item" index="index" open="(" separator="),(" close=")">
            #{item.eventCode},
            #{item.variableCode},
            #{item.isTrans},
            #{item.isRequired},
            #{item.transCode}
        </foreach>
    </insert>
    
    <select id="selectInputs" resultMap="InputResultMap" parameterType="java.lang.String">
        select
        <include refid="Input_Column_List"/>
        from admin_event_input
        where event_code=#{eventCode}
    </select>

    <select id="selectOutputs" resultMap="OutputResultMap" parameterType="java.lang.String">
        select
        <include refid="Output_Column_List"/>
        from admin_event_output
        where event_code=#{eventCode}
    </select>

    <select id="selectInputsByVariableCode" resultMap="InputResultMap" parameterType="java.lang.String">
        select
        <include refid="Input_Column_List"/>
        from admin_event_input
        where variable_code=#{variableCode}
    </select>

    <select id="selectOutputsByVariableCode" resultMap="OutputResultMap" parameterType="java.lang.String">
        select
        <include refid="Output_Column_List"/>
        from admin_event_output
        where variable_code=#{variableCode}
    </select>

    <select id="selectByProcessDefId" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from admin_event_info
        where process_def_id = #{processDefId}
    </select>
    <update id="updateByProcessDefId">
        update admin_event_info
        set process_def_id = #{newProcessDefId}
        where process_def_id = #{oldProcessDefId}
    </update>

    <update id="updateByReferrenceId">
        update admin_event_info
        set process_def_id = #{processDefId}, version = #{version}
        where referrence_id = #{referrenceId}
    </update>

    <select id="selectEventInfoList" resultType="java.util.Map">
        SELECT event_code eventCode ,event_name eventName
        from admin_event_info
        ORDER BY id DESC
    </select>

    <select id="selectEventProcessInfo" resultMap="EventProcessInfoMap" >
        SELECT a.id,a.event_code,b.process_def_id,b.process_content
        from admin_event_info a
        LEFT JOIN admin_process_definition b on a.process_def_id = b.process_def_id
        <where>
            <if test="eventCodes != null">
                and a.event_code in
                <foreach collection="eventCodes" item="eventCode" index="index" open="(" close=")" separator=",">
                    #{eventCode}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectProcessDefIdList" resultType="java.lang.String">
        SELECT referrence_id
        from admin_event_info
        ORDER BY id DESC
    </select>
    <select id="selectByEventCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from admin_event_info
        where event_code = #{eventCode}
    </select>
</mapper>