<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youxin.risk.admin.dao.admin.AdminSystemLogMapper">
  <resultMap id="BaseResultMap" type="com.youxin.risk.admin.model.AdminSystemLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="username" jdbcType="VARCHAR" property="username" />
    <result column="uri" jdbcType="VARCHAR" property="uri" />
    <result column="params" jdbcType="VARCHAR" property="params" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, username, uri, params, create_time, update_time
  </sql>
  <insert id="insert" parameterType="com.youxin.risk.admin.model.AdminSystemLog">
    insert into admin_system_log (
      username,
      uri,
      params
    ) values (
      #{username},
      #{uri},
      #{params}
    )
  </insert>

  <insert id="insertReturnId" useGeneratedKeys="true" keyProperty="id" parameterType="com.youxin.risk.admin.model.AdminSystemLog" >
    insert into admin_system_log (
    username,
    uri,
    params
    ) values (
    #{username},
    #{uri},
    #{params}
    )
  </insert>

  <select id="selectById" resultMap="BaseResultMap" parameterType="java.lang.Long">
    select
    <include refid="Base_Column_List"/>
    from admin_system_log
    where id = #{id}
  </select>

  <select id="selectByUri" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from admin_system_log
    where uri = #{uri} and params like CONCAT('%',#{keyWord},'%')
    order by id desc limit 1
  </select>

  <update id="updateParams"  parameterType="com.youxin.risk.admin.model.AdminSystemLog" >
    update admin_system_log set params = #{params} where id = #{id}
  </update>

</mapper>