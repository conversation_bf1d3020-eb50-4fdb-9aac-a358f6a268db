<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.admin.dao.engine.EngineEventHistoryMapper" >

    <resultMap id="BaseResultMap" type="com.youxin.risk.admin.model.EngineEvent" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="session_id" property="sessionId" jdbcType="VARCHAR" />
        <result column="source_system" property="sourceSystem" jdbcType="VARCHAR" />
        <result column="user_key" property="userKey" jdbcType="VARCHAR" />
        <result column="loan_key" property="loanKey" jdbcType="VARCHAR" />
        <result column="event_code" property="eventCode" jdbcType="VARCHAR" />
        <result column="event_type" property="eventType" jdbcType="VARCHAR" />
        <result column="process_def_id" property="processDefId" jdbcType="VARCHAR" />
        <result column="process_instance_id" property="processInstanceId" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <resultMap id="BlockEventResultMap" type="com.youxin.risk.admin.model.BlockedEngineEvent" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="session_id" property="sessionId" jdbcType="VARCHAR" />
        <result column="user_key" property="userKey" jdbcType="VARCHAR" />
        <result column="loan_key" property="loanKey" jdbcType="VARCHAR" />
        <result column="event_code" property="eventCode" jdbcType="VARCHAR" />
        <result column="process_def_id" property="processDefId" jdbcType="VARCHAR" />
        <result column="process_instance_id" property="processInstanceId" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="VARCHAR" />
        <result column="current_node_id" property="currentNodeId" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <resultMap id="LogResultMap" type="com.youxin.risk.admin.model.EngineAsyncRequestLog">
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="async_request_id" property="asyncRequestId" jdbcType="VARCHAR" />
        <result column="session_id" property="sessionId" jdbcType="VARCHAR" />
        <result column="source_system" property="sourceSystem" jdbcType="VARCHAR" />
        <result column="user_key" property="userKey" jdbcType="VARCHAR" />
        <result column="loan_key" property="loanKey" jdbcType="VARCHAR" />
        <result column="data_code" property="dataCode" jdbcType="VARCHAR" />
        <result column="job_id" property="jobId" jdbcType="VARCHAR" />
        <result column="node_id" property="nodeId" jdbcType="VARCHAR" />
        <result column="type" property="type" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="table_name">
      engine_event_history
    </sql>

    <sql id="Base_Column_List" >
        id, session_id, source_system, user_key, loan_key, event_code, event_type, process_def_id, 
        process_instance_id, status, create_time, update_time
    </sql>

    <sql id="log_Column_List" >
        id, async_request_id, session_id, source_system, user_key, loan_key, data_code,
        job_id, node_id, type, status, create_time, update_time
    </sql>


    <delete id="deleteBySessionIds">
        delete from <include refid="table_name"/> where session_id in
        <foreach collection="sessionIdList" item="sessionId" index="index" open="(" separator="," close=")">
            #{sessionId}
        </foreach>
    </delete>


</mapper>