<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youxin.risk.admin.dao.di.AdminDiTaskMapper">
  <resultMap id="BaseResultMap" type="com.youxin.risk.admin.model.AdminDiTask">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="source_system" jdbcType="VARCHAR" property="sourceSystem" />
    <result column="service_code" jdbcType="VARCHAR" property="serviceCode" />
    <result column="request_id" jdbcType="VARCHAR" property="requestId" />
    <result column="user_key" jdbcType="VARCHAR" property="userKey" />
    <result column="loan_key" jdbcType="VARCHAR" property="loanKey" />
    <result column="task_type" jdbcType="VARCHAR" property="taskType" />
    <result column="job_id" jdbcType="VARCHAR" property="jobId" />
    <result column="task_mode" jdbcType="VARCHAR" property="taskMode" />
    <result column="task_status" jdbcType="VARCHAR" property="taskStatus" />
    <result column="stop_reason" jdbcType="VARCHAR" property="stopReason" />
    <result column="expire_time" jdbcType="TIMESTAMP" property="expireTime" />
    <result column="fetch_time" jdbcType="TIMESTAMP" property="fetchTime" />
    <result column="err_count" jdbcType="INTEGER" property="errCount" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="version" jdbcType="INTEGER" property="version" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.youxin.risk.admin.model.AdminDiTask">
    <result column="request_body" jdbcType="LONGVARCHAR" property="requestBody" />
  </resultMap>
  <sql id="Base_Column_List">
    id, source_system, service_code, request_id, user_key, loan_key, task_type, job_id, 
    task_mode, task_status, stop_reason, expire_time, fetch_time, err_count, create_time, 
    update_time, version
  </sql>
  <sql id="Blob_Column_List">
    request_body
  </sql>
  
  <!-- 自定义查询 -->
  <select id="selectAllTaskTotal" parameterType="com.youxin.risk.admin.vo.AdminDiTaskVo"  resultType="int">
    select 
    count(1)
    from di_task
    <where>
    <if test="serviceCode != null" >
       and service_code = #{serviceCode}
    </if>
    <if test="taskType != null" >
       and task_type = #{taskType}
    </if>
    <if test="taskMode != null" >
       and task_mode = #{taskMode}
    </if>
    <if test="jobId != null" >
       and job_id = #{jobId}
    </if>
    <if test="taskStatus != null" >
       and task_status = #{taskStatus}
    </if>
    <if test="requestId != null" >
       and request_id = #{requestId}
    </if>
    <if test="startTime != null and endTime != null" >
       and create_time &gt; #{startTime} and create_time &lt; #{endTime}
    </if>
    </where>
  </select>

  <select id="selectAllTask" parameterType="com.youxin.risk.admin.vo.AdminDiTaskVo"  resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from di_task
    <where>
    <if test="serviceCode != null" >
       and service_code = #{serviceCode}
    </if>
    <if test="taskType != null" >
       and task_type = #{taskType}
    </if>
    <if test="taskMode != null" >
       and task_mode = #{taskMode}
    </if>
    <if test="jobId != null" >
       and job_id = #{jobId}
    </if>
    <if test="taskStatus != null" >
       and task_status = #{taskStatus}
    </if>
    <if test="requestId != null" >
       and request_id = #{requestId}
    </if>
    <if test="startTime != null and endTime != null" >
       and create_time &gt; #{startTime} and create_time &lt; #{endTime}
    </if>
    </where>
    order by id desc
    limit ${startIndex},${pageSize}
  </select>
  
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from di_task
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from di_task
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insertSelective" parameterType="com.youxin.risk.admin.model.AdminDiTask">
    insert into di_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="sourceSystem != null">
        source_system,
      </if>
      <if test="serviceCode != null">
        service_code,
      </if>
      <if test="requestId != null">
        request_id,
      </if>
      <if test="userKey != null">
        user_key,
      </if>
      <if test="loanKey != null">
        loan_key,
      </if>
      <if test="taskType != null">
        task_type,
      </if>
      <if test="jobId != null">
        job_id,
      </if>
      <if test="taskMode != null">
        task_mode,
      </if>
      <if test="taskStatus != null">
        task_status,
      </if>
      <if test="stopReason != null">
        stop_reason,
      </if>
      <if test="expireTime != null">
        expire_time,
      </if>
      <if test="fetchTime != null">
        fetch_time,
      </if>
      <if test="errCount != null">
        err_count,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="requestBody != null">
        request_body,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="sourceSystem != null">
        #{sourceSystem,jdbcType=VARCHAR},
      </if>
      <if test="serviceCode != null">
        #{serviceCode,jdbcType=VARCHAR},
      </if>
      <if test="requestId != null">
        #{requestId,jdbcType=VARCHAR},
      </if>
      <if test="userKey != null">
        #{userKey,jdbcType=VARCHAR},
      </if>
      <if test="loanKey != null">
        #{loanKey,jdbcType=VARCHAR},
      </if>
      <if test="taskType != null">
        #{taskType,jdbcType=VARCHAR},
      </if>
      <if test="jobId != null">
        #{jobId,jdbcType=VARCHAR},
      </if>
      <if test="taskMode != null">
        #{taskMode,jdbcType=VARCHAR},
      </if>
      <if test="taskStatus != null">
        #{taskStatus,jdbcType=VARCHAR},
      </if>
      <if test="stopReason != null">
        #{stopReason,jdbcType=VARCHAR},
      </if>
      <if test="expireTime != null">
        #{expireTime,jdbcType=TIMESTAMP},
      </if>
      <if test="fetchTime != null">
        #{fetchTime,jdbcType=TIMESTAMP},
      </if>
      <if test="errCount != null">
        #{errCount,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="requestBody != null">
        #{requestBody,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.youxin.risk.admin.model.AdminDiTask">
    update di_task
    <set>
      <if test="sourceSystem != null">
        source_system = #{sourceSystem,jdbcType=VARCHAR},
      </if>
      <if test="serviceCode != null">
        service_code = #{serviceCode,jdbcType=VARCHAR},
      </if>
      <if test="requestId != null">
        request_id = #{requestId,jdbcType=VARCHAR},
      </if>
      <if test="userKey != null">
        user_key = #{userKey,jdbcType=VARCHAR},
      </if>
      <if test="loanKey != null">
        loan_key = #{loanKey,jdbcType=VARCHAR},
      </if>
      <if test="taskType != null">
        task_type = #{taskType,jdbcType=VARCHAR},
      </if>
      <if test="jobId != null">
        job_id = #{jobId,jdbcType=VARCHAR},
      </if>
      <if test="taskMode != null">
        task_mode = #{taskMode,jdbcType=VARCHAR},
      </if>
      <if test="taskStatus != null">
        task_status = #{taskStatus,jdbcType=VARCHAR},
      </if>
      <if test="stopReason != null">
        stop_reason = #{stopReason,jdbcType=VARCHAR},
      </if>
      <if test="expireTime != null">
        expire_time = #{expireTime,jdbcType=TIMESTAMP},
      </if>
      <if test="fetchTime != null">
        fetch_time = #{fetchTime,jdbcType=TIMESTAMP},
      </if>
      <if test="errCount != null">
        err_count = #{errCount,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="requestBody != null">
        request_body = #{requestBody,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateStatusByRequestIdList">
    update di_task
    set task_status=#{status}
    where request_id in
    <foreach collection="requestIdList" index="index" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </update>

  <select id="selectByRequestIdList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from di_task
    where request_id in
    <foreach collection="requestIdList" index="index" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>
  <select id="selectByRequestIds" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from di_task
    where request_id in
       <foreach collection="requestIds" item="item" open="(" close=")" separator=",">
         #{item}
       </foreach>
      and task_status in ('INIT', 'SUBMITTED')
      and create_time > DATE_ADD(NOW(), INTERVAL - 24 HOUR)

  </select>
</mapper>