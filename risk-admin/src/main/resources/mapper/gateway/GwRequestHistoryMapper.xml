<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.admin.dao.gateway.GwRequestHistoryMapper">

    <resultMap id="BaseResultMap" type="com.youxin.risk.admin.model.GwRequest">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="request_id" property="requestId" jdbcType="VARCHAR"/>
        <result column="icode" property="icode" jdbcType="VARCHAR"/>
        <result column="session_id" property="sessionId" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="request_message" property="requestMessage" jdbcType="LONGVARCHAR"/>
        <result column="callback_message" property="callbackMessage" jdbcType="LONGVARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="is_lock" property="isLock" jdbcType="TINYINT"/>
        <result column="err_count" property="errCount" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <resultMap id="GwResultMap" type="com.youxin.risk.admin.model.vo.GwRequestVo">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="request_id" property="requestId" jdbcType="VARCHAR"/>
        <result column="icode" property="icode" jdbcType="VARCHAR"/>
        <result column="session_id" property="sessionId" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="event_code" property="eventCode" jdbcType="LONGVARCHAR"/>
        <result column="err_count" property="errCount" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <resultMap id="GwRetryRequestMap" type="com.youxin.risk.admin.model.GwRetryRequestBody">
        <result column="session_id" property="sessionId" jdbcType="VARCHAR"/>
        <result column="request_body" property="requestBody" jdbcType="LONGVARCHAR"/>
    </resultMap>

    <sql id="table_name">
      gw_request_history
    </sql>

    <sql id="Base_Column_List">
        id, request_id, icode, session_id, status, request_message, callback_message, remark, 
        is_lock, err_count, create_time, update_time
    </sql>



    <delete id="deleteBySessionIds" parameterType="list">
        delete from <include refid="table_name"/> where session_id in
        <foreach collection="sessionIdList" item="sessionId" index="index" open="(" separator="," close=")">
            #{sessionId}
        </foreach>
    </delete>



</mapper>