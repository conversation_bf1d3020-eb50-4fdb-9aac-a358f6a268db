package com.youxin.risk.admin.service.notifier;

import com.youxin.risk.admin.service.consume.VariableListener;
import com.youxin.risk.commons.observable.AbstractNotifier;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Component
public class DeploySuccessNotifier extends AbstractNotifier {

    @Autowired
    private VariableListener variableListener;

    public DeploySuccessNotifier() {
        super("DeploySuccessNotifier");
    }

    @PostConstruct
    public void init(){
        addListener(variableListener);
    }
}
