package com.youxin.risk.admin.interceptor;

import com.alibaba.fastjson.JSON;
import com.youxin.risk.admin.model.auth.Role;
import com.youxin.risk.admin.service.PermissionRequestService;
import com.youxin.risk.admin.utils.UserInfoUtil;
import com.youxin.risk.commons.utils.HttpResponseUtil;
import com.youxin.risk.commons.utils.JsonUtils;
import com.youxin.risk.commons.utils.StringUtils;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/9/17 20:15
 * @desc
 */
public class AdminUrlInterceptor extends HandlerInterceptorAdapter {

    private static Logger logger = LoggerFactory.getLogger(AdminUrlInterceptor.class);

    private static final String ROLE_ADMIN = "admin";

    private static final String VARIABLE_TOKEN = "******************************111111";

    @Autowired
    private PermissionRequestService permissionRequestService;


    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String username = UserInfoUtil.getUsername();
        if (StringUtils.isEmpty(username)) {
            return super.preHandle(request, response, handler);
        }

        // 变量中心过来的，直接放过
        String variableToken = request.getHeader("variableToken");
        if (VARIABLE_TOKEN.equals(variableToken)) {
            return super.preHandle(request, response, handler);
        }

        List<Role> userRoles = permissionRequestService.getRolesByCache(username);
        logger.info("URL拦截器：用户={},角色集合={}", username, JSON.toJSONString(userRoles));
        //超级管理员 直接放过
        if (CollectionUtils.isNotEmpty(userRoles)) {
            for (Role userRole : userRoles) {
                if (ROLE_ADMIN.equals(userRole.getSign())) {
                    return super.preHandle(request, response, handler);
                }
            }
        }

        List<String> urlList = permissionRequestService.getUrlByCache(username);
        if (!urlList.contains(request.getRequestURI())) {
            logger.error("无权访问，请联系值班人员, 解决方法：在auth服务配置该资源={},以及确保用户{}对应的角色拥有该资源", request.getRequestURI(), username);
            dealResponse(response);
            return false;
        }

        return super.preHandle(request, response, handler);
    }

    private void dealResponse(HttpServletResponse response) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("status", 403);
        resultMap.put("message", "无权访问，请联系值班人员！");
        HttpResponseUtil.renderJson(response, JsonUtils.toJson(resultMap));
    }
}
