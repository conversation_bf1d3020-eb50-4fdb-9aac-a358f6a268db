package com.youxin.risk.admin.scheduler.xxljob;

import com.alibaba.fastjson.JSON;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.youxin.risk.admin.dao.admin.RuleCandidateScoreMapper;
import com.youxin.risk.admin.model.rulescore.RuleCandidateScore;
import com.youxin.risk.admin.service.rulescore.IntegrationService;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.xxl.job.XxlJobBase;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;

import static com.youxin.risk.admin.model.rulescore.RuleCandidateScore.ruleStatusFail;


/**
 * 集成测试定时任务
 */
@Component
public class RuleScoreJob implements XxlJobBase {

    private static final Logger LOGGER = LoggerFactory.getLogger(RuleScoreJob.class);

    @Autowired
    private RuleCandidateScoreMapper ruleCandidateScoreMapper;
    @Autowired
    private IntegrationService integrationService;

    @Override
    @XxlJob(value = "RuleScoreJob")
    public ReturnT<String> execJobHandler(String param) {
        List<RuleCandidateScore> ruleCandidateScoreList = ruleCandidateScoreMapper.findRuleCandidateScoreRunningTask();
        LoggerProxy.info("ruleScoreJob start",LOGGER, "ruleCandidateScoreList={} minutes ={}", JSON.toJSONString(ruleCandidateScoreList));

        for(RuleCandidateScore ruleCandidateScore : ruleCandidateScoreList) {
            /** 集成测试发起超过2分钟,自动失败**/
            Date date = new Date();
            Date workStartTime = ruleCandidateScore.getWorkStartTime();
            String ruleKey = ruleCandidateScore.getRuleKey();
            Integer ruleVersion = ruleCandidateScore.getRuleVersion();
            String modifyUser = ruleCandidateScore.getModifyUser();

            Long minutes = ChronoUnit.MINUTES.between(workStartTime.toInstant(), date.toInstant());
            LoggerProxy.info("ruleScoreJob",LOGGER, "ruleCandidateScore={} minutes ={}", JSON.toJSONString(ruleCandidateScore),minutes);
            if (minutes > 10){
                ruleCandidateScoreMapper.updateStatus(ruleKey,ruleVersion,ruleStatusFail,new Date(),modifyUser);
            }
            integrationService.updateIntegrationWork(ruleKey, ruleVersion,null,ruleCandidateScore.getTaskId(), modifyUser);
        }
        return ReturnT.SUCCESS;
    }
}
