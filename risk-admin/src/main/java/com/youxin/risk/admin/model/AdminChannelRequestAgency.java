package com.youxin.risk.admin.model;

import com.youxin.risk.commons.model.BaseModel;

public class AdminChannelRequestAgency extends BaseModel {
    /**
     * 
     */
    private static final long serialVersionUID = -2900427920595390301L;

    private String requestId;

    private String requestAgencyId;

    private String outRequestId;

    private String agencyCode;

    private String dpJodid;

    private String requestAgencyMessage;

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId == null ? null : requestId.trim();
    }

    public String getRequestAgencyId() {
        return requestAgencyId;
    }

    public void setRequestAgencyId(String requestAgencyId) {
        this.requestAgencyId = requestAgencyId;
    }

    public String getOutRequestId() {
        return outRequestId;
    }

    public void setOutRequestId(String outRequestId) {
        this.outRequestId = outRequestId;
    }

    public String getAgencyCode() {
        return agencyCode;
    }

    public void setAgencyCode(String agencyCode) {
        this.agencyCode = agencyCode;
    }

    public String getDpJodid() {
        return dpJodid;
    }

    public void setDpJodid(String dpJodid) {
        this.dpJodid = dpJodid;
    }

    public String getRequestAgencyMessage() {
        return requestAgencyMessage;
    }

    public void setRequestAgencyMessage(String requestAgencyMessage) {
        this.requestAgencyMessage = requestAgencyMessage;
    }

}
