package com.youxin.risk.admin.service;

import java.util.List;
import java.util.Map;

import com.youxin.risk.admin.model.AdminDiVariable;
import com.youxin.risk.admin.vo.AdminDiVariableVo;

/**
 * 
 * <AUTHOR>
 *
 */
public interface AdminDiVariableService {

    List<AdminDiVariable> queryVariableList(String keyword);

    List<AdminDiVariable> queryVariableList(AdminDiVariableVo request);

    Map<String, Object> queryAllVariable();

    AdminDiVariable queryDiVariableDetail(AdminDiVariable adminDiVariable);

    int modifyDiVariable(AdminDiVariable adminDiVariable);

    int addDiVariable(AdminDiVariable request);

    int removeDiVariable(Long id);

    int queryAllVariableTotal(AdminDiVariableVo request);

}
