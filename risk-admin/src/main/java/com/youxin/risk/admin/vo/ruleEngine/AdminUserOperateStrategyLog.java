package com.youxin.risk.admin.vo.ruleEngine;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AdminUserOperateStrategyLog {

	private Integer id;

    private Integer integrationId;

    private String userName;

    private String operateType;

    private String operateContent;

    private Date createTime;
}