package com.youxin.risk.admin.service.ruleEngine.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.youxin.apollo.client.NacosClient;
import com.youxin.risk.admin.constants.ApolloLocalNamespace;
import com.youxin.risk.admin.constants.CmpIntegrationStatusEnum;
import com.youxin.risk.admin.dao.cp.SystemParameterMapper;
import com.youxin.risk.admin.dao.rm.AdminCandidateStrategyMapper;
import com.youxin.risk.admin.dao.admin.StrategyCmpMapper;
import com.youxin.risk.admin.dao.rm.AdminStrategyCodeMapper;
import com.youxin.risk.admin.domain.ruleengine.AdminCandidateStrategy;
import com.youxin.risk.admin.domain.ruleengine.StrategyCmpExample;
import com.youxin.risk.admin.dto.PageInfo;
import com.youxin.risk.admin.model.AdminStrategyCode;
import com.youxin.risk.admin.model.ruleEngine.AdminCandidateStrategyExample;
import com.youxin.risk.admin.model.ruleEngine.StrategyCmpNew;
import com.youxin.risk.admin.service.ruleEngine.AdminCandidateStrategyService;
import com.youxin.risk.admin.service.ruleEngine.StrategyNodeService;
import com.youxin.risk.admin.tools.ruleEngine.DataWarehouseServiceClient;
import com.youxin.risk.admin.tools.ruleEngine.StrategyCodeHandler;
import com.youxin.risk.admin.utils.DateUtils;
import com.youxin.risk.admin.utils.UserInfoUtil;
import com.youxin.risk.admin.vo.ruleEngine.AdminCmpIntegrationSubmitVo;
import com.youxin.risk.admin.vo.ruleEngine.StrategyCmpIntegrationPageQueryVo;
import com.youxin.risk.admin.vo.ruleEngine.VersionInfo;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.nio.charset.StandardCharsets;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class StrategyCmpIntegrationService {

	private static final Logger logger = LoggerFactory.getLogger(StrategyCmpIntegrationService.class);

	private static final int DEFAULT_FETCH_SIZE = 5000;
	private static final int DEFAULT_LIMIT = 50000;
	@Autowired
	private StrategyCmpMapper strategyCmpMapper;
	@Autowired
	private AdminStrategyCodeMapper adminStrategyCodeMapper;
	@Autowired
	private AdminCandidateStrategyMapper adminCandidateStrategyMapper;
	@Resource
	private SystemParameterMapper systemParameterMapper;
	@Resource
	private AdminCandidateStrategyService adminCandidateStrategyService;
	@Resource
	private DataWarehouseServiceClient dataWarehouseServiceClient;
	@Autowired(required = false)
	private JdbcTemplate hiveTemplate;
	@Resource
	private StrategyNodeService strategyNodeService;

	public List<VersionInfo> versionByStrategyType(String strategyType) {
		AdminCandidateStrategyExample example = new AdminCandidateStrategyExample();
		example.createCriteria().andStrategyTypeEqualTo(strategyType);
		example.setOrderByClause("id desc");
		List<AdminCandidateStrategy> adminCandidateStrategies = adminCandidateStrategyMapper.selectByExample(example);
		return adminCandidateStrategies.stream().map(this::convertToVersionInfo).collect(Collectors.toList());
	}

	private VersionInfo convertToVersionInfo(AdminCandidateStrategy adminCandidateStrategy) {
		VersionInfo versionInfo = new VersionInfo();
		versionInfo.setId(adminCandidateStrategy.getId());
		versionInfo.setStatus(adminCandidateStrategy.getStatus());
		versionInfo.setCreateTime(adminCandidateStrategy.getCreateTime());
		return versionInfo;
	}

	public PageInfo<StrategyCmpNew> list(StrategyCmpIntegrationPageQueryVo queryVo) {
		PageHelper.startPage(queryVo.getPageNum(), queryVo.getPageSize(), true);
		StrategyCmpExample ex = new StrategyCmpExample();
		StrategyCmpExample.Criteria criteria = ex.createCriteria();
		buildCriteria(queryVo, criteria);
		ex.setOrderByClause("create_time desc");
		List<StrategyCmpNew> strategyCmpWithBLOBs = strategyCmpMapper.selectByExample(ex);
		return new PageInfo<>(strategyCmpWithBLOBs);
	}

	private void buildCriteria(StrategyCmpIntegrationPageQueryVo queryVo, StrategyCmpExample.Criteria criteria) {
		if (StringUtils.isNotBlank(queryVo.getTitle())) {
			criteria.andTitleLike("%" + queryVo.getTitle() + "%");
		}
		if (StringUtils.isNotBlank(queryVo.getStrategyType())) {
			criteria.andStrategyTypeEqualTo(queryVo.getStrategyType());
		}
		if (StringUtils.isNotBlank(queryVo.getStatus())) {
			criteria.andStatusEqualTo(queryVo.getStatus());
		}
		if (queryVo.getBegin() != null && queryVo.getEnd() != null) {
			criteria.andUpdateTimeBetween(queryVo.getBegin(), queryVo.getEnd());
		}
		if ("me".equals(queryVo.getUserName())) {
			criteria.andUserNameEqualTo(UserInfoUtil.getUsername());
		} else if (StringUtils.isNotBlank(queryVo.getUserName())) {
			criteria.andUserNameEqualTo(queryVo.getUserName());
		}
	}

	public AdminCmpIntegrationSubmitVo detailById(Integer id) {
		StrategyCmpNew strategyCmpWithBLOBs = strategyCmpMapper.selectByPrimaryKey(id);
		return convertToCmpIntegrationSubmitNewVo(strategyCmpWithBLOBs);
	}

	private AdminCmpIntegrationSubmitVo convertToCmpIntegrationSubmitNewVo(StrategyCmpNew strategyCmpWithBLOBs) {
		AdminCmpIntegrationSubmitVo strategyCmpIntegrationNewVo = new AdminCmpIntegrationSubmitVo();
		BeanUtils.copyProperties(strategyCmpWithBLOBs, strategyCmpIntegrationNewVo);
		setSampleSteps(strategyCmpWithBLOBs, strategyCmpIntegrationNewVo);
		setSamplePeriod(strategyCmpWithBLOBs, strategyCmpIntegrationNewVo);
		setStrategyVersionId(strategyCmpWithBLOBs, strategyCmpIntegrationNewVo);
		return strategyCmpIntegrationNewVo;
	}

	private void setSampleSteps(StrategyCmpNew strategyCmpWithBLOBs, AdminCmpIntegrationSubmitVo strategyCmpIntegrationNewVo) {
		String sampleSteps = strategyCmpWithBLOBs.getSampleSteps();
		if (StringUtils.isNotEmpty(sampleSteps)) {
			List<String> sampleStepList = Arrays.stream(sampleSteps.split(","))
					.map(String::valueOf)
					.collect(Collectors.toList());
			strategyCmpIntegrationNewVo.setSampleSteps(sampleStepList);
		}
	}

	private void setSamplePeriod(StrategyCmpNew strategyCmpWithBLOBs, AdminCmpIntegrationSubmitVo strategyCmpIntegrationNewVo) {
		if (strategyCmpWithBLOBs.getSampleStartDate() != null && strategyCmpWithBLOBs.getSampleEndDate() != null) {
			List<Date> samplePeriod = Arrays.asList(strategyCmpWithBLOBs.getSampleStartDate(), strategyCmpWithBLOBs.getSampleEndDate());
			strategyCmpIntegrationNewVo.setSamplePeriod(samplePeriod);
		}
	}

	private void setStrategyVersionId(StrategyCmpNew strategyCmpWithBLOBs, AdminCmpIntegrationSubmitVo strategyCmpIntegrationNewVo) {
		if (StringUtils.isNotEmpty(strategyCmpWithBLOBs.getStrategyVersionId())) {
			strategyCmpIntegrationNewVo
					.setStrategyVersionId(JSON.parseArray(strategyCmpWithBLOBs.getStrategyVersionId(), String.class));
		}
	}

	public JSONObject downloadResultExcel(String tableName) {
		ByteArrayOutputStream os = new ByteArrayOutputStream();
		try (Workbook workbook = new XSSFWorkbook()) {
			Sheet sheet = workbook.createSheet(tableName);
			String sql = "SELECT * FROM " + tableName;
			hiveTemplate.query(
					con -> createPreparedStatement(con, sql), rs -> {
						writeResultSetToExcel(rs, sheet);
					}
			);
			workbook.write(os);
		} catch (Exception e) {
			logger.error("Error downloading result excel", e);
			throw new RuntimeException("Error downloading result excel: " + e.getMessage());
		}

		String base64EncodedCsv = Base64.getEncoder().encodeToString(os.toByteArray());

		JSONObject data = new JSONObject();
		data.put("data", base64EncodedCsv);
		data.put("fileName", String.format("%s.xlsx", tableName));
		return data;
	}

	private PreparedStatement createPreparedStatement(java.sql.Connection con, String sql) throws java.sql.SQLException {
		PreparedStatement preparedStatement = con.prepareStatement(sql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
		preparedStatement.setFetchSize(DEFAULT_FETCH_SIZE);
		preparedStatement.setFetchDirection(ResultSet.FETCH_FORWARD);
		return preparedStatement;
	}

	private void writeResultSetToExcel(ResultSet rs, Sheet sheet) throws java.sql.SQLException {
		ResultSetMetaData metaData = rs.getMetaData();
		writeHeaderToExcel(metaData, sheet);
		writeRowsToExcel(rs, metaData, sheet);
	}

	private void writeHeaderToExcel(ResultSetMetaData metaData, Sheet sheet) throws java.sql.SQLException {
		Row headerRow = sheet.createRow(0);
		for (int i = 1; i <= metaData.getColumnCount(); i++) {
			Cell cell = headerRow.createCell(i - 1);
			cell.setCellValue(metaData.getColumnName(i));
		}
	}

	private void writeRowsToExcel(ResultSet rs, ResultSetMetaData metaData, Sheet sheet) throws java.sql.SQLException {
		int rowCount = 1;
		int columnCount = metaData.getColumnCount();
		while (rs.next()) {
			Row row = sheet.createRow(rowCount++);
			for (int i = 1; i <= columnCount; i++) {
				Cell cell = row.createCell(i - 1);
				cell.setCellValue(rs.getString(i));
			}
		}
	}

	public StrategyCmpNew buildStrategyCmp(AdminCmpIntegrationSubmitVo submitNew) {
		StrategyCmpNew strategyCmp = new StrategyCmpNew();
		strategyCmp.setTitle(submitNew.getTitle());
		strategyCmp.setStrategyVersionId(JSON.toJSONString(submitNew.getStrategyVersionId()));
		strategyCmp.setCreateTime(new Date());
		strategyCmp.setUpdateTime(new Date());
		strategyCmp.setUserName(UserInfoUtil.getUsername());
		setSamplePeriod(submitNew, strategyCmp);
		setSampleSteps(submitNew, strategyCmp);
		strategyCmp.setSampleStrategyTypes(submitNew.getSampleStrategyTypes());
		strategyCmp.setProcess("0");
		strategyCmp.setStatus(CmpIntegrationStatusEnum.RUNNING.getCode());
		strategyCmp.setAdditionKey(submitNew.getAdditionKey());
		strategyCmp.setCompareKey(submitNew.getCompareKey());
		strategyCmp.setCount(submitNew.getCount());
		strategyCmp.setStrategyType(submitNew.getStrategyType());
		return strategyCmp;
	}

	private void setSamplePeriod(AdminCmpIntegrationSubmitVo submitNew, StrategyCmpNew strategyCmp) {
		List<Date> samplePeriod = submitNew.getSamplePeriod();
		if (!CollectionUtils.isEmpty(samplePeriod) && samplePeriod.size() >= 2) {
			strategyCmp.setSampleStartDate(samplePeriod.get(0));
			strategyCmp.setSampleEndDate(samplePeriod.get(1));
		}
	}

	private void setSampleSteps(AdminCmpIntegrationSubmitVo submitNew, StrategyCmpNew strategyCmp) {
		List<String> sampleSteps = submitNew.getSampleSteps();
		if (!CollectionUtils.isEmpty(sampleSteps)) {
			String sampleStepsStr = sampleSteps.stream()
					.map(String::valueOf)
					.collect(Collectors.joining(","));
			strategyCmp.setSampleSteps(sampleStepsStr);
		}
	}

	public void buildReq(StrategyCmpNew strategyCmp, AdminCmpIntegrationSubmitVo submitNew) {
		try {
			List<String> strategyVersionIds = submitNew.getStrategyVersionId();
			if (strategyVersionIds.isEmpty()) {
				throw new RuntimeException("对比集成测试至少需要一个策略集成");
			}

			AdminCandidateStrategy strategyIntegration1 = adminCandidateStrategyService.selectById(Integer.parseInt(strategyVersionIds.get(0)));
			AdminCandidateStrategy strategyIntegration2 = strategyVersionIds.size() > 1 ? adminCandidateStrategyService.selectById(Integer.parseInt(strategyVersionIds.get(1))) : null;

			// 策略迁移临时方案，快速验证数据
			JSONObject config = JSON.parseObject(NacosClient.getByNameSpace(ApolloLocalNamespace.localNamespace
					, "strategy.cmp.hiveSqlTemplate.key", "{}"));
			String key = config.getOrDefault(submitNew.getSampleStrategyTypes(),
					"cmp_strategy_integration_test_var_vulcan_prefix_new").toString();
			String hiveSqlTemplate = systemParameterMapper.selectByKey(key).getValue();

			String formatSQL = buildFormatSQL(submitNew, hiveSqlTemplate);

			JSONObject data = strategyIntegration2 == null
					? integrationSubmitVoBuild(submitNew, strategyIntegration1, formatSQL)
					: cmpIntegrationSubmitVoBuild(submitNew, strategyIntegration1, strategyIntegration2, formatSQL);

			setStrategyCmpFields(strategyCmp, data);
			strategyCmpMapper.insert(strategyCmp);
		} catch (Exception ex) {
			logger.error("对比集成测试提交失败", ex);
			throw new RuntimeException("对比集成测试提交失败:" + ex.getMessage());
		}
	}

	private String buildFormatSQL(AdminCmpIntegrationSubmitVo submitNew, String hiveSqlTemplate) {
		String startDate = DateUtils.dateTime(submitNew.getSamplePeriod().get(0));
		String endDate = DateUtils.dateTime(submitNew.getSamplePeriod().get(1));
		String startTime = DateUtils.dateTimeSS(submitNew.getSamplePeriod().get(0));
		String endTime = DateUtils.dateTimeSS(submitNew.getSamplePeriod().get(1));
		String sampleStepsStr = String.join("','", submitNew.getSampleSteps());
		int limit = submitNew.getCount() == null ? DEFAULT_LIMIT : submitNew.getCount();
		return String.format(hiveSqlTemplate, startDate, endDate, startTime, endTime, sampleStepsStr, limit, startDate, endDate, startTime, endTime, sampleStepsStr);
	}

	private void setStrategyCmpFields(StrategyCmpNew strategyCmp, JSONObject data) {
		strategyCmp.setTaskId(data.getString("task_id"));
		strategyCmp.setDevelopmentCenterUrl(data.getString("development_center_url"));
		strategyCmp.setResultTableName(data.getString("result_table_name"));
		strategyCmp.setDiffResultTableName(data.getString("diff_result_table_name"));
	}

	private JSONObject integrationSubmitVoBuild(AdminCmpIntegrationSubmitVo submitNew, AdminCandidateStrategy adminCandidateStrategy, String formatSQL) {
		HashMap<String, Object> payload = new HashMap<>();
		List<JSONObject> codeList = buildCodeList(adminCandidateStrategy, submitNew.getSampleSteps());
		List<String> additionKeyList = Arrays.asList(Optional.ofNullable(submitNew.getAdditionKey()).orElse("").split("\n"));
		payload.put("id", adminCandidateStrategy.getId());
		payload.put("addition_key", additionKeyList);
		payload.put("strategy_codes", codeList);
		payload.put("sample_selection_sql", formatSQL);
		return dataWarehouseServiceClient.submitIntegrationTest(payload);
	}

	private List<JSONObject> buildCodeList(AdminCandidateStrategy adminCandidateStrategy, List<String> sampleSteps) {
		List<JSONObject> codeList = new ArrayList<>();
		Integer projectId = adminCandidateStrategy.getProjectId();

		Map<String, String> finalCode = strategyNodeService.getFinalCodeForCmpIntegrationTest(projectId, adminCandidateStrategy);
		for (String nodeName : sampleSteps) {
			if (finalCode.containsKey(nodeName)) {
				JSONObject item = new JSONObject();
				item.put("step", nodeName);
				item.put("strategy_code", finalCode.get(nodeName));
				codeList.add(item);
			}
		}
		if (codeList.isEmpty()) {
			throw new RuntimeException("策略代码组装失败！");
		}
		return codeList;
	}

	private JSONObject cmpIntegrationSubmitVoBuild(AdminCmpIntegrationSubmitVo submitNew, AdminCandidateStrategy strategyIntegration1, AdminCandidateStrategy strategyIntegration2, String formatSQL) {
		List<JSONObject> diffStrategyCodes = buildDiffStrategyCodes(strategyIntegration1, strategyIntegration2, submitNew.getSampleSteps());
		HashMap<String, Object> payload = new HashMap<>();
		payload.put("diff_strategy_codes", diffStrategyCodes);
		payload.put("sample_selection_sql", formatSQL);
		List<String> compareKeyList = Arrays.asList(Optional.ofNullable(submitNew.getCompareKey()).orElse("").split("\n"));
		List<String> additionKeyList = Arrays.asList(Optional.ofNullable(submitNew.getAdditionKey()).orElse("").split("\n"));
		payload.put("comparison_fields_path", compareKeyList);
		payload.put("addition_key", additionKeyList);
		return dataWarehouseServiceClient.submitCompareIntegrationTest(payload);
	}

	private List<JSONObject> buildDiffStrategyCodes(AdminCandidateStrategy strategyIntegration1
			, AdminCandidateStrategy strategyIntegration2, List<String> sampleSteps) {
		List<JSONObject> diffStrategyCodes = new ArrayList<>();
		Integer projectId = strategyIntegration1.getProjectId();

		Map<String, String> finalCode1 = strategyNodeService.getFinalCodeForCmpIntegrationTest(projectId, strategyIntegration1);
		Map<String, String> finalCode2 = strategyNodeService.getFinalCodeForCmpIntegrationTest(projectId, strategyIntegration2);

		for (String nodeName : sampleSteps) {
			if (finalCode1.containsKey(nodeName) && finalCode2.containsKey(nodeName)) {
				JSONObject item = new JSONObject();
				item.put("node_name", nodeName);
				item.put("strategy_code1", finalCode1.get(nodeName));
				item.put("strategy_code2", finalCode2.get(nodeName));
				item.put("integration_id1", strategyIntegration1.getId());
				item.put("integration_id2", strategyIntegration2.getId());
				diffStrategyCodes.add(item);
			}
		}

		if (diffStrategyCodes.isEmpty()) {
			throw new RuntimeException("策略代码组装失败！");
		}

		return diffStrategyCodes;
	}
}