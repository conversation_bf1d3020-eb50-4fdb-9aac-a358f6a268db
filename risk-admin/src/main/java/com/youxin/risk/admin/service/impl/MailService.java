package com.youxin.risk.admin.service.impl;

import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.SystemUtil;
import freemarker.template.Template;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;
import org.springframework.web.servlet.view.freemarker.FreeMarkerConfigurer;

import javax.annotation.Resource;
import javax.mail.internet.MimeMessage;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

@Component
public class MailService {

    private Logger logger = LoggerFactory.getLogger(MailService.class);

    @Autowired
    private FreeMarkerConfigurer freemarker;

    @Autowired
    private JavaMailSender mailSender;

    @Resource(name = "configProperties")
    private Properties properties;

    public void sendEmail(Map<String,Object> params, String templateName, Set<String> receiverList){
        String content;
        try {
            // 获取模版对象
            Template template = freemarker.getConfiguration().getTemplate(templateName);
            // 解析 模版和 数据
            content = FreeMarkerTemplateUtils.processTemplateIntoString(template, params);
            LoggerProxy.info("sendEmail",logger,content);
        } catch (Exception e) {
            LoggerProxy.error("sendEmail", logger, e.getMessage(), e);
            return;
        }
        String title = (String)params.get("title");
        sendEmail(title, content, receiverList);
    }

    public void sendEmail(String title, String content, Set<String> emails) {
        if (CollectionUtils.isEmpty(emails)) {
            return;
        }
        for (int i = 1, size = 3; i <= size; i++) {
            try {
                MimeMessage message = this.mailSender.createMimeMessage();
                MimeMessageHelper helper = new MimeMessageHelper(message, true, "utf-8");
                helper.setFrom(properties.getProperty("mail.from"));
                String[] to = new String[emails.size()];
                emails.toArray(to);
                helper.setTo(to);
                helper.setSubject(title);
                helper.setText(content, true);
                mailSender.send(message);
                break;
            } catch (Exception e) {
                if (i == size) {
                    LoggerProxy.error("sendEmailException", logger, e.getMessage(), e);
                } else {
                    SystemUtil.threadSleep(100);
                }
            }
        }
    }
}
