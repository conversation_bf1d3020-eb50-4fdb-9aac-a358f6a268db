package com.youxin.risk.admin.model.ruleEngine;

import java.io.Serializable;
import java.util.Date;

/**
 * x新决策引擎master共享区表(RuleProjectMasterCommit)实体类
 *
 * <AUTHOR>
 * @since 2023-09-19 15:26:24
 */
public class RuleProjectMasterCommit implements Serializable {
    private static final long serialVersionUID = 621642938316484360L;
    /**
     * 主键
     */
    private Integer id;
    /**
     * (项目id)
     */
    private Integer projectId;
    /**
     * (项目名称)
     */
    private String projectName;
    /**
     * (文件名称)
     */
    private String fileName;
    /**
     * 操作流水表id
     */
    private Integer historyMasterId;
    /**
     * （该版本文件编写人）
     */
    private String username;
    /**
     * (是否删除，0正常，1删除)
     */
    private Integer isDel;
    /**
     * （更新时间）
     */
    private Date updateTime;
    /**
     * （创建时间）
     */
    private Date createTime;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getProjectId() {
        return projectId;
    }

    public void setProjectId(Integer projectId) {
        this.projectId = projectId;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public Integer getHistoryMasterId() {
        return historyMasterId;
    }

    public void setHistoryMasterId(Integer historyMasterId) {
        this.historyMasterId = historyMasterId;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public Integer getIsDel() {
        return isDel;
    }

    public void setIsDel(Integer isDel) {
        this.isDel = isDel;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

}

