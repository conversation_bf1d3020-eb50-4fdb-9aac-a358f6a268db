package com.youxin.risk.admin.model;

import com.youxin.risk.commons.model.BaseModel;

public class AdminDiServiceInput extends BaseModel {
    /**
     * 
     */
    private static final long serialVersionUID = 6192310139427309206L;

    private String serviceCode;

    private String inputCode;

    private String inputType;
    
    private String inputValue;

    private String valueType;

    private Boolean isTrans;

    private Boolean isRequired;

    private String transCode;
    
    public Boolean getIsTrans() {
        return isTrans;
    }

    public void setIsTrans(Boolean isTrans) {
        this.isTrans = isTrans;
    }

    public Boolean getIsRequired() {
        return isRequired;
    }

    public void setIsRequired(Boolean isRequired) {
        this.isRequired = isRequired;
    }

    public String getInputValue() {
        return inputValue;
    }

    public void setInputValue(String inputValue) {
        this.inputValue = inputValue;
    }

    public String getValueType() {
        return valueType;
    }

    public void setValueType(String valueType) {
        this.valueType = valueType;
    }

    public String getServiceCode() {
        return serviceCode;
    }

    public void setServiceCode(String serviceCode) {
        this.serviceCode = serviceCode == null ? null : serviceCode.trim();
    }

    public String getInputCode() {
        return inputCode;
    }

    public void setInputCode(String inputCode) {
        this.inputCode = inputCode == null ? null : inputCode.trim();
    }

    public String getInputType() {
        return inputType;
    }

    public void setInputType(String inputType) {
        this.inputType = inputType == null ? null : inputType.trim();
    }

    public String getTransCode() {
        return transCode;
    }

    public void setTransCode(String transCode) {
        this.transCode = transCode == null ? null : transCode.trim();
    }
}