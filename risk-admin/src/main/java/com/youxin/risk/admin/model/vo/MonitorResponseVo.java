package com.youxin.risk.admin.model.vo;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @description: 卡单监控响应参数
 * @author: juxiang
 * @create: 2021-09-02 17:13
 **/
@Getter
@Setter
@Builder
public class MonitorResponseVo {
    private String name;
    private int cardNum;
    private int increaseNum;
    private int consumeNum;
    private List<CardDetail> cardDetailList;
}
