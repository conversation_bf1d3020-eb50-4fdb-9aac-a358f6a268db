package com.youxin.risk.admin.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.youxin.risk.admin.dao.di.AdminDiRequestLogMapper;
import com.youxin.risk.admin.model.AdminDiRequestLog;
import com.youxin.risk.admin.service.AdminDiRequestLogService;
import com.youxin.risk.admin.vo.AdminDiRequestLogVo;

@Service
public class AdminDiRequestLogServiceImpl implements AdminDiRequestLogService {

    @Autowired
    private AdminDiRequestLogMapper diRequestLogMapper;

    @Override
    public List<AdminDiRequestLog> queryAllRequestLog(AdminDiRequestLogVo request) {
        // 获取分页参数
        int startIndex = 0;
        int pageSize = 20;
        if (request.getPageSize() != null) {
            pageSize = request.getPageSize();
            if (request.getPageNum() != null) {
                startIndex = (request.getPageNum() - 1) * pageSize;
            }
        }
        request.setStartIndex(startIndex);
        request.setPageSize(pageSize);
        return diRequestLogMapper.selectAllRequestLog(request);
    }

    @Override
    public int removeRequestLog(Long id) {
        return diRequestLogMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int queryAllRequestLogTotal(AdminDiRequestLogVo request) {
        return diRequestLogMapper.selectRequestLogTotal(request);
    }

}
