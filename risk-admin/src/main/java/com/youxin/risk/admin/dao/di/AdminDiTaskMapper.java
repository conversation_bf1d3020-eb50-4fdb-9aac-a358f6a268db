package com.youxin.risk.admin.dao.di;

import java.util.List;

import com.youxin.risk.admin.model.AdminDiTask;
import com.youxin.risk.admin.vo.AdminDiTaskVo;
import org.apache.ibatis.annotations.Param;

public interface AdminDiTaskMapper {
    int deleteByPrimaryKey(Long id);

    int insertSelective(AdminDiTask record);

    AdminDiTask selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(AdminDiTask record);

    int updateByPrimaryKeyWithBLOBs(AdminDiTask record);

    List<AdminDiTask> selectAllTask(AdminDiTaskVo request);
    
    int selectAllTaskTotal(AdminDiTaskVo record);

    List<AdminDiTask> selectByRequestIdList(@Param("requestIdList")List<String> requestIdList);

    int updateStatusByRequestIdList(@Param("requestIdList")List<String> requestIdList, @Param("status")String status);

    List<AdminDiTask> selectByRequestIds(@Param("requestIds") List<String> requestIds);
}