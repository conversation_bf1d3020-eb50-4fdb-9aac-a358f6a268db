package com.youxin.risk.admin.controller.ruleEngine.v1;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.youxin.risk.admin.model.ruleEngine.SingleTestResultVo;
import com.youxin.risk.admin.service.ruleEngine.ProjectExecuteService;
import com.youxin.risk.admin.vo.JsonResultVo;
import com.youxin.risk.admin.vo.ruleEngine.StrategyIntegrationTestModel;
import com.youxin.risk.admin.vo.ruleEngine.StrategySingleTestModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Map;

@RestController
@RequestMapping("/executes/v1")
public class AdminProjectExecuteController {
    private Logger logger = LoggerFactory.getLogger(AdminProjectExecuteController.class);

    @Resource
    private ProjectExecuteService projectExecuteService;

    /**
     * 执行单笔测试的接口
     */
    @ResponseBody
    @RequestMapping(value = "/single_test", method = RequestMethod.POST)
    public JsonResultVo singleTest(@RequestBody @Valid StrategySingleTestModel strategySingleTestModel) {
        try {
            logger.info("single test params:{}", JSON.toJSONString(strategySingleTestModel));
            SingleTestResultVo resultVo = projectExecuteService.singleExecute(strategySingleTestModel);
            JsonResultVo jsonResult = JsonResultVo.success();
            jsonResult.setData(BeanUtil.beanToMap(resultVo));
            return jsonResult;
        } catch (Exception e) {
            logger.error("", e);
            return JsonResultVo.error(e.getMessage());
        }
    }

    /**
     * 执行集成测试的接口
     */
    @ResponseBody
    @RequestMapping(value = "/integration_test", method = RequestMethod.POST)
    public JsonResultVo integrationTest(@RequestBody @Valid StrategyIntegrationTestModel strategyIntegrationTestModel) {
        try {
            logger.info("integration test params:{}", JSON.toJSONString(strategyIntegrationTestModel));
            projectExecuteService.integrationExecute(strategyIntegrationTestModel);
            return JsonResultVo.success();
        } catch (Exception e) {
            logger.error("", e);
            return JsonResultVo.error(e.getMessage());
        }
    }

    /**
     * 获取策略入参数据
     */
    @ResponseBody
    @RequestMapping(value = "/get_strategy_input", method = RequestMethod.POST)
    public JsonResultVo getStrategyInput(@RequestBody Map<String, Object> map) {
        try {
            Map<String,Object> strategyInput = projectExecuteService.getStrategyInput(map);
            JsonResultVo jsonResult = JsonResultVo.success();
            jsonResult.setData(strategyInput);
            return jsonResult;
        } catch (Exception e) {
            logger.error("", e);
            return JsonResultVo.error(e.getMessage());
        }
    }
}
