package com.youxin.risk.admin.model;

import com.youxin.risk.commons.model.BaseModel;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/11/13 15:08
 */
public class AdminEventInfo extends BaseModel {
    private static final long serialVersionUID = 2565755947210032531L;

    private String eventCode;
    private String eventName;
    private String eventType;
    private String processDefId;
    private String referrenceId;
    private String sourceSystem; // 来源系统
    private Integer version;//version指的是事件关联的流程的生效版本
    private String oldProcessDefId;
    private Boolean isNewProcess;

    private List<AdminEventInput> inputs;
    private List<AdminEventOutput> outputs;

    public String getReferrenceId() {
        return referrenceId;
    }

    public void setReferrenceId(String referrenceId) {
        this.referrenceId = referrenceId;
    }

    public String getSourceSystem() {
        return sourceSystem;
    }

    public void setSourceSystem(String sourceSystem) {
        this.sourceSystem = sourceSystem;
    }

    public String getEventCode() {
        return eventCode;
    }

    public void setEventCode(String eventCode) {
        this.eventCode = eventCode;
    }

    public String getEventName() {
        return eventName;
    }

    public void setEventName(String eventName) {
        this.eventName = eventName;
    }

    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    public String getProcessDefId() {
        return processDefId;
    }

    public void setProcessDefId(String processDefId) {
        this.processDefId = processDefId;
    }

    public List<AdminEventInput> getInputs() {
        return inputs;
    }

    public void setInputs(List<AdminEventInput> inputs) {
        this.inputs = inputs;
    }

    public List<AdminEventOutput> getOutputs() {
        return outputs;
    }

    public void setOutputs(List<AdminEventOutput> outputs) {
        this.outputs = outputs;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getOldProcessDefId() {
        return oldProcessDefId;
    }

    public void setOldProcessDefId(String oldProcessDefId) {
        this.oldProcessDefId = oldProcessDefId;
    }

    public Boolean getIsNewProcess() {
        return isNewProcess;
    }

    public void setIsNewProcess(Boolean isNewProcess) {
        this.isNewProcess = isNewProcess;
    }
}
