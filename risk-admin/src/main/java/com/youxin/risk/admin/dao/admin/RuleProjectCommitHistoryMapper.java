package com.youxin.risk.admin.dao.admin;

import com.youxin.risk.admin.model.ruleEngine.RuleProjectCommitHistory;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 新决策引擎项目提交历史表(RuleProjectCommitHistory)表数据库访问层
 */
public interface RuleProjectCommitHistoryMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    RuleProjectCommitHistory queryById(Integer id);

    /**
     * 查询指定行数据
     *
     * @param ruleProjectCommitHistory 查询条件
     * @param pageable                 分页对象
     * @return 对象列表
     */
    List<RuleProjectCommitHistory> queryAllByLimit(RuleProjectCommitHistory ruleProjectCommitHistory, @Param("pageable") Pageable pageable);

    /**
     * 统计总行数
     *
     * @param ruleProjectCommitHistory 查询条件
     * @return 总行数
     */
    long count(RuleProjectCommitHistory ruleProjectCommitHistory);

    /**
     * 新增数据
     *
     * @param ruleProjectCommitHistory 实例对象
     * @return 影响行数
     */
    int insert(RuleProjectCommitHistory ruleProjectCommitHistory);

    /**
     * 插入主键的数据
     */
    int insertWithId(RuleProjectCommitHistory ruleProjectCommitHistory);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<RuleProjectCommitHistory> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<RuleProjectCommitHistory> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<RuleProjectCommitHistory> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<RuleProjectCommitHistory> entities);

    /**
     * 修改数据
     *
     * @param ruleProjectCommitHistory 实例对象
     * @return 影响行数
     */
    int update(RuleProjectCommitHistory ruleProjectCommitHistory);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Long id);

    RuleProjectCommitHistory queryLatestByProjectId(Integer projectId);

    /**
     * 根据项目id、用户名称、文件名称查询提交历史
     */
    List<RuleProjectCommitHistory> queryByProjectIdAndUserNameAndFileName(@Param("projectId") Integer projectId, @Param("fileName") String fileName,
                                                                          @Param("userName") String userName);

    /**
     * 根据项目id、文件名称查询提交历史
     */
    List<RuleProjectCommitHistory> queryByProjectIdAndFileName(@Param("projectId") Integer projectId, @Param("fileName") String fileName);

    /**
     * 根据项目id和节点名称查询最新的提交记录
     */
    RuleProjectCommitHistory queryLatestByProjectIdAndNodeName(@Param("projectId") Integer projectId, @Param("fileName") String fileName);

    /**
     * 根据项目id和节点名称查询最早的创建记录
     */
    RuleProjectCommitHistory queryEarliestByProjectId(@Param("projectId") Integer projectId);

}

