package com.youxin.risk.admin.model;

import com.youxin.risk.admin.annotation.Excel;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;

/**
 * 数据源导出model
 * <AUTHOR>
 * @since 2022/4/24 17:31
 */
@Data
@Accessors(chain = true)
public class EventThirdDetailModel implements Serializable {

    private static final long serialVersionUID = 515615651651651L;

    @Excel(name = "数据源中文名称")
    private String datasourceName;

    @Excel(name = "事件")
    private String eventName;

    @Excel(name = "配置步骤")
    private String step;

    @Excel(name = "是否等待非必须")
    private String waitNotRequired;

    @Excel(name = "关联节点类型")
    private String type;

    @Excel(name = "等待时间")
    private String waitTimeout;

    @Excel(name = "是否卡单")
    private String isRequired;

    @Excel(name = "是否关闭")
    private String isDelete;

    @Excel(name = "是否使用缓存")
    private String useCache;

    @Excel(name = "是否仅使用缓存")
    private String onlyUseCache;

    @Excel(name = "缓存天数")
    private String cacheDay;

    @Excel(name = "数据是否全量")
    private String dataComplete;

    @Excel(name = "分流逻辑")
    private String requestExpression;

    @Excel(name = "数据路径")
    private String dataVoPath;

    @Excel(name = "创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String createTime;
}
