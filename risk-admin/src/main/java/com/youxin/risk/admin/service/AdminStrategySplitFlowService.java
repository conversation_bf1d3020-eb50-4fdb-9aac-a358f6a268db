package com.youxin.risk.admin.service;

import com.youxin.risk.admin.model.AdminStrategySplitFlow;
import com.youxin.risk.admin.model.Page;
import com.youxin.risk.commons.vo.StrategySplitFlowResultVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/1/15 10:26
 */
public interface AdminStrategySplitFlowService extends BaseService<AdminStrategySplitFlow> {
    Boolean checkExists(String splitFlowCode);

    List<AdminStrategySplitFlow> selectForEnable();

    List<AdminStrategySplitFlow> selectForDisable();

    void updateStatus(List<Long> idList, String status);

    Page<StrategySplitFlowResultVo> selectSplitFlowResultPage(Map<String, Object> params);

    StrategySplitFlowResultVo getSplitFlowResult(String id);
}
