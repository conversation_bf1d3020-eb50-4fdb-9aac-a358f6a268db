package com.youxin.risk.admin.model;

import com.youxin.risk.commons.model.BaseModel;

/**
 * <AUTHOR>
 * @date 2018/11/13 15:09
 */
public class AdminEventInput extends BaseModel {
    private static final long serialVersionUID = -8838520846434048418L;

    private String eventCode;
    private String variableCode;
    private Boolean isTrans;
    private Boolean isRequired;
    private String transCode;

    public String getEventCode() {
        return eventCode;
    }

    public void setEventCode(String eventCode) {
        this.eventCode = eventCode;
    }

    public String getVariableCode() {
        return variableCode;
    }

    public void setVariableCode(String variableCode) {
        this.variableCode = variableCode;
    }

    public Boolean getIsTrans() {
        return isTrans;
    }

    public void setIsTrans(Boolean isTrans) {
        this.isTrans = isTrans;
    }

    public Boolean getIsRequired() {
        return isRequired;
    }

    public void setIsRequired(Boolean isRequired) {
        this.isRequired = isRequired;
    }

    public String getTransCode() {
        return transCode;
    }

    public void setTransCode(String transCode) {
        this.transCode = transCode;
    }
}
