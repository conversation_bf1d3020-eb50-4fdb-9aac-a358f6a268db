package com.youxin.risk.admin.vo;

import com.youxin.risk.commons.model.BaseModel;

import lombok.Data;

@Data
public class AdminManualNodeDataVo extends BaseModel {

	private static final long serialVersionUID = 53579668633987632L;

	private String nodeCode;
	private String fromNodeCode;
	private Boolean isDelete;
	private String step;
	private Boolean isRequired;
    private String eventCode;
    private String sourceSystem;
    private Boolean useCache; // 是否使用缓存
    private Integer cacheDay; // 缓存天数
    private Long waitTimeout; // 等待时间
    private Boolean onlyUseCache; //是否仅用缓存
    private String type;

}