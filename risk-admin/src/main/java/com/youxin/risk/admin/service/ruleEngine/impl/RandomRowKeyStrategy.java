package com.youxin.risk.admin.service.ruleEngine.impl;

import com.youxin.risk.admin.dao.rm.RmStrategyResultIndexMapper;
import com.youxin.risk.admin.dto.rulescore.RmStrategyResultIndexDTO;
import com.youxin.risk.admin.model.ruleEngine.RuleProject;
import com.youxin.risk.admin.service.ruleEngine.RuleProjectService;
import com.youxin.risk.admin.service.ruleEngine.StrategyInputProvider;
import org.datanucleus.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Random;

@Component
public class RandomRowKeyStrategy implements StrategyInputProvider {
    private static final Logger logger = LoggerFactory.getLogger(RandomRowKeyStrategy.class);

    private static final String STRATEGY_TYPE_NEW = "strategyTypeNew";
    private static final String CREATE_TIME_START = "createTimeStart";
    private static final String CREATE_TIME_END = "createTimeEnd";
    @Autowired
    private RmStrategyResultIndexMapper rmStrategyResultIndexMapper;
    @Autowired
    private RuleProjectService ruleProjectService;

    @Override
    public RmStrategyResultIndexDTO getRowKeyInfo(Map<String, Object> map) {
        String strategyTypeNew = (String) map.get(STRATEGY_TYPE_NEW);
        if (StringUtils.isEmpty(strategyTypeNew)) {
            Integer projectId = (Integer) map.get("projectId");
            if (projectId == null) {
                logger.info("getRowKeyByParams is null actionType 1");
                return null;
            }
            RuleProject ruleProject = ruleProjectService.getProjectById(projectId);
            strategyTypeNew = ruleProject.getStrategyType();
        }

        String createTimeStart = (String) map.get(CREATE_TIME_START);
        String createTimeEnd = (String) map.get(CREATE_TIME_END);
        List<RmStrategyResultIndexDTO> rmStrategyResultIndexDTOS = rmStrategyResultIndexMapper
                .getResultByTypeAndCreateTime(strategyTypeNew, createTimeStart, createTimeEnd);

        if (CollectionUtils.isEmpty(rmStrategyResultIndexDTOS)) {
            logger.info("getRowKeyByParams is null actionType 1");
            return null;
        }

        Random random = new Random();
        RmStrategyResultIndexDTO rmStrategyResultIndexDTO= rmStrategyResultIndexDTOS.get(random.nextInt(rmStrategyResultIndexDTOS.size()));
        logger.info("getRowKeyByParams actionType 1 rowKey {}", rmStrategyResultIndexDTO.getRowKey());
        return rmStrategyResultIndexDTO;
    }
}