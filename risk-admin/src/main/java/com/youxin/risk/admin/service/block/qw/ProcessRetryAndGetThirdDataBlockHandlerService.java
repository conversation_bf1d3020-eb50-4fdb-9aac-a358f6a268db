package com.youxin.risk.admin.service.block.qw;

import com.google.common.collect.Lists;
import com.youxin.risk.admin.service.EngineEventService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *<AUTHOR>
 *@create 2024/7/12 17:22
 *@desc
 */
@Service
public class ProcessRetryAndGetThirdDataBlockHandlerService extends AbstractBlockHandlerService<String> {
    @Autowired
    private EngineEventService engineEventService;

    @Override
    protected String queryAlertInfo(String... params) {
        String sessionId = params[0];
        engineEventService.setFailedAndDeleteThirdparty(Lists.newArrayList(sessionId));
        return sessionId;
    }

    @Override
    protected String buildAlertMessage(String sessionId, String... params) {
        return String.format("操作成功：sessionId=%s, 操作类型=%s", infoMessage(sessionId), infoMessage("重新发起流程并获取三方"));
    }



    @Override
    protected String supportOpType() {
        return "processAndThirdpartyRetry";
    }
}
