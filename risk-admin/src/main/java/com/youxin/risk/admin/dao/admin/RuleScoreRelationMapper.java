package com.youxin.risk.admin.dao.admin;

import com.youxin.risk.admin.dao.BaseMapper;
import com.youxin.risk.admin.model.rulescore.RuleScoreRelation;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface RuleScoreRelationMapper extends BaseMapper<RuleScoreRelation> {
    int insertBatch(@Param("list") List<RuleScoreRelation> list);

    List<RuleScoreRelation> findRuleScoreRelationList(@Param("ruleKey") String ruleKey,@Param("ruleVersion") Integer ruleVersion);

    List<RuleScoreRelation> findRuleScoreChildRelationList(@Param("ruleKey") String ruleKey,@Param("ruleVersion") Integer ruleVersion);
}
