package com.youxin.risk.admin.controller.block.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019-06-10
 */
@Data
public class RRDBlockDataRecord {

    private String applicationId;

    private String serviceCode;

    private String thirdPartyType;

    private String recordType;

    private String jobId;

    private String userKey;

    private String step;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss.SSSZ")
    private Date createTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss.SSSZ")
    private Date updateTime;

}
