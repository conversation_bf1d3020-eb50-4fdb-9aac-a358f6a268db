package com.youxin.risk.admin.service;

import com.youxin.risk.admin.model.alert.AlertAdminUser;
import com.youxin.risk.admin.model.alert.AlertAdminUserGroup;
import com.youxin.risk.admin.vo.AlertAdminUserGroupVo;

import java.util.List;

/**
 * <AUTHOR>
public interface AlertAdminGroupService extends BaseAlertService<AlertAdminUserGroup> {
    int removeItem(AlertAdminUserGroup alertAdminUserGroup);

    int modifyItem(AlertAdminUserGroupVo alertAdminUserGroupVo);


    List<AlertAdminUser> queryAllUsersByGroup(AlertAdminUserGroup alertAdminUserGroup);

}
