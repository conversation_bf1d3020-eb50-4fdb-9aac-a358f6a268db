package com.youxin.risk.admin.service.ruleEngine.impl;

import com.alibaba.fastjson.JSON;
import com.youxin.risk.admin.dao.admin.RuleProjectMapper;
import com.youxin.risk.admin.model.ruleEngine.RuleProject;
import com.youxin.risk.admin.model.ruleEngine.RuleProjectCommitHistory;
import com.youxin.risk.admin.model.ruleEngine.RuleProjectUserCommit;
import com.youxin.risk.admin.service.AdminStrategyTypeService;
import com.youxin.risk.admin.service.ruleEngine.AdminCandidateStrategyService;
import com.youxin.risk.admin.service.ruleEngine.ProjectPullService;
import com.youxin.risk.admin.service.ruleEngine.RuleProjectCommitHistoryService;
import com.youxin.risk.admin.service.ruleEngine.RuleProjectUserCommitService;
import com.youxin.risk.admin.utils.UserInfoUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

@Component
public class ProjectPullServiceImpl implements ProjectPullService {
    private static final Logger logger = LoggerFactory.getLogger(ProjectPullServiceImpl.class);

    @Resource
    private RuleProjectMapper ruleProjectMapper;
    @Autowired
    private AdminCandidateStrategyService adminCandidateStrategyService;
    @Resource
    private RuleProjectUserCommitService ruleProjectUserCommitService;
    @Autowired
    private AdminStrategyTypeService adminStrategyTypeService;
    @Resource
    private RuleProjectCommitHistoryService ruleProjectCommitHistoryService;

    /**
     * 查询个人分支与线上分支的差异
     * @param projectId
     * @return
     */
    public List<String> getUnPulledNodes(Integer projectId) {
        RuleProject ruleProject = ruleProjectMapper.queryById(projectId);
        Map<String, Integer> deploySuccessCommits = adminCandidateStrategyService
                .getDeploySuccessCommits(ruleProject.getStrategyType());

        List<RuleProjectUserCommit> ruleProjectUserCommits = ruleProjectUserCommitService
                .queryByProjectIdAndUserName(projectId, UserInfoUtil.getUsername());

        List<String> nodeNames = adminStrategyTypeService.selectStrategyNodes(ruleProject.getStrategyType());

        List<String> diffNodeNames = new ArrayList<>();
        for (Map.Entry<String, Integer> entry : deploySuccessCommits.entrySet()) {
            String nodeName = entry.getKey();
            Integer historyId = entry.getValue();

            Optional<RuleProjectUserCommit> ruleProjectUserCommit = ruleProjectUserCommits.stream()
                    .filter(r -> r.getFileName().equals(nodeName) && r.getIsDel() == 0)
                    .findAny();

            if ((!ruleProjectUserCommit.isPresent()
                    || !historyId.equals(ruleProjectUserCommit.get().getHistoryMasterId()))
                    && nodeNames.contains(nodeName)) {
                diffNodeNames.add(nodeName);
            }
        }

        return diffNodeNames;
    }


    /**
     * 拉取线上代码
     * @param projectId
     * @param nodeNameList
     */
    public void pullOnlineVersion(Integer projectId, List<String> nodeNameList) {
        RuleProject ruleProject = ruleProjectMapper.queryById(projectId);
        Map<String, Integer> deploySuccessCommits = adminCandidateStrategyService.getDeploySuccessCommits(ruleProject.getStrategyType());
        logger.info("部署的线上版本: {} 项目id: {}", JSON.toJSONString(deploySuccessCommits), projectId);
        Date now = new Date();
        nodeNameList.forEach(nodeName -> {
            RuleProjectUserCommit ruleProjectUserCommit = ruleProjectUserCommitService
                    .queryByProjectIdAndUserNameAndFileName(projectId, UserInfoUtil.getUsername(), nodeName);
            logger.info("nodeName: {} ruleProjectUserCommit: {}", nodeName, JSON.toJSONString(ruleProjectUserCommit));
            Optional.ofNullable(deploySuccessCommits.get(nodeName)).ifPresent(historyId -> {
                RuleProjectCommitHistory ruleProjectCommitHistory = createOnlineCommitHistory(historyId, now);
                updateOrInsertUserCommit(ruleProjectUserCommit, ruleProject, nodeName, ruleProjectCommitHistory, historyId, now);
            });
        });
    }

    /**
     * 强制恢复到线上代码
     * @param projectId
     */
    public void recoverToOnlineVersion(Integer projectId) {
        RuleProject ruleProject = ruleProjectMapper.queryById(projectId);
        Map<String, Integer> deploySuccessCommits = adminCandidateStrategyService.getDeploySuccessCommits(ruleProject.getStrategyType());
        if (CollectionUtils.isEmpty(deploySuccessCommits)) {
            throw new IllegalArgumentException("线上版本为空");
        }
        Date now = new Date();
        List<String> nodeNameList = adminStrategyTypeService.selectStrategyNodes(ruleProject.getStrategyType());
        nodeNameList.forEach(nodeName -> {
            RuleProjectUserCommit ruleProjectUserCommit = ruleProjectUserCommitService
                    .queryByProjectIdAndUserNameAndFileName(projectId, UserInfoUtil.getUsername(), nodeName);
            Integer historyId = deploySuccessCommits.get(nodeName);
            if (historyId != null) {
                RuleProjectCommitHistory ruleProjectCommitHistory = createOnlineCommitHistory(historyId, now);
                updateOrInsertUserCommit(ruleProjectUserCommit, ruleProject, nodeName, ruleProjectCommitHistory, historyId, now);
            }
        });
    }

    private RuleProjectCommitHistory createOnlineCommitHistory(Integer historyId, Date now) {
        RuleProjectCommitHistory ruleProjectCommitHistory = ruleProjectCommitHistoryService.queryById(historyId);
        ruleProjectCommitHistory.setUsername(UserInfoUtil.getUsername());
        ruleProjectCommitHistory.setUpdateTime(now);
        ruleProjectCommitHistory.setCreateTime(now);
        ruleProjectCommitHistory.setComment("恢复到线上版本");
        ruleProjectCommitHistoryService.insert(ruleProjectCommitHistory);
        return ruleProjectCommitHistory;
    }

    private void updateOrInsertUserCommit(RuleProjectUserCommit ruleProjectUserCommit
            , RuleProject ruleProject, String nodeName, RuleProjectCommitHistory ruleProjectCommitHistory, Integer historyId, Date now) {
        if (ruleProjectUserCommit != null) {
            ruleProjectUserCommit.setIsUpdate(0);
            ruleProjectUserCommit.setHistoryCurrentId(ruleProjectCommitHistory.getId());
            ruleProjectUserCommit.setHistoryMasterId(historyId);
            ruleProjectUserCommit.setUpdateTime(ruleProjectCommitHistory.getUpdateTime());
            ruleProjectUserCommitService.update(ruleProjectUserCommit);
        } else {
            RuleProjectUserCommit userCommit = new RuleProjectUserCommit();
            userCommit.setProjectId(ruleProject.getId());
            userCommit.setProjectName(ruleProject.getProjectName());
            userCommit.setFileName(nodeName);
            userCommit.setHistoryCurrentId(ruleProjectCommitHistory.getId());
            userCommit.setHistoryMasterId(historyId);
            userCommit.setUserName(UserInfoUtil.getUsername());
            userCommit.setIsDel(0);
            userCommit.setIsUpdate(0);
            userCommit.setUpdateTime(now);
            userCommit.setCreateTime(now);
            ruleProjectUserCommitService.insert(userCommit);
        }
    }

    /**
     * 获取部署成功的提交id并更新到个人分支historyid
     */
    public void updateUserHistoryForOnlineId(Integer projectId, String userName){
        RuleProject ruleProject = ruleProjectMapper.queryById(projectId);
        Map<String, Integer> deploySuccessCommits = adminCandidateStrategyService
                .getDeploySuccessCommits(ruleProject.getStrategyType());
        logger.info("部署的线上版本: {} 项目id: {}", JSON.toJSONString(deploySuccessCommits), projectId);
        List<RuleProjectUserCommit> ruleProjectUserCommits = ruleProjectUserCommitService
                .queryByProjectIdAndUserName(projectId, userName);
        for (Map.Entry<String, Integer> entry : deploySuccessCommits.entrySet()) {
            String nodeName = entry.getKey();
            Integer historyId = entry.getValue();

            Optional<RuleProjectUserCommit> ruleProjectUserCommit = ruleProjectUserCommits.stream()
                    .filter(r -> r.getFileName().equals(nodeName))
                    .findAny();
            ruleProjectUserCommit.ifPresent(userCommit -> {
                logger.info("projectid: {} nodeName: {} ruleProjectUserCommit: {}"
                        , projectId
                        , nodeName
                        , JSON.toJSONString(userCommit.getHistoryMasterId()));
                userCommit.setHistoryMasterId(historyId);
                userCommit.setUpdateTime(new Date());
                ruleProjectUserCommitService.update(userCommit);
            });
        }
    }
}
