package com.youxin.risk.admin.controller;

import com.youxin.risk.admin.handler.PuDaoQueryDataHandler;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.SpringContext;
import com.youxin.risk.commons.vo.QueryAdminDataVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.List;

/**
 * @description: 为公用的缓存模块提供的缓存刷新获取数据的入口
 * @author: juxiang
 * @create: 2022-12-02 15:06
 **/
@RestController
@RequestMapping("/adminData")
public class QueryDataController extends BaseController{
    private static final Logger LOGGER = LoggerFactory.getLogger(QueryDataController.class);

    @Resource
    private PuDaoQueryDataHandler puDaoQueryDataHandler;

    @RequestMapping("/obtain")
    public Object getData(@RequestBody QueryAdminDataVo queryAdminDataVo) throws Exception {
        // 特殊处理 映射
        queryAdminDataVo = puDaoQueryDataHandler.getQueryAdminDataVoByKey(queryAdminDataVo);

        Class clazz = queryAdminDataVo.getClazz();
        Object bean = SpringContext.getBean(clazz);
        boolean hasArgument = queryAdminDataVo.isHasArgument();
        String methodName = queryAdminDataVo.getMethodName();
        LoggerProxy.info("obtain",LOGGER,"source:{}",clazz.getName()+"-"+methodName);
        if(hasArgument){
            List<QueryAdminDataVo.Argument> argumentList = queryAdminDataVo.getArgumentList();
            Class[] argumentsClass=new Class[argumentList.size()];
            Object[] argumentsVale=new Object[argumentList.size()];
            for(int i=0;i<argumentList.size();i++){
                argumentsClass[i]=argumentList.get(i).getArgueClass();
                argumentsVale[i]=argumentList.get(i).getArgueValue();
            }
            Method declaredMethod = clazz.getDeclaredMethod(methodName, argumentsClass);
            return declaredMethod.invoke(bean, argumentsVale);
        }else {
            Method declaredMethod = clazz.getDeclaredMethod(methodName);
            return declaredMethod.invoke(bean);
        }
    }

    /*private AdminDataResponseVo packageResult(Method declaredMethod,Class clazz,Object data){
        AdminDataResponseVo adminDataResponseVo=new AdminDataResponseVo();
        ResolvableType resolvableType = ResolvableType.forMethodReturnType(declaredMethod);
        adminDataResponseVo.setClazz(clazz);
        adminDataResponseVo.setData(data);
        ResolvableType[] generics = resolvableType.getGenerics();
        List<Class> genericsClass=new ArrayList<>();
        for(ResolvableType generic:generics){
            genericsClass.add(generic.getRawClass());
        }
        adminDataResponseVo.setGenericsClass(genericsClass);
        return adminDataResponseVo;
    }*/
}
