package com.youxin.risk.admin.service.ruleEngine.impl;

import com.youxin.risk.admin.dao.admin.RuleProjectUserCommitMapper;
import com.youxin.risk.admin.model.ruleEngine.RuleProjectUserCommit;
import com.youxin.risk.admin.service.ruleEngine.RuleProjectUserCommitService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 新决策引擎项目个人提交记录表(RuleProjectUserCommit)表服务实现类
 *
 */
@Service
public class RuleProjectUserCommitServiceImpl implements RuleProjectUserCommitService {
    @Resource
    private RuleProjectUserCommitMapper ruleProjectUserCommitMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public RuleProjectUserCommit queryById(Integer id) {
        return this.ruleProjectUserCommitMapper.queryById(id);
    }

    /**
     * 分页查询
     *
     * @param ruleProjectUserCommit 筛选条件
     * @param pageRequest           分页对象
     * @return 查询结果
     */
    @Override
    public Page<RuleProjectUserCommit> queryByPage(RuleProjectUserCommit ruleProjectUserCommit, PageRequest pageRequest) {
        long total = this.ruleProjectUserCommitMapper.count(ruleProjectUserCommit);
        return new PageImpl<>(this.ruleProjectUserCommitMapper.queryAllByLimit(ruleProjectUserCommit, pageRequest), pageRequest, total);
    }

    /**
     * 新增数据
     *
     * @param ruleProjectUserCommit 实例对象
     * @return 实例对象
     */
    @Override
    public RuleProjectUserCommit insert(RuleProjectUserCommit ruleProjectUserCommit) {
        this.ruleProjectUserCommitMapper.insert(ruleProjectUserCommit);
        return ruleProjectUserCommit;
    }

    /**
     * 修改数据
     *
     * @param ruleProjectUserCommit 实例对象
     * @return 实例对象
     */
    @Override
    public RuleProjectUserCommit update(RuleProjectUserCommit ruleProjectUserCommit) {
        this.ruleProjectUserCommitMapper.update(ruleProjectUserCommit);
        return this.queryById(ruleProjectUserCommit.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(Long id) {
        return this.ruleProjectUserCommitMapper.deleteById(id) > 0;
    }

    /**
     * 查询当前用户提交的项目记录
     * @param projectId
     * @param username
     * @return
     */
    @Override
    public List<RuleProjectUserCommit> queryByProjectIdAndUserName(Integer projectId, String username) {
        return this.ruleProjectUserCommitMapper.queryByProjectIdAndUserName(projectId, username);
    }

    /**
     * 根据项目ID和节点名称查询提交记录
     */
    @Override
    public List<RuleProjectUserCommit> queryByProjectIdAndNodeName(Integer projectId, String nodeName) {
        return this.ruleProjectUserCommitMapper.queryByProjectIdAndNodeName(projectId, nodeName);
    }

    /**
     * 根据项目Id\用户名\文件名查询提交记录
     */
    @Override
    public RuleProjectUserCommit queryByProjectIdAndUserNameAndFileName(Integer projectId, String userName, String fileName) {
        return this.ruleProjectUserCommitMapper.queryByProjectIdAndUserNameAndFileName(projectId, fileName, userName);
    }

    @Override
    public RuleProjectUserCommit queryByProjectIdAndFileName(Integer projectId, String userName, String fileName) {
        return this.ruleProjectUserCommitMapper.queryByProjectIdAndFileName(projectId, fileName, userName);
    }

    /**
     * 根据项目id\文件名查询最早的提交记录
     */
    @Override
    public RuleProjectUserCommit queryEarliestByProjectId(Integer projectId) {
        return this.ruleProjectUserCommitMapper.queryEarliestByProjectId(projectId);
    }

    /**
     * 根据节点名称更新为删除
     */
    public int markAsDeletedByNodeName(String nodeName) {
        return this.ruleProjectUserCommitMapper.markAsDeletedByNodeName(nodeName);
    }
}
