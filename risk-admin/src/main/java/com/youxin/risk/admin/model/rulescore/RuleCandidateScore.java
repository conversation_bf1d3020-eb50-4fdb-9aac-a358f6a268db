package com.youxin.risk.admin.model.rulescore;

import com.youxin.risk.commons.model.BaseModel;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 规则分的所有版本
 */
@Data
public class RuleCandidateScore extends BaseModel {
    private String ruleKey;
    private Integer ruleVersion;
    private String ruleName;
    private String code;
    /** 每个规则分的版本不一样 **/
    private String ruleRunCode;
    private String remark;

    private String createUser;
    private String modifyUser;
    /** 运行中、运行失败、待审批、审批中、待上线、已上线、已废弃、已下线 **/
    private Integer ruleStatus;

    /** 是否变量确认 **/
    private Integer isVarConfirm;

    private String taskId;
    private String resultTableName;
    private String taskUrl;
    private Date createTime;
    private Date workStartTime;
    /** 规则分是否被使用 **/
    private Integer ruleIsUsed;

    private List<String> ruleScoreRelations = new ArrayList<>();
    private List<RuleScoreStrategy> ruleScoreStrategies = new ArrayList<>();

    public static final Integer ruleStatusRunning = 1;//运行中
    public static final Integer ruleStatusFail = 2; //运行失败
    public static final Integer ruleWaitAudit = 3; //待审批
    public static final Integer ruleAudit = 4; //审批中
    public static final Integer ruleWaitOnline = 5; //待上线
    public static final Integer ruleOnline = 6; //上线
    public static final Integer ruleCancel = 7; //已废弃
    public static final Integer ruleOffline = 8; //下线


}
