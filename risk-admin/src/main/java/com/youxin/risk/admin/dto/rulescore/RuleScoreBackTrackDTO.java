package com.youxin.risk.admin.dto.rulescore;

import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class RuleScoreBackTrackDTO {
    private String ruleKey;
    private Integer ruleVersion;
    private String backTrackCode;
    /** 回溯数量 **/
    private Integer backTrackCount;
    /** 回溯时间 **/
    private Date backStartTime;
    private Date backEndTime;
    /** 比较的规则版本 **/
    private Integer compareRuleVersion;
    private String compareField;
    private String modifyUser;
    private List<RuleScoreStrategyDTO> ruleScoreStrategyDTOS = new ArrayList<>();
}
