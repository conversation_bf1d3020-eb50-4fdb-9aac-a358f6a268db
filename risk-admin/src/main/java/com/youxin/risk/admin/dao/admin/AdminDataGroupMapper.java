/*
 * Copyright (C) 2019 Baidu, Inc. All Rights Reserved.
 */
package com.youxin.risk.admin.dao.admin;

import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.ibatis.annotations.Param;

import com.youxin.risk.admin.dao.BaseMapper;
import com.youxin.risk.admin.model.AdminMatrixDataSource;
import com.youxin.risk.admin.model.AdminNodeData;
import com.youxin.risk.admin.model.AdminNodeDataParam;
import com.youxin.risk.admin.model.AdminNodeFeature;
import com.youxin.risk.admin.model.AdminNodeGroupData;
import com.youxin.risk.admin.model.AdminNodeStrategy;
import com.youxin.risk.admin.model.AdminProcessNode;

/**
 * <AUTHOR>
 * @date 2018/11/14 20:37
 */
public interface AdminDataGroupMapper extends BaseMapper<AdminNodeGroupData> {

    List<Map<String,Object>> selectPage(Map<String,Object> params);
    int selectAllCount(Map<String,Object> params);

    List<AdminNodeGroupData> selectAllNodeDataGroup();

    List<AdminNodeGroupData> selectNodeDataGroups(String nodeCode);

    List<AdminNodeGroupData> selectDataGroupByCode(String groupCode);

    void insertDataGroups(List<AdminNodeGroupData> list);

    void insertNodeDataGroup(AdminMatrixDataSource adminMatrixDataSource);

    List<Map<String,Object>> selectByDataCode(String dataCode);

    void deleteDataGroup(String groupCode);

    void deleteNodeDataGroup(String groupCode);

    void deleteNodeDataGroupByNode(@Param("nodeCode") String nodeCode, @Param("groupCode") String groupCode);

    AdminNodeGroupData selectNodeDataGroup(@Param("nodeCode") String nodeCode, @Param("groupCode") String groupCode);


}
