package com.youxin.risk.admin.service;

import com.youxin.risk.admin.model.AdminDictionary;
import com.youxin.risk.admin.model.AdminDictionaryExtend;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/11/23 14:32
 */
public interface AdminDictionaryService extends BaseService<AdminDictionary> {
    List<AdminDictionary> selectByCode(String dictCode);
    List<AdminDictionary> selectCodeList();
    List<AdminDictionaryExtend> selectDetailByCode(String dictCode);
    AdminDictionary selectByValue(String dictValue);
    AdminDictionary selectBySysValue(String dictValue);
    void update(AdminDictionary adminDictionary);
}
