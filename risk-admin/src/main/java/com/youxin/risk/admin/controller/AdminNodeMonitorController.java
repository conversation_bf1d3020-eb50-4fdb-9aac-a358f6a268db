package com.youxin.risk.admin.controller;

import com.youxin.risk.admin.interceptor.SystemLog;
import com.youxin.risk.admin.model.AdminNodeMonitorParam;
import com.youxin.risk.admin.service.AdminNodeMonitorService;
import com.youxin.risk.commons.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/nodeMonitor")
public class AdminNodeMonitorController extends BaseController{

    private final Logger logger = LoggerFactory.getLogger(AdminNodeMonitorController.class);

    @Autowired
    private AdminNodeMonitorService adminNodeMonitorService;

    /**
     * 查询审批人
     * @param param 参数Vo
     * @return ResponseEntity
     */
    @SystemLog
    @ResponseBody
    @RequestMapping(value = "query", method = RequestMethod.POST)
    public ResponseEntity<?> query(@RequestBody AdminNodeMonitorParam param) {
        try {
            if(StringUtils.isEmpty(param.getEventCode()) ||
                    StringUtils.isEmpty(param.getStep()) ||
                    StringUtils.isEmpty(param.getTimeGroup()) ||
                    StringUtils.isEmpty(param.getStartTime()) ||
                    StringUtils.isEmpty(param.getEndTime())){
                return buildErrorResponse("缺失参数");
            }
            if("5m".equals(param.getTimeGroup()) ||
                    "1h" .equals(param.getTimeGroup()) ||
                    "6h".equals(param.getTimeGroup()) ||
                    "1d".equals(param.getTimeGroup()) ||
                    "7d".equals(param.getTimeGroup())){
                return buildSuccessResponse(adminNodeMonitorService.query(param));
            }else{
                return buildErrorResponse("TimeGroup无效");
            }
        }catch(Exception e){
            logger.error("query node monitor error: ", e);
            return buildErrorResponse(e.getMessage());
        }
    }
}
