package com.youxin.risk.admin.apollo;

import com.ctrip.framework.apollo.ConfigService;
import com.ctrip.framework.apollo.model.ConfigChange;
import com.youxin.apollo.client.NacosClient;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.youxin.risk.admin.service.WechatAlertService;
import com.youxin.risk.commons.constants.ApolloNamespace;
import com.youxin.risk.commons.tools.lock.LockResult;
import com.youxin.risk.commons.tools.lock.RedisLock;
import com.youxin.risk.commons.utils.LoggerProxy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * 监听risk-all空间变动，发送企业消息
 * <AUTHOR>
 */
@Component
public class ApolloConfigListener{
    private static final Logger logger = LoggerFactory.getLogger(ApolloConfigListener.class);
    @Resource
    private WechatAlertService wechatAlertService;

    @Resource
    private RedisLock redisLock;

    @PostConstruct
    public void listenChange(){
        ConfigService.getConfig(ApolloNamespace.riskAllSpace).addChangeListener(changeEvent -> {
            String lockKey = "riskAll.listener.weChatKey";
            LockResult result = null;
            try {
                result = redisLock.tryLock(lockKey, 5);
                if (!result.isSuccess()) {
                    return;
                }

                LoggerProxy.info("ApolloConfigListener",logger,"apollo namespace:{} change", changeEvent.getNamespace());
                if (!ApolloNamespace.riskAllSpace.equals(changeEvent.getNamespace())) {
                    return;
                }

                StringBuilder builder = new StringBuilder();
                builder.append("risk-all 配置变动：").append("\n");
                for (String key : changeEvent.changedKeys()) {
                    ConfigChange change = changeEvent.getChange(key);
                    builder.append("key: ").append(change.getPropertyName())
                            .append(",oldValue: ").append(change.getOldValue())
                            .append(",newValue: ").append(change.getNewValue())
                            .append("\n");
                    LoggerProxy.info("ApolloConfigListener",logger,"apollo Found change namespace:{}, key: {}, oldValue: {}, newValue: {}, changeType: {}",
                            changeEvent.getNamespace(), change.getPropertyName(), change.getOldValue(), change.getNewValue(), change.getChangeType());
                }

                String weChatKey = NacosClient.getByNameSpace(ApolloNamespace.riskAllSpace,"riskAll.listener.wechatkey","");
                // 发送企微消息
                wechatAlertService.sendText(builder.toString(), weChatKey);
            } catch (Exception e){
                LoggerProxy.error("risAllApolloConfigListener",logger,"e :{}",e);
            } finally {
                if (result != null && result.isSuccess()){
                    redisLock.releaseLock(lockKey, result.getLockId());
                }
            }
        });

    }

    @PostConstruct
    public void gateWayListenChange(){
        ConfigService.getConfig(ApolloNamespace.gwSpace).addChangeListener(changeEvent -> {
            String lockKey = "riskGateway.listener.weChatKey";
            LockResult result = redisLock.tryLock(lockKey, 5);
            if (result.isSuccess()){
                try {
                    LoggerProxy.info("ApolloConfigListener",logger,"apollo namespace:{} change", changeEvent.getNamespace());
                    if (!ApolloNamespace.gwSpace.equals(changeEvent.getNamespace())) {
                        return;
                    }

                    StringBuilder builder = new StringBuilder();
                    builder.append("risk-gateway配置变动：").append("\n");
                    for (String key : changeEvent.changedKeys()) {
                        ConfigChange change = changeEvent.getChange(key);
                        String oldValue = change.getOldValue();
                        String newValue = change.getNewValue();
                        try {
                            builder.append("key: ").append(change.getPropertyName()).append("\n")
                                    .append("oldValue: ").append(change.getOldValue()).append("\n")
                                    .append("newValue: ").append(change.getNewValue()).append("\n")
                                    .append("变更配置： ").append(Maps.difference(JSONObject.parseObject(oldValue),JSONObject.parseObject(newValue)).entriesDiffering());
                            LoggerProxy.info("ApolloConfigListener",logger,"apollo Found change namespace:{}, key: {}, oldValue: {}, newValue: {}, changeType: {}",
                                    changeEvent.getNamespace(), change.getPropertyName(), change.getOldValue(), change.getNewValue(), change.getChangeType());
                        }catch (Exception e){
                            builder.append("key: ").append(change.getPropertyName()).append("\n")
                                    .append("oldValue: ").append(change.getOldValue()).append("\n")
                                    .append("newValue: ").append(change.getNewValue()).append("\n");
                            LoggerProxy.info("ApolloConfigListener",logger,"apollo Found change namespace:{}, key: {}, oldValue: {}, newValue: {}, changeType: {}",
                                    changeEvent.getNamespace(), change.getPropertyName(), change.getOldValue(), change.getNewValue(), change.getChangeType());
                        }
                    }

                    String weChatKey = NacosClient.getByNameSpace(ApolloNamespace.gwSpace,"riskGateway.listener.wechatkey","");
                    // 发送企微消息
                    wechatAlertService.sendText(builder.toString(), weChatKey);
                }catch (Exception e){
                    LoggerProxy.error("riskGatewayApolloConfigListener",logger,"e :{}",e);
                }finally {
                    if (result.isSuccess()){
                        redisLock.releaseLock(lockKey,result.getLockId());
                    }
                }

            }
        });

    }

}
