//package com.youxin.risk.alert.test;
//
//import com.youxin.risk.alert.job.v4.RiskMetricMonitorJob;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.test.context.ContextConfiguration;
//import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//
//@RunWith(SpringJUnit4ClassRunner.class)
//@ContextConfiguration("classpath:spring/spring-config.xml")
//public class MetricJobTest {
//
////    @Autowired
////    CountMetricDataJob countMetricDataJob;
//
//    @Autowired
//    RiskMetricMonitorJob riskMetricMonitorJob;
//
//    @Test
//    public void testMiss() {
//        String param = "{\"startTime\": \"2023-05-25 09:00:00\",\"endTime\": \"2023-05-25 10:00:00\"," +
//                "\"features\": \"rh_credit_sd,dz_rh_credit_sd,pboc_v3_sd,dz_pboc_v3_sd,pboc_v5_sd,dz_pboc_v5_sd,heika_feature_sd,dz_heika_feature_sd,xw_cache_pboc_v3_sd\"," +
//                "\"featureItems\": \"yxGrade1,yxGrade2,yxGrade3,yxGrade4,yxGrade5,yxGrade6,yxGrade7,yxGrade8,yxGrade9,yxGrade10,yxGrade11,yxGrade12\"," +
//                "\"type\":\"miss\",\"force\": true,\"alert\": true, \"featureItemValues\":\"\"}";
//        countMetricDataJob.execJobHandler(param);
//    }
//
//    @Test
//    public void testAverage() {
//        String param = "{\"startTime\": \"2023-05-25 09:00:00\",\"endTime\": \"2023-05-25 10:00:00\"," +
//                "\"features\": \"rh_credit_sd,dz_rh_credit_sd,pboc_v3_sd,dz_pboc_v3_sd,pboc_v5_sd,dz_pboc_v5_sd,heika_feature_sd,dz_heika_feature_sd,xw_cache_pboc_v3_sd\"," +
//                "\"featureItems\": \"yxGrade1,yxGrade2,yxGrade3,yxGrade4,yxGrade5,yxGrade6,yxGrade7,yxGrade8,yxGrade9,yxGrade10,yxGrade11,yxGrade12\"," +
//                "\"type\":\"average\",\"force\": true,\"alert\": true, \"featureItemValues\":\"9.223372036854776e+18\"}";
//        countMetricDataJob.execJobHandler(param);
//    }
//
//    @Test
//    public void testMissRate() {
//        String param = "{\"robotId\": \"f47b241c-35e9-473e-9442-0876e048d5f3\",\"threshold\": 0.2," +
//                "\"features\": \"rh_credit_sd,dz_rh_credit_sd,pboc_v3_sd,dz_pboc_v3_sd,pboc_v5_sd,dz_pboc_v5_sd,heika_feature_sd,dz_heika_feature_sd,xw_cache_pboc_v3_sd\"," +
//                "\"featureItems\": \"yxGrade1,yxGrade2,yxGrade3,yxGrade4,yxGrade5,yxGrade6,yxGrade7,yxGrade8,yxGrade9,yxGrade10,yxGrade11,yxGrade12\"," +
//                "\"type\":\"miss\",\"force\": true,\"alert\": true}";
//        riskMetricMonitorJob.execJobHandler(param);
//    }
//
//    @Test
//    public void testAverageRate() {
//        String param = "{\"robotId\": \"f47b241c-35e9-473e-9442-0876e048d5f3\",\"threshold\": 0.2," +
//                "\"features\": \"rh_credit_sd,dz_rh_credit_sd,pboc_v3_sd,dz_pboc_v3_sd,pboc_v5_sd,dz_pboc_v5_sd,heika_feature_sd,dz_heika_feature_sd,xw_cache_pboc_v3_sd\"," +
//                "\"featureItems\": \"yxGrade1,yxGrade2,yxGrade3,yxGrade4,yxGrade5,yxGrade6,yxGrade7,yxGrade8,yxGrade9,yxGrade10,yxGrade11,yxGrade12\"," +
//                "\"type\":\"average\",\"force\": true,\"alert\": true, \"endTime\":\"2023-06-07 18:00:00\"}";
//        riskMetricMonitorJob.execJobHandler(param);
//    }
//
//}
