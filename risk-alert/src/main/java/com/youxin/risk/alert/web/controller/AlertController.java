package com.youxin.risk.alert.web.controller;

import com.google.common.util.concurrent.RateLimiter;
import com.youxin.risk.alert.constants.AlertSourceEnum;
import com.youxin.risk.alert.model.AlertEntity;
import com.youxin.risk.alert.sender.impl.RobotAlertSender;
import com.youxin.risk.alert.service.AlertMessageService;
import com.youxin.risk.alert.vo.AlertEvent;
import com.youxin.risk.commons.cache.CacheApi;
import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.model.AlertPolicy;
import com.youxin.risk.commons.utils.GlobalUtil;
import com.youxin.risk.commons.utils.LogUtil;
import com.youxin.risk.commons.utils.LoggerProxy;
import org.apache.ibatis.annotations.Param;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

@RestController
public class AlertController {

    private Logger logger = LoggerFactory.getLogger(AlertController.class);

    private RateLimiter rateLimiter = RateLimiter.create(50);

    @Resource
    private AlertMessageService alertMessageService;

    @Resource(name = "RobotAlertSender")
    private RobotAlertSender sender;

    private static final String RET_CODE_FAILED = RetCodeEnum.FAILED.getValue();
    private static final String RET_CODE_SUCCESS = RetCodeEnum.SUCCESS.getValue();

    @SuppressWarnings("unchecked")
    @ResponseBody
    @RequestMapping(value = "/alert/api/event/{source}/v1", method = RequestMethod.POST)
    public String alert(@PathVariable("source") String source, @RequestBody String message) {

        String alertId = GlobalUtil.getGlobalId("alert_");
        LogUtil.bindLogId(alertId);

        LoggerProxy.info("alertMessageRecv", logger, "source={},message={}", source, message);

        if (!rateLimiter.tryAcquire(50, TimeUnit.MILLISECONDS)) {
            LoggerProxy.warn("alertMessageLimitCheck", logger, "");
            return RET_CODE_FAILED;
        }

        AlertSourceEnum sourceEnum = AlertSourceEnum.getAlertSource(source);
        if (null == sourceEnum) {
            LoggerProxy.info("alertMessageCheck", logger, "source=" + source);
            return RET_CODE_FAILED;
        }
        try {
            AlertEvent alertEvent = new AlertEvent(sourceEnum, message);
            alertEvent.setMsgType(sourceEnum.getMstType());
            alertEvent.setAlertId(alertId);
            alertMessageService.service(alertEvent);
            return RET_CODE_SUCCESS;
        } catch (Exception e) {
            LoggerProxy.error(getClass().getSimpleName() + "Exception", logger, "", e);
            return RET_CODE_FAILED;
        } finally {
            LogUtil.unbindLogId();
        }
    }

    @ResponseBody
    @RequestMapping(value = "/alert/robot", method = RequestMethod.POST)
    public String robotKeyAlert(@RequestBody AlertEntity alertEntity){
        try {
            AlertPolicy policy = new AlertPolicy();
            policy.setRobotKey(alertEntity.getRobotKey());
            policy.setPolicyName(alertEntity.getPolicyName());
            AlertEvent alertEvent = new AlertEvent(AlertSourceEnum.riskAlert, alertEntity.getContent());
            alertEvent.setTitle(alertEntity.getTitle());
            alertEvent.setAlertPolicy(policy);
            sender.send(alertEvent);
            return RET_CODE_SUCCESS;
        }catch (Exception e) {
            LoggerProxy.error(getClass().getSimpleName() + "Exception", logger, "", e);
            return RET_CODE_FAILED;
        }
    }

    @RequestMapping(value = "/risk/sysconfig/value", method = {RequestMethod.GET})
    @ResponseBody
    public String getSysconfigValue(String key) {
        try{
            return CacheApi.getDictSysConfig(key);
        }catch (Exception e){
            logger.error(e.getMessage(),e);
            return e.getMessage();
        }
    }
}