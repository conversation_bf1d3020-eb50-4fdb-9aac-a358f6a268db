package com.youxin.risk.alert.filter;

/**
 * 报警过滤器管理抽象接口
 *
 * <AUTHOR>
 */
public interface AlertFilterManager<T> {

    /**
     * 添加过滤器
     *
     * @param filter 过滤器
     */
    void addFilter(IAlertFilter<T> filter);

    /**
     * 移除过滤器
     *
     * @param filter 过滤器
     */
    void removeFilter(IAlertFilter<T> filter);

    /**
     * 判断该条报警信息是否被过滤
     *
     * @param alertMessage 报警信息
     * @return true or false
     */
    boolean isFiltered(T alertMessage);
}
