package com.youxin.risk.alert.mapper;

import com.youxin.risk.alert.model.FeatureCostTime;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 特征耗时Mapper
 *
 * <AUTHOR>
 */
public interface FeatureCostTimeMapper {

    int insert(FeatureCostTime featureCostTime);

    /**
     * 根据特征名，步骤分组求近5分钟的平均耗时
     * @return 耗时
     */
    List<FeatureCostTime> getMeanCostTime(@Param("time") Integer time);

    int delete(@Param("time") Integer time);
}
