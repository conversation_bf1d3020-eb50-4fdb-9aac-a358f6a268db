package com.youxin.risk.alert.controller;

import com.alibaba.fastjson.JSON;
import com.youxin.risk.alert.grafana.GrafanaAlertService;
import com.youxin.risk.alert.grafana.dto.GrafanaAlertMessage;
import com.youxin.risk.commons.utils.LoggerProxy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * grafana监控调用Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/grafana")
public class GrafanaController {
    private static final Logger LOGGER = LoggerFactory.getLogger(GrafanaController.class);

    @Autowired
    private GrafanaAlertService grafanaAlertService;

    @RequestMapping(value = "/v1/event", method = RequestMethod.POST)
    public void alert(@RequestBody String requestBody) {
        LoggerProxy.info("receiveGrafanaAlert", LOGGER, "requestBody={}", requestBody);
        GrafanaAlertMessage alertMessage = JSON.parseObject(requestBody, GrafanaAlertMessage.class);
        grafanaAlertService.handleAlert(alertMessage);
    }
}
