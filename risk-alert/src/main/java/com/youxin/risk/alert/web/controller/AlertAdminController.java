package com.youxin.risk.alert.web.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.util.concurrent.RateLimiter;
import com.youxin.risk.alert.influxdb.InfluxDBAPI;
import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.utils.GlobalUtil;
import com.youxin.risk.commons.utils.JacksonUtil;
import com.youxin.risk.commons.utils.LogUtil;
import com.youxin.risk.commons.utils.LoggerProxy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;
import java.util.concurrent.TimeUnit;

@RestController
public class AlertAdminController {

    private Logger logger = LoggerFactory.getLogger(AlertAdminController.class);

    private RateLimiter rateLimiter = RateLimiter.create(5);

    private static enum AlertCommandEnum {
        getDatasourceNames,
        getTableNames,
        getFunctionFields,
        getTagKeys,
        getTagValues;

        private static AlertCommandEnum getAlertCommandEnum(String command) {
            try {
                return AlertCommandEnum.valueOf(command);
            } catch (Exception e) {
                return null;
            }
        }
    }

    @SuppressWarnings("unchecked")
    @ResponseBody
    @RequestMapping(value = "/alert/admin/{command}", method = RequestMethod.POST)
    public String alert(@PathVariable("command") String command, @RequestBody String message) {
        LogUtil.bindLogId(GlobalUtil.getGlobalId());
        LoggerProxy.info("alertAdminMessageRecv", logger, "message={}", message);
        Map<String, Object> ret = new HashMap<>();
        try {

            if (!rateLimiter.tryAcquire(100, TimeUnit.MILLISECONDS)) {
                ret.put("retCode", RetCodeEnum.RATE_LIMIT.getValue());
                ret.put("retMsg", RetCodeEnum.RATE_LIMIT.getRetMsg());
                return JacksonUtil.toJson(ret);
            }

            JSONObject requestJson = JSONObject.parseObject(message);
            String requestId = requestJson.getString("requestId");
            LogUtil.bindLogId(requestId);
            if (null == AlertCommandEnum.getAlertCommandEnum(command)) {
                ret.put("retCode", RetCodeEnum.ILLEGAL_ARGUMENT.getValue());
                ret.put("retMsg", RetCodeEnum.ILLEGAL_ARGUMENT.getRetMsg());
                return JacksonUtil.toJson(ret);
            }
            Set<String> set = new TreeSet<>();
            List<String> list = command(AlertCommandEnum.getAlertCommandEnum(command), requestJson);
            set.addAll(list);
            ret.put("result", set);
            ret.put("retCode", RetCodeEnum.SUCCESS.getValue());
            ret.put("retMsg", RetCodeEnum.SUCCESS.getRetMsg());
        } catch (Exception e) {
            LoggerProxy.error(getClass().getSimpleName() + "Exception", logger, "", e);
            ret.put("retCode", RetCodeEnum.FAILED.getValue());
            ret.put("retMsg", RetCodeEnum.FAILED.getRetMsg(e.getMessage()));
        } finally {
            LogUtil.unbindLogId();
        }
        return JacksonUtil.toJson(ret);
    }

    private List<String> command(AlertCommandEnum commandEnum, JSONObject req) {
        String dbName = req.getString("datasourceName");
        String tableName = req.getString("tableName");
        String tag = req.getString("tag");
        switch (commandEnum) {
            case getDatasourceNames:
                return getDBS();
            case getTableNames:
                return getTables(dbName);
            case getTagKeys:
                return getTagKeys(dbName, tableName);
            case getTagValues:
                return getTagValues(dbName, tableName, tag);
            case getFunctionFields:
                return getFileds(dbName, tableName);
            default:
                throw new IllegalArgumentException("'command'");
        }
    }

    private List<String> getFileds(String dbName, String tableName) {
        // columns
        List<String> list = new ArrayList<>();
        JSONObject response = JSONObject.parseObject(InfluxDBAPI.queryNow(dbName, tableName));
        JSONArray results = response.getJSONArray("results");
        if (null == results || results.isEmpty()) {
            return list;
        }
        JSONArray series = results.getJSONObject(0).getJSONArray("series");
        if (null == series || series.isEmpty()) {
            return list;
        }
        JSONArray columns = series.getJSONObject(0).getJSONArray("columns");
        if (null == columns || columns.isEmpty()) {
            return list;
        }
        List<String> tagKeys = getTagKeys(dbName, tableName);
        for (int i = 0; i < columns.size(); i++) {
            if ("time".equals(columns.getString(i)) || tagKeys.contains(columns.getString(i))) {
                continue;
            }
            list.add(columns.getString(i));
        }
        return list;
    }

    private List<String> getTagValues(String dbName, String tableName, String tag) {
        List<String> dblist = new ArrayList<>();
        JSONArray values = parseValues(InfluxDBAPI.showTagValues(dbName, tableName, tag));
        if (null == values || values.isEmpty()) {
            return dblist;
        }
        for (int i = 0; i < values.size(); i++) {
            JSONArray dns = values.getJSONArray(i);
            dblist.add(dns.getString(1));
        }
        return dblist;
    }

    private List<String> getTagKeys(String dbName, String tableName) {
        List<String> dblist = new ArrayList<>();
        JSONArray values = parseValues(InfluxDBAPI.showTagKeys(dbName, tableName));
        if (null == values || values.isEmpty()) {
            return dblist;
        }
        for (int i = 0; i < values.size(); i++) {
            JSONArray dns = values.getJSONArray(i);
            dblist.add(dns.getString(0));
        }
        return dblist;
    }

    private List<String> getTables(String dbName) {
        List<String> dblist = new ArrayList<>();
        JSONArray values = parseValues(InfluxDBAPI.showMeasurements(dbName));
        if (null == values || values.isEmpty()) {
            return dblist;
        }
        for (int i = 0; i < values.size(); i++) {
            JSONArray dns = values.getJSONArray(i);
            dblist.add(dns.getString(0));
        }
        return dblist;
    }

    private List<String> getDBS() {
        List<String> dblist = new ArrayList<>();
        JSONArray values = parseValues(InfluxDBAPI.showDatabases());
        if (null == values || values.isEmpty()) {
            return dblist;
        }
        for (int i = 0; i < values.size(); i++) {
            JSONArray dns = values.getJSONArray(i);
            if (!"_internal".equals(dns.getString(0))) {
                dblist.add(dns.getString(0));
            }
        }
        return dblist;
    }

    private JSONArray parseValues(String influxdbResponse) {
        JSONObject response = JSONObject.parseObject(influxdbResponse);
        JSONArray results = response.getJSONArray("results");
        if (null == results || results.isEmpty()) {
            return null;
        }
        JSONArray series = results.getJSONObject(0).getJSONArray("series");
        if (null == series || series.isEmpty()) {
            return null;
        }
        JSONArray values = series.getJSONObject(0).getJSONArray("values");
        return values;
    }

    public static void main(String[] args) {
        AlertAdminController a = new AlertAdminController();
        List<String> res = a.getTagValues("renrendai_antifraud_risk", "risk_di_callService", "service");
        System.out.println(res);


    }
}