<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:hdp="http://www.springframework.org/schema/hadoop"
    xsi:schemaLocation="http://www.springframework.org/schema/hadoop http://www.springframework.org/schema/hadoop/spring-hadoop.xsd
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">
    
    <import resource="classpath:spring/spring-hadoop.xml" />     
        
    <hdp:hbase-configuration id="hbaseConfiguration" properties-location="classpath:conf/hbase.conf"
        delete-connection="false" zk-port="${zookeeper.port}" zk-quorum="${zookeeper.quorum}" />
        
    <bean id="hbaseTemplate" name="hbaseTemplate" class="org.springframework.data.hadoop.hbase.HbaseTemplate">
        <property name="configuration" ref="hbaseConfiguration" />
    </bean>

<!--    <hdp:hbase-configuration id="hbaseSlaveConfiguration" properties-location="classpath:conf/hbase.conf"-->
<!--                             delete-connection="false" zk-port="${zookeeper.port}" zk-quorum="${slave.zookeeper.quorum}" />-->

<!--    <bean id="hbaseSlaveTemplate" name="hbaseSlaveTemplate" class="org.springframework.data.hadoop.hbase.HbaseTemplate">-->
<!--        <property name="configuration" ref="hbaseSlaveConfiguration" />-->
<!--    </bean>-->
</beans>
