package com.paydayloan.verify.vo;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.List;

public class AccountPayInfoVo {
	private Double amount;
	private Double principal;
	private Double interest;
	private Double serviceFee;
	private Double overdueInterest;
	private Double overdueMgmnFee;
	private Double inRepayFee;
	private Double otherFee;
	private List<AccountPayDetailsVo> details;

	public Double getAmount() {
		return amount;
	}

	public void setAmount(Double amount) {
		this.amount = amount;
	}

	public Double getPrincipal() {
		return principal;
	}

	public void setPrincipal(Double principal) {
		this.principal = principal;
	}

	public Double getInterest() {
		return interest;
	}

	public void setInterest(Double interest) {
		this.interest = interest;
	}

	public Double getServiceFee() {
		return serviceFee;
	}

	public void setServiceFee(Double serviceFee) {
		this.serviceFee = serviceFee;
	}

	public Double getOverdueInterest() {
		return overdueInterest;
	}

	public void setOverdueInterest(Double overdueInterest) {
		this.overdueInterest = overdueInterest;
	}

	public Double getOverdueMgmnFee() {
		return overdueMgmnFee;
	}

	public void setOverdueMgmnFee(Double overdueMgmnFee) {
		this.overdueMgmnFee = overdueMgmnFee;
	}

	public Double getInRepayFee() {
		return inRepayFee;
	}

	public void setInRepayFee(Double inRepayFee) {
		this.inRepayFee = inRepayFee;
	}

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this,
				ToStringStyle.SHORT_PREFIX_STYLE);
	}

	public Double getOtherFee() {
		return otherFee;
	}

	public void setOtherFee(Double otherFee) {
		this.otherFee = otherFee;
	}

	public List<AccountPayDetailsVo> getDetails() {
		return details;
	}

	public void setDetails(List<AccountPayDetailsVo> details) {
		this.details = details;
	}

}
