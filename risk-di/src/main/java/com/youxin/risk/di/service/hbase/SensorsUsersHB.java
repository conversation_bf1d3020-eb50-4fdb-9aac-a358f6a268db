package com.youxin.risk.di.service.hbase;

import org.apache.hadoop.hbase.Cell;
import org.apache.hadoop.hbase.HConstants;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.util.Bytes;
import org.springframework.data.annotation.Transient;


/**
 * <AUTHOR>
 * @date 2021/7/16 10:12
 * @desc 神策 users hbase表
 */
public class SensorsUsersHB implements HbaseTable {

    @Transient
    public String family = "user";
    @Transient
    public byte[] familyBytes = Bytes.toBytes(family);

    private String rowKey;

    private long firstVisitTime;


    public SensorsUsersHB(String rowKey,long firstVisitTime) {
        this.rowKey = rowKey;
        this.firstVisitTime = firstVisitTime;
    }

    public SensorsUsersHB(String rowKey) {
        this.rowKey = rowKey;
    }

    public SensorsUsersHB() {}


    @Override
    public String getTableName() {
        return "sensors_users";
    }

    @Override
    public Put buildPut() {
        byte[] rowKey = Bytes.toBytes(this.rowKey);
        Put put = new Put(rowKey);
        put.addColumn(familyBytes, Bytes.toBytes("firstVisitTime"), Bytes.toBytes(String.valueOf(firstVisitTime)));
        return put;
    }

    @Override
    public Scan buildScan() {
        return null;
    }

    @Override
    public void readValues(ResultScanner resultScanner) {

    }


    @Override
    public void readValue(Result value) {
        for (Cell cell : value.listCells()) {
            //值
            String data = Bytes.toString(cell.getValueArray(),
                    cell.getValueOffset(), cell.getValueLength());
            this.firstVisitTime = Long.parseLong(data);
        }
    }

    @Override
    public void readSimpleValue(Result value) {}

    @Override
    public Get buildGet(String rowKeyStr) {
        Get get = new Get(Bytes.toBytes(rowKeyStr));
        get.setPriority(HConstants.HIGH_QOS);
        get.setLoadColumnFamiliesOnDemand(true);
        get.addFamily(familyBytes);
        return get;
    }

    @Override
    public String getRowKey() {
        return rowKey;
    }

    public void setRowKey(String rowKey) {
        this.rowKey = rowKey;
    }

    public long getFirstVisitTime() {
        return firstVisitTime;
    }

    public void setFirstVisitTime(long firstVisitTime) {
        this.firstVisitTime = firstVisitTime;
    }
}
