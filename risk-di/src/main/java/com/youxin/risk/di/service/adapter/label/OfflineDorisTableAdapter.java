package com.youxin.risk.di.service.adapter.label;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.youxin.risk.commons.adapter.di.ServiceAdapter;
import com.youxin.risk.commons.adapter.di.ServiceRequest;
import com.youxin.risk.commons.adapter.di.ServiceResponse;
import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

public class OfflineDorisTableAdapter extends ServiceAdapter {

    private static final Logger LOGGER = LoggerFactory.getLogger(OfflineDorisTableAdapter.class);

    private static final Integer ERROR_CODE = 500;
    @Override
    protected ServiceResponse callService(ServiceRequest request) {

        ServiceResponse response = new ServiceResponse();
        response.setRequestId(request.getRequestId());

        String loanKey = request.getLoanKey();
        String userKey = request.getUserKey();
        String step = request.getStep();
        String eventCode = request.getSystemEventCode();

        String serviceCode = request.getServiceCode();
        LoggerProxy.info("OfflineDorisTableAdapter_callService_param", LOGGER,
                "loanKey={} eventCode = {} step = {} userKey={} serviceCode = {} url = {}",loanKey,eventCode,step,userKey,serviceCode,diService.getServiceUrl());
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("loanKey",loanKey);
        jsonObject.put("eventCode",eventCode);
        jsonObject.put("step",step);
        jsonObject.put("userKey",userKey);
        jsonObject.put("serviceCode", serviceCode);
        jsonObject.put("registerAccount","risk");

        String result = SyncHTTPRemoteAPI.postJson(diService.getServiceUrl(), JSON.toJSONString(jsonObject), diService.getServiceTimeout().intValue());
        LoggerProxy.info("OfflineDorisTableAdapter_callService_result", LOGGER, "loanKey={} result = {} ",loanKey,result);

        JSONObject data = JSONObject.parseObject(result);
        if (data.getInteger("code") == 500){
            response.setRetCode(RetCodeEnum.RESPONSE_EXCEPTION.getValue());
            response.setRetMsg(RetCodeEnum.RESPONSE_EXCEPTION.getRetMsg());
            return response;
        }
        if (!data.containsKey("data") || StringUtils.isEmpty(data.getString("data"))){
            response.setRetCode(RetCodeEnum.NO_DATA.getValue());
            response.setRetMsg(RetCodeEnum.NO_DATA.getRetMsg());
            return response;
        }else {
            response.setRetCode(RetCodeEnum.SUCCESS.getValue());
            response.setRetMsg(RetCodeEnum.SUCCESS.getRetMsg());
            Map<String, Object> resultMap = Maps.newHashMap();
            resultMap.put("data", data.getString("data"));
            response.setResult(resultMap);
            response.setData(data.getString("data"));
        }
        return response;
    }
}
