package com.youxin.risk.di.service.mongo;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.utils.JacksonUtil;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.StringManager;
import com.youxin.risk.di.common.Constant;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.RetryCallback;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class MobileAreaService {


    private static final Logger logger = LoggerFactory.getLogger(MobileAreaService.class);

    @Autowired
    private MobileAreaModelDao dao;
    @Autowired
    private RedissonClient redissonClient;

    private static String url360 = "http://cx.shouji.360.cn/phonearea.php?number=";
    private static final String PHONE_BLACKLIST_FOR_PHONE_BOOK = "PHONE_BOOK_BLACKLIST";
    private static RetryTemplate retry = new RetryTemplate();
    private final static StringManager SM = StringManager.getManager(Constant.CONF_PATH);
    private static String dcInsideUrl = SM.getString("dc.inside.url");

    ThreadPoolExecutor threadPool = new ThreadPoolExecutor(50, 100, 0, TimeUnit.MINUTES,
            new ArrayBlockingQueue<Runnable>(10000), Executors.defaultThreadFactory(), new ThreadPoolExecutor.CallerRunsPolicy());


   /* @Cacheable(cacheNames = "model.area_mapping", key = "#mobile.substring(0,7)",
            condition = "#mobile!=null and #mobile.length()>=7", unless = "#result==null")*/
    public MobileAreaModel getMobileArea(@PathVariable String mobile) {
        String id = mobile;
        if(id.length()>7){
            id = mobile.substring(0, 7);
        }else{
            logger.info("Mobile phone number is not standard ,mobile:{} ",mobile);
            return null;
        }
        MobileAreaModel model = this.dao.find(id);
        logger.info("Scan mobile:{} in mongo,section：{},model is not exist:{}", mobile, id, null == model);
        if (model == null) {

       /*     Redisson redisson = (Redisson)redissonClient;
            for(MasterSlaveEntry masterSlaveEntry :redisson.getConnectionManager().getEntrySet()){
                for(ClientConnectionsEntry clientConnectionsEntry:masterSlaveEntry.getAllEntries()){
                    clientConnectionsEntry
                }
            }*/
            try {
                //判断黑名单是否有该号码  没有才请求
                if(!redissonClient.getSet(PHONE_BLACKLIST_FOR_PHONE_BOOK).contains(mobile)){
                    logger.info("mobile miss in mongo,mobile:{} doRest()", mobile);
                    model = this.mobile360(mobile);
                }
            } catch (Exception ex) {
                //请求报错手机号，添加到黑名单
                redissonClient.getSet(PHONE_BLACKLIST_FOR_PHONE_BOOK).add(mobile);
                logger.error("请求360手机号归属地接口异常,mobile:{}", mobile, ex);
            }
        }
        return model;
    }
    private MobileAreaModel mobile360(String mobile) throws Exception {
        String res = retry.execute((RetryCallback<String, Exception>) context -> SyncHTTPRemoteAPI.get(url360 + mobile, 5000));
        if (StringUtils.isBlank(res)) {
            return null;
        }
        JSONObject json = JSONObject.parseObject(StringEscapeUtils.unescapeJava(res));
        MobileAreaModel model = json.getJSONObject("data").toJavaObject(MobileAreaModel.class);
        if (model == null) {
            return model;
        }
        if (StringUtils.isBlank(model.getCity())) {
            model.setCity(model.getProvince());
        }
        model.setMobile(mobile.substring(0, 7));
        model.setCreateTime(new Date());
        model.setUpdateTime(model.getCreateTime());
        model.setSource("360");
        this.dao.save(model);
        return model;
    }

    public List<MobileAreaModel> getMobileAreaMappings(String[] mobiles) {
        if (ArrayUtils.isEmpty(mobiles)) {
            return Collections.EMPTY_LIST;
        }
        ForkJoinPool pool = new ForkJoinPool(mobiles.length);
        try {
            return pool.submit(() -> Arrays.stream(mobiles).parallel().map(it -> {
                MobileAreaModel model = this.getMobileArea(it);
                if (model != null) {
                    model.setMobile(it);
                }
                return model;
            }).collect(Collectors.toList())).get();
        } catch (InterruptedException | ExecutionException e) {
            logger.error("批量获取手机号归属地异常", e);
            return Collections.EMPTY_LIST;
        }
    }

    /**
     * 获取手机号段对应地址
     * @param mobiles
     * @return
     */
    public List<MobileAreaModel> getMobileAreaNew(List<String> mobiles) {
        if (CollectionUtils.isEmpty(mobiles)) {
            return Collections.emptyList();
        }

        JSONObject params = new JSONObject();
        params.put("mobiles", mobiles);
        params.put("systemid", "HAO_HUAN");
        String result = SyncHTTPRemoteAPI.postJson(dcInsideUrl+ Constant.DC_MOBILE_AREA, params.toJSONString(), 10000);

        if (StringUtils.isBlank(result)) {
            logger.error("getMobileAreaNew getMobileArea error, result is empty, params: {}, mobiles: {}", params.toJSONString(), mobiles);
            return Collections.emptyList();
        }
        final JSONObject resultObj = JSONObject.parseObject(result);
        if (Objects.isNull(resultObj)) {
            logger.error("getMobileAreaNew getMobileArea error, resultObj is empty, params: {}, mobiles: {}", params.toJSONString(), mobiles);
            return Collections.emptyList();
        }
        final JSONArray records = resultObj.getJSONArray("records");
        if (CollectionUtils.isEmpty(records)) {
            logger.error("getMobileAreaNew getMobileArea error, records is empty, params: {}, mobiles: {}", params.toJSONString(), mobiles);
            return Collections.emptyList();
        }

        try {
             return JSON.parseArray(records.toJSONString(), MobileAreaModel.class);
        } catch (Exception ex) {
            logger.error("getMobileAreaNew parse object error result:{}", records.toJSONString(), ex);
        }

        return Collections.emptyList();
    }


    /**
     * 并发获取手机号
     * @param mobiles
     */
    public List<MobileAreaModel> parallelGetMobile(List<String> mobiles, int batchSize) {
        if (CollectionUtils.isEmpty(mobiles)) {
            return Collections.emptyList();
        }

        List<List<String>> list = Lists.partition(mobiles, batchSize);
        CountDownLatch countDownLatch = new CountDownLatch(list.size());
        List<MobileAreaModel> result = Lists.newArrayList();
        for (List<String> l : list) {
            threadPool.execute(() -> {
                try{
                    result.addAll(this.getMobileAreaNew(l));
                }catch (Exception e){
                    LoggerProxy.error("parallelGetMobileError", logger, "查询手机号归属地错误, request={}", JacksonUtil.toJson(l),e);
                }finally {
                    countDownLatch.countDown();
                }
            });
        }

        try {
            countDownLatch.await(30,TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            LoggerProxy.error("parallelGetMobileError", logger, "主线程被打断",e);
        }

        return result;
    }
}
