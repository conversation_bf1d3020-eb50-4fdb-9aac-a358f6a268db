package com.youxin.risk.di.utils;

import cn.hutool.core.convert.Convert;
import com.youxin.risk.di.common.VariableValueTypeEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 变量类型处理类
 * <AUTHOR>
 * @date 2023年06月09日 下午4:33
 */
public class ValueTypeUtil {
     private static final Logger LOGGER = LoggerFactory.getLogger(CreditUtils.class);


     public static Object parseValue(VariableValueTypeEnum valueTypeEnum, Object value, Object defaultValue) {
          if (value == null) {
               return parseValue(valueTypeEnum, defaultValue, valueTypeEnum.getDefaultValue());
          }

          Object val;
          try {
               switch (valueTypeEnum) {
                    case STRING:
                         val = Convert.toStr(value, (String) VariableValueTypeEnum.STRING.getDefaultValue());
                         break;
                    case NUMBER:
                         val = Convert.toNumber(value, (Number) VariableValueTypeEnum.NUMBER.getDefaultValue());
                         break;
                    case BOOL:
                         val = Convert.toBool(value, (Boolean) VariableValueTypeEnum.BOOL.getDefaultValue());
                         break;
                    default:
                         LOGGER.error("ValueTypeUtil parseValue");
                         throw new RuntimeException("类型转换错误");
               }
          } catch (Exception e) {
               LOGGER.error("ValueTypeUtil Convert error", e);
               return parseValue(valueTypeEnum, defaultValue, valueTypeEnum.getDefaultValue());
          }

          return val;
     }
}
