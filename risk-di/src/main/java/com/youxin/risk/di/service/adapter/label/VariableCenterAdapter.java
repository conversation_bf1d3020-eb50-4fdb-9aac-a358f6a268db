package com.youxin.risk.di.service.adapter.label;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.youxin.risk.commons.adapter.di.ServiceAdapter;
import com.youxin.risk.commons.adapter.di.ServiceRequest;
import com.youxin.risk.commons.adapter.di.ServiceResponse;
import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.utils.LoggerProxy;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

public class VariableCenterAdapter extends ServiceAdapter {
    private static final Logger LOGGER = LoggerFactory.getLogger(VariableCenterAdapter.class);

    @Override
    protected ServiceResponse callService(ServiceRequest request) {
        ServiceResponse response = new ServiceResponse();
        response.setRequestId(request.getRequestId());
        String loanKey = request.getLoanKey();
        String userKey = request.getUserKey();
        String step = request.getStep();
        String eventCode = request.getSystemEventCode();
        String variableType = (String) request.getParams().get("variableType");

        String serviceCode = request.getServiceCode();
        Map<String, Object> params = request.getParams();
        params.put("loanKey",loanKey);
        params.put("userKey",userKey);
        params.putIfAbsent("strategyId", request.getStep());
        params.putIfAbsent("strategyId", params.get("eventStep"));
        params.putIfAbsent("eventCode", request.getSystemEventCode());
        params.putIfAbsent("eventCode", params.get("eventCode"));
        params.putIfAbsent("eventName", request.getSystemEventName());
        params.putIfAbsent("eventName", params.get("eventName"));

        try {
            String url = String.format(diService.getServiceUrl() + "/%s/%s/%s", eventCode, variableType, serviceCode);
            LoggerProxy.info("VariableCenterAdapter", LOGGER,
                    "loanKey={} eventCode = {} step = {} userKey={} serviceCode = {} url = {}, params:{}"
                    , loanKey, eventCode, step, userKey, serviceCode, url, JSON.toJSONString(params));

            String result = SyncHTTPRemoteAPI.postJson(url, JSON.toJSONString(params), diService.getServiceTimeout().intValue());
            if (StringUtils.isBlank(result)){
                LoggerProxy.info("VariableCenterAdapter_callService_null", LOGGER, "params={}",JSON.toJSONString(params));
                response.setRetCode(RetCodeEnum.NO_DATA.getValue());
                response.setRetMsg(RetCodeEnum.NO_DATA.getRetMsg());
                return response;
            }

            JSONObject resultJson = JSONObject.parseObject(result);
            if (resultJson.getInteger("code") != 200) {
                response.setRetCode(RetCodeEnum.RESPONSE_EXCEPTION.getValue());
                response.setRetMsg(RetCodeEnum.RESPONSE_EXCEPTION.getRetMsg());
                return response;
            }
            if (!resultJson.containsKey("data")) {
                response.setRetCode(RetCodeEnum.NO_DATA.getValue());
                response.setRetMsg(RetCodeEnum.NO_DATA.getRetMsg());
                return response;
            }

            JSONObject data = resultJson.getJSONObject("data");
            if (!data.containsKey("varResults")) {
                response.setRetCode(RetCodeEnum.NO_DATA.getValue());
                response.setRetMsg(RetCodeEnum.NO_DATA.getRetMsg());
                return response;
            }

            LoggerProxy.info("VariableCenterAdapter_callService", LOGGER, "loanKey={} userKey={} result={}", loanKey, userKey, StringUtils.abbreviate(result, 300));
            response.setRetCode(RetCodeEnum.SUCCESS.getValue());
            response.setRetMsg(RetCodeEnum.SUCCESS.getRetMsg());
            Map<String, Object> resultMap = Maps.newHashMap();
            resultMap.put("data", result);
            response.setResult(resultMap);
            response.setData(result);

        } catch (Exception e){
            LoggerProxy.info("VariableCenterAdapter_callService_error", LOGGER, "loanKey={} eventCode = {} step = {} userKey={} serviceCode = {} url = {}",loanKey,eventCode,step,userKey,serviceCode,diService.getServiceUrl());
            response.setRetCode(RetCodeEnum.RESPONSE_EXCEPTION.getValue());
            response.setRetMsg(RetCodeEnum.RESPONSE_EXCEPTION.getRetMsg());
        }
        return response;
    }
}
