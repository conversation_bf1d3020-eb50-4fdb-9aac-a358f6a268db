package com.youxin.risk.di.service.adapter.haohuan;

import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

import com.alibaba.fastjson.JSONArray;
import com.youxin.apollo.client.NacosClient;
import com.youxin.risk.commons.constants.DiConstant;
import com.youxin.risk.commons.tools.redis.RetryableJedis;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.youxin.risk.commons.adapter.di.ServiceAdapter;
import com.youxin.risk.commons.adapter.di.ServiceRequest;
import com.youxin.risk.commons.adapter.di.ServiceResponse;
import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.utils.ContextUtil;
import com.youxin.risk.commons.utils.EncryptUtils;
import com.youxin.risk.commons.utils.JacksonUtil;
import com.youxin.risk.commons.utils.LoggerProxy;

import static com.youxin.risk.commons.constants.ApolloNamespace.commonSpace;

public class HaohuanSyncPostServiceAdapter extends ServiceAdapter {
    private static final Logger LOGGER = LoggerFactory.getLogger(HaohuanSyncPostServiceAdapter.class);

    private static final Integer RESPONSE_STATUS_SUCCESS = 0;

    private static final String REDIS_CHECK_USER_CANCEL_KEY = "REDIS_CHECK_USER_CANCEL_KEY2";

    private static final String REDIS_CHECK_USER_CANCEL_VALUE = "1";

    private static final String STOP_FLOW_SWITCH = "stopFlowSwitch";

    private Properties property = (Properties)ContextUtil.getBean("configProperties");

    private RetryableJedis jedisClient = (RetryableJedis)ContextUtil.getBean("retryableJedis");

    @Override
    protected ServiceResponse callService(ServiceRequest request) {
        Map<String, Object> params = this.generatorParams(request);
        ServiceResponse response = new ServiceResponse();
        //调用探活接口
        if(!userAgentLive(request)){
            response.setRetCode(RetCodeEnum.USER_CANCEL.getValue());
            response.setRetMsg(RetCodeEnum.USER_CANCEL.getRetMsg());
            return response;
        }
        response.setRequestId(request.getRequestId());
        String requestBody = JacksonUtil.toJson(params);
        JSONObject req = new JSONObject();
        // 根据url确认是否加密, 上线前清理掉
        String remoteUrl = this.diService.getServiceUrl();
        if (remoteUrl.endsWith("/getUserSuperMemberInfo")) {
            req.put("data", requestBody);
        } else {
            String salt = property.getProperty("salt.haohuan");
            req.put("data", EncryptUtils.encryptToBase64(requestBody, salt));
        }

        LoggerProxy.info("callServiceParams", LOGGER, "before call service, serviceCode={},params={}", request.getServiceCode(), req);
        String result = SyncHTTPRemoteAPI.postJson(this.diService.getServiceUrl(), req.toJSONString(), this.diService.getServiceTimeout().intValue());
        if (StringUtils.isBlank(result)) {
            LoggerProxy.error("serviceResponseError", LOGGER, "service response status error, request={},result={}", JacksonUtil.toJson(request), result);
            response.setRetCode(RetCodeEnum.RESPONSE_EXCEPTION.getValue());
            response.setRetMsg(RetCodeEnum.RESPONSE_EXCEPTION.getRetMsg());
            return response;
        }
        JSONObject resultJson = JSONObject.parseObject(result);
        if (RESPONSE_STATUS_SUCCESS.equals(resultJson.getInteger("code"))) {
            String data = resultJson.getString("data");
            response.setRetCode(RetCodeEnum.SUCCESS.getValue());
            response.setRetMsg(RetCodeEnum.SUCCESS.getRetMsg());
            Map<String, Object> resultMap = Maps.newHashMap();
            resultMap.put("data", data);
            response.setResult(resultMap);
            response.setJobId(resultJson.getString("jobid"));
            response.setData(data);
        } else {
            super.logStatusError(LOGGER,  JacksonUtil.toJson(request), result);
            response.setRetCode(RetCodeEnum.RESPONSE_EXCEPTION.getValue());
            String retMsg = resultJson.containsKey("msg") ? resultJson.getString("msg") :
                    RetCodeEnum.RESPONSE_EXCEPTION.getRetMsg();
            response.setRetMsg(retMsg);
        }
        return response;
    }

    protected Map<String, Object> generatorParams(ServiceRequest request) {
        Map<String, Object> params = Maps.newHashMap();
        params.putAll(request.getParams());
        return params;
    }

    //探活方法  true代表用户未注销或者不需要探活   false代表用户已注销
    private boolean userAgentLive(ServiceRequest request){
        try{
            String serviceCode = request.getServiceCode();
            //目前只有fundRouteService服务 需要探活
            if(serviceCode!=null&&serviceCode.equals("fundRouteService")&&checkCancelCount()){
                String userKey = request.getUserKey();
                String requestId = request.getRequestId();
                String sessionId = requestId.substring(0,requestId.lastIndexOf("_"));
                if(checkCancelUser(userKey,serviceCode)){
                    String forceTerminateUrl = property.getProperty("risk.admin.force.terminate.url");
                    JSONObject requestBody = new JSONObject();
                    JSONArray jsonArray = new JSONArray();
                    requestBody.put("sessionId",sessionId);
                    jsonArray.add(requestBody);
                    Map<String, String> header = new HashMap<>();
                    header.put("Content-Type", "application/json");
                    header.put("X-Token", DiConstant.INNER_SYSTEM_ACCESS_TOKEN);
                    LoggerProxy.info(serviceCode + "  forceTerminate", LOGGER, "url={},sessionId={}",
                            forceTerminateUrl, sessionId);
                    //获取终止流程开关状态
                    String stopFlowSwitch = NacosClient.getByNameSpace(commonSpace, STOP_FLOW_SWITCH, "未获取到apollo配置");
                    //1打开 0关闭
                    if("1".equals(stopFlowSwitch)){
                        String result = SyncHTTPRemoteAPI.postJson(forceTerminateUrl, jsonArray.toJSONString(), header, 10000);
                        if(!result.contains("succeed to Terminate Process!")){
                            LoggerProxy.warn(serviceCode + "  forceTerminate", LOGGER, "url={},sessionId={},resp={}",
                                    forceTerminateUrl, sessionId, result);
                        }
                        //设置redis值 控制访问频率
                        jedisClient.setex(REDIS_CHECK_USER_CANCEL_KEY,3600,REDIS_CHECK_USER_CANCEL_VALUE);
                        return false;
                    }
                }
            }
        }catch (Exception e){
            LoggerProxy.error("userAgentLive", LOGGER,"params={},error={}",e);
        }
        return true;
    }

    protected boolean checkCancelUser(String userKey,String serviceCode) {
        try {
            JSONObject request = new JSONObject();
            request.put("userKey", userKey);
            Map<String, String> header = new HashMap<>();
            header.put("Content-Type", "application/json");
            String requestBody = request.toJSONString();
            String userCancelUrl = property.getProperty("haofenqi.userCancel.url");
            LoggerProxy.info(serviceCode + "  checkCancelUser", LOGGER, "url={},request={}",
                    userCancelUrl, requestBody);
            String result = SyncHTTPRemoteAPI.postJson(userCancelUrl, request.toJSONString(), header,10000);
            LoggerProxy.info(serviceCode + "  checkCancelUser", LOGGER, "url={},request={},resp={}",
                    userCancelUrl, requestBody, result);
            if (StringUtils.isNotBlank(result)) {
                JSONObject resultObject = JSONObject.parseObject(result);
                Integer code = resultObject.getInteger("code");
                if (null != code && 0 == code) {
                    JSONObject dataObject = resultObject.getJSONObject("data");
                    if (dataObject != null) {
                        return dataObject.getBooleanValue("cancel");
                    } else {
                        throw new RuntimeException(serviceCode + "  checkCancelUser result is error!");
                    }
                } else {
                    throw new RuntimeException(serviceCode + "  checkCancelUser result is error!");
                }
            } else {
                throw new RuntimeException(serviceCode + "  checkCancelUser result is null!");
            }
        } catch (Exception ex) {
            LoggerProxy.error(serviceCode + "  checkCancelUserError", LOGGER, "userKey=" + userKey, ex);
        }
        return false;
    }

    private boolean checkCancelCount(){
        String value = jedisClient.get(REDIS_CHECK_USER_CANCEL_KEY);
        return !REDIS_CHECK_USER_CANCEL_VALUE.equals(value);
    }


}
