package com.youxin.risk.di.service.adapter;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.youxin.risk.commons.adapter.di.ServiceAdapter;
import com.youxin.risk.commons.adapter.di.ServiceRequest;
import com.youxin.risk.commons.adapter.di.ServiceResponse;
import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.model.datacenter.DcSubmitPlist;
import com.youxin.risk.commons.service.datacenter.DatacenterService;
import com.youxin.risk.commons.utils.ContextUtil;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.UploadTimeJudgeUtil;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.youxin.risk.commons.utils.UploadTimeJudgeUtil.LATEST_DATA_CREATE_TIME;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2020/5/13 9:16
 * @Version 1.0
 */
public class PlistServiceAdapter extends ServiceAdapter {

    private static final Logger LOGGER = LoggerFactory.getLogger(PlistServiceAdapter.class);


    public static void main(String[] args) {
        List<String> data = Lists.newArrayList("a","a1","a2","a3","a4","a5","a6","7","ae","1a");

        List<String> collect = data.stream().limit(1).collect(Collectors.toList());

        System.out.println(collect);


    }

    @Override
    protected ServiceResponse callService(ServiceRequest request) {
        ServiceResponse response = new ServiceResponse();
        response.setRequestId(request.getRequestId());
        Map<String,Object> params =  request.getParams();
        LoggerProxy.info("PlistServiceAdapter",LOGGER,"params={}", JSONObject.toJSONString(params));
        if(params.isEmpty()){
            LoggerProxy.error("PlistServiceAdapter", LOGGER, "userKey is empty");
            response.setRetCode(RetCodeEnum.RESPONSE_EXCEPTION.getValue());
            response.setRetMsg(RetCodeEnum.RESPONSE_EXCEPTION.getRetMsg());
            return response;
        }else{
            String userKey = (String) params.get("userKey");
            DatacenterService datacenterService = (DatacenterService) ContextUtil.getBean("datacenterService");
            Date latestUploadTime = datacenterService.getLatestUploadTimeByUserKey(userKey);
            List<DcSubmitPlist> plists = datacenterService.getAllPlsitByUserkey(userKey);
//            List<DcSubmitPlist> plists = new ArrayList<>();
//            if (!CollectionUtils.isEmpty(allDcSubmitPlist)){
//                int size = allDcSubmitPlist.size();
//                if (size < 10000){
//                    LoggerProxy.info("preOperationLogIdIndex",LOGGER,"不需要截取 userKey={} size={}",userKey,size);
//                    plists = allDcSubmitPlist;
//                }else {
//                    try {
//                        int lastIndex = size - 1;
//                        int preOperationLogIdIndex = 0;
//                        long lastOperationLogId = allDcSubmitPlist.get(lastIndex).getOperationLogId();
//                        long preOperationLogId = 0;
//                        for (int currentIndex = lastIndex; currentIndex > -1; currentIndex--) {
//                            /** 遍历从后往前,找到第一个不相等的位置 **/
//                            preOperationLogId = allDcSubmitPlist.get(currentIndex).getOperationLogId();
//                            if (lastOperationLogId != preOperationLogId) {
//                                preOperationLogIdIndex = currentIndex;
//                                break;
//                            }
//                        }
//                        /** 缩短之后的集合 **/
//                        plists = allDcSubmitPlist.stream().limit(preOperationLogIdIndex + 1).collect(Collectors.toList());
//                        LoggerProxy.info("preOperationLogIdIndex", LOGGER,
//                                "userKey={} 总大小size={} 截取end={} preOperationLogId={} plistsSize={} plistsValue={}",
//                                userKey,size,preOperationLogIdIndex,preOperationLogId,plists.size(),plists.get(plists.size()-1));
//                    } catch (Exception e) {
//                        LoggerProxy.error("preOperationLogIdIndex", LOGGER, "error {}", e);
//                    }
//                }
//            }
            response.setRetCode(RetCodeEnum.SUCCESS.getValue());
            response.setRetMsg(RetCodeEnum.SUCCESS.getRetMsg());
            if(CollectionUtils.isEmpty(plists)){
                response.setRetCode(RetCodeEnum.NO_DATA.getValue());
                response.setRetMsg(RetCodeEnum.NO_DATA.getRetMsg());
            }
            Map<String, Object> resultMap = Maps.newHashMap();
            String result = JSONObject.toJSONString(plists);
            resultMap.put("data", result);
            resultMap.put(LATEST_DATA_CREATE_TIME,latestUploadTime);
            response.setResult(resultMap);
            response.setData(result);
        }
        return response;
    }

    @Override
    protected  boolean isInUploadTime(ServiceRequest request,ServiceResponse response){
        return UploadTimeJudgeUtil.allPlistServiceUploadTimeJudge(request,response);
    }

}
