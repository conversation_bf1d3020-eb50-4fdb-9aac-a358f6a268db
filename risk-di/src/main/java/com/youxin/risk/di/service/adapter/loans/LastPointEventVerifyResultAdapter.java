/*
 * Copyright (C) 2019 Baidu, Inc. All Rights Reserved.
 */
package com.youxin.risk.di.service.adapter.loans;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.youxin.risk.commons.adapter.di.ServiceAdapter;
import com.youxin.risk.commons.adapter.di.ServiceRequest;
import com.youxin.risk.commons.adapter.di.ServiceResponse;
import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.service.verify.VerifyResultService;
import com.youxin.risk.commons.utils.ContextUtil;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.StringUtils;
import com.youxin.risk.commons.vo.EventVo;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class LastPointEventVerifyResultAdapter extends ServiceAdapter {
    private static final Logger logger = LoggerFactory.getLogger(LastPointEventVerifyResultAdapter.class);

    private static final String PARAM_KEY_USER_KEY = "userKey";
    private static final String PARAM_KEY_SOURCE_SYSTEM = "sourceSystem";
    private static final String PARAM_KEY_EVENT_CODE = "eventCode";
    private static final String SERVICE = "eventService";
    private static final String VERIFY_RESULT_SERVICE = "verifyResultService";
    private static final String PARAM_KEY_LOAN_KEY = "loanKey";

    @Override
    protected ServiceResponse callService(ServiceRequest request) {
        ServiceResponse response = new ServiceResponse();
        response.setRequestId(request.getRequestId());

        Map<String, Object> params = request.getParams();
        String userKey = (String) params.get(PARAM_KEY_USER_KEY);
        String sourceSystem = (String) params.get(PARAM_KEY_SOURCE_SYSTEM);
        String curLoanKey = (String) params.get(PARAM_KEY_LOAN_KEY);
        // 获取请求eventCode
        String sourceEventCode = (String) params.get(PARAM_KEY_EVENT_CODE);
//        EventService eventService = (EventService) ContextUtil.getBean(SERVICE);
        VerifyResultService verifyResultService = (VerifyResultService)ContextUtil.getBean(VERIFY_RESULT_SERVICE);
        LoggerProxy.info("lastVerifyResultInit", logger, "userKey={},requestId={},eventService init suceess",
				userKey,request.getRequestId());
        if(verifyResultService == null){
            LoggerProxy.error("eventDaoNotFound", logger, "EventDao instance can not find");
            response.setRetCode(RetCodeEnum.RESPONSE_EXCEPTION.getValue());
            response.setRetMsg(RetCodeEnum.RESPONSE_EXCEPTION.getRetMsg());
            return response;
        }
        boolean hasRecord = false;
//        List<EventVo> templateEventVoList = eventService.getEventsByUserKey(userKey, sourceSystem, sourceEventCode, TEMPLATE_SOURCE);
        List<EventVo> templateEventVoList = verifyResultService.getEventsByUserKey(userKey, sourceSystem, sourceEventCode);
        // todo 拆分放款审核新老客事件后，这里需要做定制兼容，后续切到变量删除
        if (StringUtils.equals(sourceEventCode, "haoHuanLendAudit")) {
            List<EventVo> tmpList = verifyResultService.getEventsByUserKey(userKey, sourceSystem, "haoHuanLendAuditReloan");
            if (CollectionUtils.isNotEmpty(tmpList)) {
                // 过滤当前loanKey
                tmpList.removeIf(eventVo -> curLoanKey.equals(eventVo.getEvent().getLoanKey()));
            }
            if (CollectionUtils.isNotEmpty(tmpList)) {
                sourceEventCode = "haoHuanLendAuditReloan";
                // 使用复贷的审核数据
                templateEventVoList = tmpList;
            }
        }
        LoggerProxy.info("LastPointEventVerifyResultAdapter_templateEventVoList", logger
                , "userKey={},eventCode={},size={}", userKey,sourceEventCode,templateEventVoList.size());
        if (CollectionUtils.isNotEmpty(templateEventVoList)){
            hasRecord = setResponse(hasRecord, templateEventVoList, sourceEventCode, sourceSystem ,response);
        }

        if (!hasRecord) {
            LoggerProxy.info("lastVerifyResultNoData", logger, "userKey={},eventCode={},last verify "
					+ "result not exist", userKey, sourceEventCode);
            response.setRetCode(RetCodeEnum.NO_DATA.getValue());
            response.setRetMsg(RetCodeEnum.NO_DATA.getRetMsg());
        }

        return response;
    }

    /**
     *
     * @return
     */
    public boolean setResponse(boolean hasRecord,List<EventVo> eventVoList,String sourceEventCode,String sourceSystem,ServiceResponse response){
        for (EventVo eventVo : eventVoList) {
            Event event = eventVo.getEvent();
            String eventCode = event.getEventCode();
            LoggerProxy.info("LastPointEventVerifyResultAdapter_setResponse", logger, "eventCode: {}", eventCode);
            if (sourceEventCode.equals(eventCode)) {
                Map<String, Object> verifyResult = event.getVerifyResult();
                if (verifyResult == null){
                    LoggerProxy.warn("LastPointEventVerifyResultAdapter_setResponse", logger, "verifyResult is null");
                    continue;
                }
                hasRecord = true;
                response.setRetCode(RetCodeEnum.SUCCESS.getValue());
                response.setRetMsg(RetCodeEnum.SUCCESS.getRetMsg());
                Map<String, Object> resultMap = Maps.newHashMap();

                String originResult = (String) verifyResult.get("originResult");
                if (StringUtils.isEmpty(originResult)) {
                    originResult = JSON.toJSONString(verifyResult);
                }
                if(!"REN_REN_DAI".equals(sourceSystem)){
                    JSONObject jsonObject = JSONObject.parseObject(originResult);
                    jsonObject.put("eventCreateTime",eventVo.getCreateTime());
                    originResult = JSON.toJSONString(jsonObject);
                }
                resultMap.put("data", originResult);
                response.setResult(resultMap);
                response.setData(originResult);
                break;
            }
        }
        LoggerProxy.info("LastPointEventVerifyResultAdapter_setResponse", logger, "hasRecord: {}", hasRecord);
        return hasRecord;
    }
}
