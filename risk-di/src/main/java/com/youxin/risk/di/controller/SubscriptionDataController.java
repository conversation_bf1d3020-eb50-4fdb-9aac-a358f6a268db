package com.youxin.risk.di.controller;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.di.service.SubscriptionDataCallbackService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @description: 数据订阅控制类
 * @author: juxiang
 * @create: 2022-04-12 11:40
 **/
@RestController
@RequestMapping("/subscriptionData")
public class SubscriptionDataController {
    @Autowired
    SubscriptionDataCallbackService subscriptionDataCallbackService;

    @RequestMapping("/callback/{requestId}")
    public JSONObject subscriptionDataCallback(@PathVariable("requestId") String requestId){
        boolean result = subscriptionDataCallbackService.subscriptionDataCallback(requestId);
        JSONObject response=new JSONObject();
        if(result){
            response.put("status",200);
            response.put("message","callback success!");
        }else {
            response.put("status",500);
            response.put("message","callback failed!");
        }
        return response;
    }

}
