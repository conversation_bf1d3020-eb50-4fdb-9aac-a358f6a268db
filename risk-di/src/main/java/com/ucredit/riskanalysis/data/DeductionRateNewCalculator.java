package com.ucredit.riskanalysis.data;

import com.youxin.risk.commons.utils.SpringContext;
import com.youxin.risk.di.mapper.accountProxy.AccountProxyMapper;
import com.youxin.risk.di.service.hbase.RiskUserPayNewService;
import com.youxin.risk.di.service.hbase.RiskUserPayService;
import org.springframework.stereotype.Service;

import static com.youxin.risk.di.common.Constant.SELECT_SUCCESS_DEDUCTION_TIMES;
import static com.youxin.risk.di.common.Constant.SELECT_TOTAL_DEDUCTION_TIMES;

/**
 * 扣款成功率计算类
 *
 * <AUTHOR>
 */
@Service
public class DeductionRateNewCalculator {

    private final RiskUserPayNewService riskUserPayService = SpringContext.getBean(RiskUserPayNewService.class);

    public String getValue(String userKey, int days) {
        Long totalDeductionTimes = (Long) riskUserPayService.getNewUserPayData(SELECT_TOTAL_DEDUCTION_TIMES, userKey, days);

        if (totalDeductionTimes == null || totalDeductionTimes == 0) {
            return "null";
        }

        //扣款成功的次数
        Long successDeductionTimes = (Long) riskUserPayService.getNewUserPayData(SELECT_SUCCESS_DEDUCTION_TIMES, userKey, days);

        if (successDeductionTimes == null) {
            return "null";
        }
        return String.valueOf(successDeductionTimes.doubleValue() / totalDeductionTimes.doubleValue());
    }
}
