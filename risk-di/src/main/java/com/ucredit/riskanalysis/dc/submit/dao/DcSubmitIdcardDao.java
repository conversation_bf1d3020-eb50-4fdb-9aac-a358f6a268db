package com.ucredit.riskanalysis.dc.submit.dao;

import com.google.common.collect.Lists;
import com.ucredit.riskanalysis.dc.submit.model.DcSubmitIdcard;
import com.weicai.caesar.CaesarUtil;
import com.youxin.risk.commons.utils.MaskUtils;
import com.youxin.risk.commons.utils.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

@Repository
public class DcSubmitIdcardDao extends DcSubmitInfoBaseDao<DcSubmitIdcard> {

    @Override
    protected Class<DcSubmitIdcard> getEntityClass() {
        return DcSubmitIdcard.class;
    }

    public List<DcSubmitIdcard> findSubmitIdcardByIdNumber(String idNumber) {
        if (StringUtils.isBlank(idNumber)) {
            return Collections.emptyList();
        }

        // 加解密数据都查询
        List<String> idNumbers = Lists.newArrayList(idNumber);
        if (CaesarUtil.isEncrypted(idNumber)) {
            idNumbers.add(MaskUtils.unMaskValue(idNumber));
        } else {
            idNumbers.add(MaskUtils.maskValue(idNumber));
        }

        String hql = "select new com.ucredit.riskanalysis.dc.submit.model.DcSubmitIdcard(userKey,min(operationLogId) as operationLogId) " +
                "from DcSubmitIdcard where idcardNumber in (:idNumbers) group by userKey";
        return this
                .createQuery(hql)
                .setParameterList("idNumbers", idNumbers).list();
    }

    public List<DcSubmitIdcard> findAllSubmitIdcardByIdNumber(String idNumber) {
        if (StringUtils.isBlank(idNumber)) {
            return Collections.emptyList();
        }

        // 加解密数据都查询
        List<String> idNumbers = Lists.newArrayList(idNumber);
        if (CaesarUtil.isEncrypted(idNumber)) {
            idNumbers.add(MaskUtils.unMaskValue(idNumber));
        } else {
            idNumbers.add(MaskUtils.maskValue(idNumber));
        }

        String hql = "select new com.ucredit.riskanalysis.dc.submit.model.DcSubmitIdcard(userKey,operationLogId as operationLogId) " +
                "from DcSubmitIdcard where idcardNumber in (:idNumbers)";
        return this
                .createQuery(hql)
                .setParameterList("idNumbers", idNumbers).list();
    }
}
