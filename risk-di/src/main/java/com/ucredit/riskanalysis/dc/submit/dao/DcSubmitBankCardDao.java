package com.ucredit.riskanalysis.dc.submit.dao;

import com.google.common.collect.Lists;
import com.ucredit.riskanalysis.dc.submit.model.DcSubmitBankCard;
import com.weicai.caesar.CaesarUtil;
import com.youxin.risk.commons.utils.MaskUtils;
import com.youxin.risk.commons.utils.StringUtils;
import org.hibernate.Query;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

@Repository
public class DcSubmitBankCardDao extends DcSubmitInfoBaseDao<DcSubmitBankCard> {

    @Override
    protected Class<DcSubmitBankCard> getEntityClass() {
        return DcSubmitBankCard.class;
    }

    public List<DcSubmitBankCard> getByBankCardNo(String bankCardNo) {
        if (StringUtils.isBlank(bankCardNo)) {
            return Collections.emptyList();
        }

        // 加解密数据都查询
        List<String> bankCardNos = Lists.newArrayList(bankCardNo);
        if (CaesarUtil.isEncrypted(bankCardNo)) {
            bankCardNos.add(MaskUtils.unMaskValue(bankCardNo));
        } else {
            bankCardNos.add(MaskUtils.maskValue(bankCardNo));
        }

        String hql = "select new com.ucredit.riskanalysis.dc.submit.model.DcSubmitBankCard(userKey,min(applyId) as applyId) " +
                "From DcSubmitBankCard WHERE bankcardNo in (:bankCardNos) group by userKey";
        Query query = this.createQuery(hql);
        query.setParameterList("bankCardNos", bankCardNos);
        return query.list();
    }
}
