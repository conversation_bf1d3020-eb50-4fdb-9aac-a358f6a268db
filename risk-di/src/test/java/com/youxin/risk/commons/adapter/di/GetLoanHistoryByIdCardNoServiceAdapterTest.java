package com.youxin.risk.commons.adapter.di;

import com.alibaba.fastjson.JSON;
import com.youxin.risk.commons.adapter.di.ServiceAdapter;
import com.youxin.risk.commons.adapter.di.ServiceRequest;
import com.youxin.risk.commons.cache.CacheApi;
import com.youxin.risk.commons.dao.datacenter.DcCityCodeMapper;
import com.youxin.risk.commons.model.datacenter.DcCityCode;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.io.*;
import java.util.HashMap;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration("classpath:spring/spring-config.xml")
public class GetLoanHistoryByIdCardNoServiceAdapterTest {

    @Resource
    private DcCityCodeMapper dcCityCodeMapper;
    @Test
    public void callService(){

        ServiceAdapter adapter = CacheApi.getServiceClass("getLoanHistoryByIdCardNo");

        ServiceRequest request = new ServiceRequest();
        HashMap<String, Object> params = new HashMap<>();
        params.put("idCardNo","310105198310212815");
        request.setParams(params);
        ServiceResponse serviceResponse = adapter.callService(request);
        System.out.println(JSON.toJSONString(serviceResponse));
    }

    @Test
    public void callService2(){

        ServiceAdapter adapter = CacheApi.getServiceClass("loanHistoryByUserKeyService");
        ServiceRequest request = new ServiceRequest();
        HashMap<String, Object> params = new HashMap<>();
        params.put("partner","HAOHUAN");
        params.put("partnerUserId","ddf57040f3cad9c7c20da9676f4f78e2");
        params.put("requestTime",System.currentTimeMillis());
        params.put("serviceVersion","1.0");
        params.put("systemId","HAO_HUAN");
        request.setParams(params);
        ServiceResponse serviceResponse = adapter.callService(request);
        System.out.println(JSON.toJSONString(serviceResponse));
    }

    @Test
    public void testInsert() throws IOException {
        String path = "C:\\Users\\<USER>\\Desktop\\city.txt";
        File file = new File(path);
        BufferedReader bufferedReader = new BufferedReader(new FileReader(file));
        String line;
        while ((line = bufferedReader.readLine()) != null){
            String[] info = line.split(" ");
            DcCityCode cityCode = new DcCityCode();
            cityCode.setCode(info[0]);
            cityCode.setCity(info[1]);
            dcCityCodeMapper.insert(cityCode);
        }

    }

    @Test
    public void testSensor(){

        ServiceAdapter adapter = CacheApi.getServiceClass("sensorEventICService");

        ServiceRequest request = new ServiceRequest();
        HashMap<String, Object> params = new HashMap<>();
        params.put("userKey","bc78f239e82563d14b36426280b88bc3");
        request.setParams(params);
        request.setUserKey("bc78f239e82563d14b36426280b88bc3");
        ServiceResponse serviceResponse = adapter.callService(request);
        System.out.println(JSON.toJSONString(serviceResponse));
    }
}
