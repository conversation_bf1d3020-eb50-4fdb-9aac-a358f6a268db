mode.name=test
app.name=risk-shadow-data

home.base=/home/<USER>

app.home=${home.base}/risk-control/${app.name}
app.log.home=${home.base}/logs/${app.name}

tomcat.home=${home.base}/products/tomcat/tomcat_risk_shadow
tomcat.port=8131
tomcat.shutdown.port=8132
tomcat.connection.timeout=5000
tomcat.doc.base=${app.home}
tomcat.allow.ips=172.*.*.*||127.0.0.1||10.*.*.*

java.opts=-Xmx2000m -Xms2000m -Xmn1000m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=128m \
		-verbose:gc -XX:+PrintGCDetails -XX:+PrintGCDateStamps -Xloggc:$CATALINA_HOME/logs/gc.log \
		-XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=$CATALINA_HOME/logs/oom.log \
		-Djava.nio.channels.spi.SelectorProvider=sun.nio.ch.EPollSelectorProvider \
        -Dfile.encoding=UTF8  -Duser.timezone=GMT+08

console.log.level=DEBUG

datasource.maxActive=200
datasource.initialSize=2
datasource.minIdle=2
datasource.maxWait=2000
datasource.testOnBorrow=true
datasource.defaultTransactionIsolation=4
datasource.timeBetweenEvictionRunsMillis=30000
datasource.minEvictableIdleTimeMillis=300000
datasource.timeBetweenLogStatsMillis=300000
datasource.druid.remove.abandoned=false
datasource.druid.remove.abandoned.timeout=300
datasource.druid.log.abandoned=false
datasource.connectProperties.connectTimeout=1000
datasource.connectProperties.socketTimeout=5000
datasource.logAbandoned=false
datasource.removeAbandoned=true
datasource.removeAbandonedTimeout=120
datasource.poolPreparedStatements=false
#datasource.filters=stat,wall,log4j
datasource.filters=stat,wall

datasource.url.params=characterEncoding=utf8&amp;autoReconnect=true&amp;zeroDateTimeBehavior=convertToNull&amp;useUnicode=true&amp;useOldAliasMetadataBehavior=true

admin.datasource.url=*****************************************?${datasource.url.params}
admin.datasource.username=test
admin.datasource.pwd=cfx4TxzSdTXOXuyUOcL1

shadow.datasource.url=******************************************?${datasource.url.params}
shadow.datasource.username=test
shadow.datasource.pwd=cfx4TxzSdTXOXuyUOcL1

redis.maxTotal=8
redis.maxIdle=8
redis.minIdle=4
redis.maxWaitMillis=5000
redis.testOnBorrow=true
redis.cluster.connectionTimeout=3000
redis.cluster.soTimeout=3000
redis.cluster.maxAttempts=1
redis.cluster.password=passwd456
redis.cluster.nodes=************:7000,************:7001,************:7002,\
  ************:7100,************:7101,************:7102

kafka.dp.hosts=hadoop-1:9092,hadoop-2:9092,hadoop-3:9092
kafka.mirror.dp.hosts=***********:9092,***********:9092,***********:9092
kafka.shadow.datatrans.topic=kafka.shadow.datatrans.topic.test
kafka.shadow.datatrans.topic.group.id=youxin_risk_shadow_Group_test

kafka.shadow.monitor.datatrans.topic=kafka.shadow.monitor.topic
kafka.shadow.monitor.datatrans.topic.group.id=youxin_risk_shadow_monitor_Group_test
#engine shadow
kafka.engine.shadow.topic=risk.engine.event.shadow.test
kafka.engine.shadow.topic.group.id=youxin_risk_engine_event_shadow_Group_test

metrics.remote.queue.server=${redis.cluster.nodes}
metrics.remote.queue.redis.password=${redis.cluster.password}
metrics.stop=false

url.notify.hfq=http://************/internal/v1/audit/update-account-info
notify.encrypt.key.hfq=HAOHUAN_PASSWORD

datasource.paydayloanVerify.url=********************************************************************************************************************************
datasource.paydayloanVerify.username=root
datasource.paydayloanVerify.password=youxin_risk

xxl.job.admin.addresses=http://risk-xxl-job-manager.test.rrdbg.com
xxl.job.accessToken=
xxl.job.executor.appname=risk-shadow-data
xxl.job.executor.address=
xxl.job.executor.ip=
xxl.job.executor.port=-1
xxl.job.executor.logpath=/tmp/
xxl.job.executor.logretentiondays=-1

youxin.env=DEV


metrics.point.kafka.hosts=hadoop-1:9092,hadoop-2:9092,hadoop-3:9092
metrics.point.kafka.topic=metrics.point.kafka.topic_test
metrics.point.kafka.group.id=metrics.point.kafka.group_test
metrics.point.kafka.topic.list=metrics.point.kafka.topic_test,metrics.point.kafka.topic.gateway_test
metrics.point.mirror.kafka.hosts=***********:9092,***********:9092,***********:9092
