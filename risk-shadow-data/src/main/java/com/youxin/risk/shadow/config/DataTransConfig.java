package com.youxin.risk.shadow.config;

import com.youxin.risk.shadow.service.handler.DataTransHandler;

public class DataTransConfig {

    private String policyType;

    private DataTransHandler dataTransHandler;

    public String getPolicyType() {
        return policyType;
    }

    public void setPolicyType(String policyType) {
        this.policyType = policyType;
    }

    public DataTransHandler getDataTransHandler() {
        return dataTransHandler;
    }

    public void setDataTransHandler(DataTransHandler dataTransHandler) {
        this.dataTransHandler = dataTransHandler;
    }
}
