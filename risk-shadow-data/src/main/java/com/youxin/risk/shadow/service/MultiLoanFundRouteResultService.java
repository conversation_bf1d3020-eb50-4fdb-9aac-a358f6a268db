package com.youxin.risk.shadow.service;

import com.youxin.risk.shadow.dao.hfq.MultiLoanFundRouteResultMapper;
import com.youxin.risk.shadow.model.MultiLoanFundRouteResult;
import org.springframework.retry.annotation.EnableRetry;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2020/6/4 20:55
 * @Version 1.0
 */
@EnableRetry
public class MultiLoanFundRouteResultService {

    @Resource
    private MultiLoanFundRouteResultMapper multiLoanFundRouteResultMapper;

    public void save(MultiLoanFundRouteResult multiLoanFundRouteResult){
        multiLoanFundRouteResultMapper.save(multiLoanFundRouteResult);
    }
}
