package com.youxin.risk.shadow.listener;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Stopwatch;
import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.kafkav2.KafkaContext;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.shadow.config.Apollo;
import com.youxin.risk.shadow.constants.ShadowConstant;
import com.youxin.risk.shadow.model.MonitorResult;
import com.youxin.risk.shadow.model.ShadowLoanAuditStrategyResult;
import com.youxin.risk.shadow.service.ShadowLoanAuditStrategyResultService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

@Service
public class LoanAuditResultTransMessageHandler extends BaseShadowKafkaMsgHandler {
    @Resource
    private ShadowLoanAuditStrategyResultService shadowLoanAuditStrategyResultService;
    @Override
    protected void before(KafkaContext context) {
        // check event is not null
        MonitorResult message = context.getMessageObject(MonitorResult.class);
		// 绑定logId
        //filter message
       Object verifyResult = message.getData();
        if (verifyResult == null){
            LoggerProxy.error("dealLoanAuditResultTransMessageResultIsNull", logger, "message=" + message);
            context.setTerminated(true);
            return;
        }
    }

    @Override
    protected void handler0(KafkaContext context) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        long costTime = 0L;
        try {
            MonitorResult message = context.getMessageObject(MonitorResult.class);
            if (Apollo.logDetail()) {
                LoggerProxy.info("receivedLoanAuditResultTransMessage", logger, "kafkaOrigMessage={}", context.getMessage());
            }
            String lendVerifyMsg = JSONObject.toJSONString(message.getData());
            if (StringUtils.isEmpty(lendVerifyMsg)) {
                return;
            }
            JSONObject jsonObject = JSONObject.parseObject(lendVerifyMsg);
            ShadowLoanAuditStrategyResult loanAuditStrategyResult = JSONObject.parseObject(lendVerifyMsg, ShadowLoanAuditStrategyResult.class);
			loanAuditStrategyResult.setIsPassed(jsonObject.getBoolean("passed"));

            shadowLoanAuditStrategyResultService.insertMinimal(loanAuditStrategyResult);
			// 考虑无异常视为成功,埋点放入final
            logger.info(String.format("LoanAuditResultTransMessageHandler insert loanKey:%s,userKey:%s,loanType:%s," +
                            "orderNo:%s,loan:%s,loanId:%s,userLineId:%s,lendId:%s,midVerifyId:%s,isPassed:%s,status:%s,createTime:%s,repayMob:%s,repayLastpayoffFlag:%s,repayLastpayoffTime:%s,fundChannel:%s",
                    loanAuditStrategyResult.getLoanKey(),loanAuditStrategyResult.getUserKey(),loanAuditStrategyResult.getLoanType(),loanAuditStrategyResult.getOrderNo(),
                    loanAuditStrategyResult.getLoan(),loanAuditStrategyResult.getLoanId(),loanAuditStrategyResult.getUserLineId(),loanAuditStrategyResult.getLendId(),loanAuditStrategyResult.getMidVerifyId(),
                    loanAuditStrategyResult.getIsPassed(),loanAuditStrategyResult.getStatus(),loanAuditStrategyResult.getCreateTime(),loanAuditStrategyResult.getRepayMob(),loanAuditStrategyResult.getRepayLastpayoffFlag(),
                    loanAuditStrategyResult.getRepayLastpayoffTime(),loanAuditStrategyResult.getFundChannel()));
            LoggerProxy.info("dealLoanAuditResultTransMessageSuccess", logger, "success");
            context.setRetCode(RetCodeEnum.SUCCESS);
        } catch (Exception e) {
            context.setTerminated(true);
            context.setRetCode(RetCodeEnum.FAILED);
            LoggerProxy.error("dealLoanAuditResultTransMessageError", logger, "", e);
        } finally {
            costTime = stopwatch.stop().elapsed(TimeUnit.MILLISECONDS);
            if (Apollo.logDetail()) {
                LoggerProxy.info("finishLoanAuditResultTransKafKaMsg", logger, "cost={},request={},result={}",
                        costTime, context.getMessage(), context.getRetCode().name());
            }
            point(costTime, context, ShadowConstant.ENGINE_POLICY_POINT_FLAG);
        }
        return;
    }


}