<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xmlns="http://www.springframework.org/schema/beans"
	   xmlns:context="http://www.springframework.org/schema/context"
	   xmlns:util="http://www.springframework.org/schema/util"
	   xmlns:task="http://www.springframework.org/schema/task"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans
     http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
     http://www.springframework.org/schema/context
     http://www.springframework.org/schema/context/spring-context-4.0.xsd
     http://www.springframework.org/schema/util
     http://www.springframework.org/schema/util/spring-util-4.0.xsd
     http://www.springframework.org/schema/task
     http://www.springframework.org/schema/task/spring-task.xsd">

	<context:annotation-config />
	<context:component-scan base-package="com.youxin" use-default-filters="false">
		<context:include-filter type="annotation" expression="org.springframework.stereotype.Repository" />
		<context:include-filter type="annotation" expression="org.springframework.stereotype.Component" />
		<context:include-filter type="annotation" expression="org.springframework.stereotype.Service" />
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>

	<util:constant id="appName" static-field="com.youxin.risk.commons.constants.AppName.risk_channel" />

	<bean class="com.youxin.risk.commons.utils.GlobalUtil">
		<property name="appName" ref="appName" />
	</bean>
	
	<bean class="com.youxin.risk.commons.utils.ContextUtil" />
	
	<util:properties id="configProperties" location="classpath:conf/*.properties"/>
	<context:property-placeholder location="classpath:conf/*.properties" />
	
	<import resource="spring-aop.xml" />
	<import resource="spring-datasource.xml" />
	<import resource="spring-service.xml" />
	<import resource="spring-redis.xml" />
	<import resource="spring-queue.xml" />
	<import resource="spring-task.xml" />
</beans>
