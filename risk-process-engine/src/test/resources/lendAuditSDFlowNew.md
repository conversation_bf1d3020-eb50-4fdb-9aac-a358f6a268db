~~~mermaid
flowchart TD
startNode --> LendAuditBaseNewData
LendAuditBaseNewData --> preSubProcessNodeForLEND_VERIFY_SD_ANTIFRAUD#0
preSubProcessNodeForLEND_VERIFY_SD_ANTIFRAUD#0 --> HH_Lend_A_SD_AF_var
HH_Lend_A_SD_AF_var --> HH_Lend_A_SD_AF
HH_Lend_A_SD_AF --> postSubProcessNodeForLEND_VERIFY_SD_ANTIFRAUD#0
postSubProcessNodeForLEND_VERIFY_SD_ANTIFRAUD#0 --> routeba052586

routeba052586 -->|REJECT| route1d5f474d
routeba052586 -->|ACCEPT| route985722ca

route1d5f474d --> preSubProcessNodeForLEND_VERIFY_FD_END#8
preSubProcessNodeForLEND_VERIFY_FD_END#8 --> hhLendResultTask
hhLendResultTask --> routef8db4304

routef8db4304 -->|ACCEPT & PUDAO| PuDaoVerifyData
routef8db4304 -->|REJECT| hhRandomPudaoTask
routef8db4304 -->|ACCEPT & !PUDAO| verifyEndNotifyTaskService

PuDaoVerifyData --> hhAuditEnvAddTask --> puDaoDifferenceResultWarn --> verifyEndNotifyTaskService

hhRandomPudaoTask -->|isPuDaoFl=true| PuDaoVerifyData
hhRandomPudaoTask -->|isPuDaoFl=false/null| verifyEndNotifyTaskService
    
verifyEndNotifyTaskService -->|if_diversion≠0| hhLendLastPointEventVerifyData --> diversionCheckTask --> postSubProcessNodeForLEND_VERIFY_FD_END#8 --> endNode
verifyEndNotifyTaskService -->|if_diversion=0/null| postSubProcessNodeForLEND_VERIFY_FD_END#8

route985722ca -->|branch=24| preSubProcessNodeForLEND_VERIFY_SD_24#0 --> HH_Lend_A_SD_24_var --> HH_Lend_A_SD_24 --> postSubProcessNodeForLEND_VERIFY_SD_24#0 --> route5bb0a3a2
route985722ca -->|branch≠24| preSubProcessNodeForLEND_VERIFY_SD_36#0 --> HH_Lend_A_SD_36_var --> HH_Lend_A_SD_36 --> postSubProcessNodeForLEND_VERIFY_SD_36#0 --> route5bb0a3a2

route5bb0a3a2 -->|REJECT| route1d5f474d
route5bb0a3a2 -->|ACCEPT| route4c3e2237
route4c3e2237 -->|isNeedManual=1| preSubProcessNodeForLEND_VERIFY_SD_IVR_ANTIFRAUD#0 --> HH_Lend_A_SD_IVR_AF_var --> HH_Lend_A_SD_IVR_AF --> postSubProcessNodeForLEND_VERIFY_SD_IVR_ANTIFRAUD#0 --> preSubProcessNodeForLEND_VERIFY_FD_END#8
route4c3e2237 -->|isNeedManual≠1| route1d5f474d
~~~