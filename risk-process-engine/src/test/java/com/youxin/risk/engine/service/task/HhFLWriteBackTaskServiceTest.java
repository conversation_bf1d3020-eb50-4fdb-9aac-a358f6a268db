package com.youxin.risk.engine.service.task;

import com.youxin.risk.commons.model.verify.VerifyResult;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration("classpath:spring/spring-config.xml")
public class HhFLWriteBackTaskServiceTest {

    @Autowired
    private HhFLWriteBackTaskService hhFLWriteBackTaskService;

    String reasonCode = "{\n" +
            "                    \"rhCoef\": \"2.0\",\n" +
            "                    \"appVersion\": \"860\",\n" +
            "                    \"ageDiff\": \"44.5\",\n" +
            "                    \"cRuleHitFlag\": 0,\n" +
            "                    \"rhCoefCust\": \"2.0\",\n" +
            "                    \"cusAdjustTagB\": \"24客群\",\n" +
            "                    \"cusAdjustTagC\": \"24客群\",\n" +
            "                    \"contactNum\": \"1244.0\",\n" +
            "                    \"C000\": \"ANDROID\",\n" +
            "                    \"ensembleLongStandardScore\": \"887.0\",\n" +
            "                    \"id\": \"0000000\",\n" +
            "                    \"pbocIncomeCust\": \"B2\",\n" +
            "                    \"isXxl\": \"1.0\",\n" +
            "                    \"idcardLegalityFrontPhotoCopy\": \"0.0\",\n" +
            "                    \"isIrr\": 1,\n" +
            "                    \"fundPaymentAmount\": \"-999\",\n" +
            "                    \"v1StrategyBaseParam\": {\n" +
            "                        \"priceCus\": \"24客群\",\n" +
            "                        \"liftCoef24\": 1,\n" +
            "                        \"huichuanLogicDetail\": \"未建模_LA\",\n" +
            "                        \"maScore24v1Bin\": \"9\",\n" +
            "                        \"amount24B\": \"9000.0\",\n" +
            "                        \"blackRuleScoreAll\": {\n" +
            "                            \"blackRuleScoreV10\": \"15.0\",\n" +
            "                            \"36客群\": \"15.0\",\n" +
            "                            \"24客群\": \"15.0\"\n" +
            "                        },\n" +
            "                        \"amount24C\": \"3600.0\",\n" +
            "                        \"blackRuleScore\": \"15.0\",\n" +
            "                        \"ruleScoreParam\": {\n" +
            "                            \"hjTotalScore\": \"-4.0\",\n" +
            "                            \"txlTotalScore\": \"-3.0\",\n" +
            "                            \"pbocTotalScore\": \"-2.0\",\n" +
            "                            \"ruleTotalScore\": \"-4.0\",\n" +
            "                            \"plistTotalScore\": \"4.0\",\n" +
            "                            \"heikaTotalScore\": \"1.0\",\n" +
            "                            \"badTotalScore\": \"0.0\",\n" +
            "                            \"brTotalScore\": \"-4.0\",\n" +
            "                            \"tdTotalScore\": \"-9.0\",\n" +
            "                            \"baseTotalScore\": \"2.0\",\n" +
            "                            \"smsTotalScore\": \"0.0\",\n" +
            "                            \"cusV2\": \"24客群\",\n" +
            "                            \"thjlTotalScore\": \"2.0\",\n" +
            "                            \"otherTotalScore\": \"6.0\",\n" +
            "                            \"ruleTotalScoreBin\": {\n" +
            "                                \"36客群\": \"3.(-11,1]\",\n" +
            "                                \"24客群\": \"3.(-13,1]\"\n" +
            "                            },\n" +
            "                            \"thirdTotalScore\": \"3.0\"\n" +
            "                        },\n" +
            "                        \"maScore24v1Binall\": \"6\",\n" +
            "                        \"levelV4\": {\n" +
            "                            \"36客群V10\": \"E\",\n" +
            "                            \"36客群\": \"D\",\n" +
            "                            \"24客群\": \"E\"\n" +
            "                        },\n" +
            "                        \"ruleTotalScore\": \"-4.0\",\n" +
            "                        \"softRuleScore24\": \"5.0\",\n" +
            "                        \"accountHuichuanLogic\": \"其它\",\n" +
            "                        \"lmtCap24\": 50000,\n" +
            "                        \"blackRuleParam\": {\n" +
            "                            \"blackRuleScoreAll\": {\n" +
            "                                \"blackRuleScoreV10\": \"15.0\",\n" +
            "                                \"36客群\": \"15.0\",\n" +
            "                                \"24客群\": \"15.0\"\n" +
            "                            },\n" +
            "                            \"blackRuleScore524\": \"10.0\",\n" +
            "                            \"blackRuleScore436\": \"5.0\",\n" +
            "                            \"blackRuleScore536\": \"10.0\",\n" +
            "                            \"blackRuleScore236\": \"0.0\",\n" +
            "                            \"blackRuleScore324\": \"0.0\",\n" +
            "                            \"blackRuleScore36\": \"15.0\",\n" +
            "                            \"blackRuleScore336\": \"0.0\",\n" +
            "                            \"blackRuleScore424\": \"5.0\",\n" +
            "                            \"blackRuleScore124\": \"0.0\",\n" +
            "                            \"blackRuleScore224\": \"0.0\",\n" +
            "                            \"blackRuleScore24\": \"15.0\",\n" +
            "                            \"blackRuleScore136\": \"0.0\",\n" +
            "                            \"cusV2\": \"24客群\",\n" +
            "                            \"blackRuleScoreBin\": {\n" +
            "                                \"36客群\": \"4.(10,20]\",\n" +
            "                                \"24客群\": \"4.(10,20]\"\n" +
            "                            }\n" +
            "                        },\n" +
            "                        \"liftLmtQuality24\": 0,\n" +
            "                        \"v5Mob3ScoreBin\": \"5.0\"\n" +
            "                    },\n" +
            "                    \"idcardLegalityFrontScreen\": \"0.0\",\n" +
            "                    \"sourceType\": \"信息流\",\n" +
            "                    \"levelV1\": \"L6\",\n" +
            "                    \"itemListRejectStepA\": 0,\n" +
            "                    \"itemListRejectStepC\": 0,\n" +
            "                    \"pbocV2EducationLevel\": \"-1\",\n" +
            "                    \"negativeChannel\": \"4.0\",\n" +
            "                    \"itemListRejectStepB\": 0,\n" +
            "                    \"device\": \"JLH-AN00\",\n" +
            "                    \"idcardLegalityFrontIdPhoto\": \"1.0\",\n" +
            "                    \"baseInfo\": {\n" +
            "                        \"rhCoef\": \"2.0\",\n" +
            "                        \"hjDaiqianCount60\": \"0.0\",\n" +
            "                        \"gender\": \"1.0\",\n" +
            "                        \"tdApplyAllAll12m\": \"0.0\",\n" +
            "                        \"tdApplyAllP2p6m\": \"0.0\",\n" +
            "                        \"tdApplyAllAll7d\": \"0.0\",\n" +
            "                        \"channel\": \"HEIKA\",\n" +
            "                        \"rhCoefCust\": \"2.0\",\n" +
            "                        \"hjDaiqianCount7\": \"0.0\",\n" +
            "                        \"tdApplyAllNobank6m\": \"0.0\",\n" +
            "                        \"tdApplyAllAll3m\": \"0.0\",\n" +
            "                        \"marriage\": \"已婚已育\",\n" +
            "                        \"segmentNew\": \"new_coming\",\n" +
            "                        \"tdApplyAllAll1m\": \"0.0\",\n" +
            "                        \"pbocHouseLoanAmount\": \"(-INF,0]\",\n" +
            "                        \"hjDaiqianCount360\": \"0.0\",\n" +
            "                        \"osPlatform\": \"ANDROID\",\n" +
            "                        \"pbocIncomeCust\": \"B2\",\n" +
            "                        \"hjDaiqianCount180\": \"0.0\",\n" +
            "                        \"pbocV3EducationLevel\": \"91-初中及以下\",\n" +
            "                        \"isXxl\": \"1.0\",\n" +
            "                        \"alsM3IdNbankOrgnum\": \"1.0\",\n" +
            "                        \"eduexperience\": \"70\",\n" +
            "                        \"tdApplyAllP2p3m\": \"0.0\",\n" +
            "                        \"weichengLevel\": \"None\",\n" +
            "                        \"pbocAccountTypeR2NormalCnt\": \"(1,3]\",\n" +
            "                        \"hjDaiqianCount30\": \"0.0\",\n" +
            "                        \"fundPaymentAmount\": \"-999\",\n" +
            "                        \"hjDaiqianCount90\": \"0.0\",\n" +
            "                        \"idcardNumber\": \"620523197901083798\",\n" +
            "                        \"tdApplyAllNobank7d\": \"0.0\",\n" +
            "                        \"ensembleScoreLong\": \"0.*****************\",\n" +
            "                        \"idProvince\": \"甘肃省\",\n" +
            "                        \"tdApplyAllBank6m\": \"0.0\",\n" +
            "                        \"whiteBoxVersion\": \"v3\",\n" +
            "                        \"sourceType\": \"信息流\",\n" +
            "                        \"tdApplyAllAll6m\": \"0.0\",\n" +
            "                        \"tdApplyAllNobank3m\": \"0.0\",\n" +
            "                        \"netIncome\": \"3000.0\",\n" +
            "                        \"pbocV2EducationLevel\": \"-1\",\n" +
            "                        \"tdApplyAllNobank12m\": \"0.0\",\n" +
            "                        \"brCode\": \"0.0\",\n" +
            "                        \"age\": \"44.0\",\n" +
            "                        \"tdApplyAllNobank1m\": \"0.0\"\n" +
            "                    },\n" +
            "                    \"idcardIssue\": \"甘谷县公安局\",\n" +
            "                    \"gender\": \"1.0\",\n" +
            "                    \"idcardLegalityFrontFrontWarnCode\": \"None\",\n" +
            "                    \"ypNewn\": \"0.****************\",\n" +
            "                    \"idcardLegalityFrontIdPhotoThreshold\": \"0.8\",\n" +
            "                    \"v6Mob3MktScore\": \"672.3101\",\n" +
            "                    \"userLevel\": \"F\",\n" +
            "                    \"osPlatform\": \"ANDROID\",\n" +
            "                    \"idcardLegalityFrontTemporaryIdPhoto\": \"0.0\",\n" +
            "                    \"mob3Level\": \"L3\",\n" +
            "                    \"manualTagRandom2\": \"0.************\",\n" +
            "                    \"faceChannel\": \"4.0\",\n" +
            "                    \"pbocAccountTypeR2NormalCnt\": \"(1,3]\",\n" +
            "                    \"manualTagRandom5\": \"0.************\",\n" +
            "                    \"manualTagRandom6\": \"0.************\",\n" +
            "                    \"manualTagRandom3\": \"0.************\",\n" +
            "                    \"manualTagRandom4\": \"0.***********\",\n" +
            "                    \"updateTime\": \"1.690179315e+12\",\n" +
            "                    \"rhBlank\": \"0.0\",\n" +
            "                    \"idcardAddress\": \"甘肃省甘谷县白家湾乡大山村75号\",\n" +
            "                    \"ensembleScoreLong\": \"0.*****************\",\n" +
            "                    \"whiteBoxVersion\": \"v3\",\n" +
            "                    \"createTime\": \"1.690179315e+12\",\n" +
            "                    \"cusV2Adjust\": \"24客群\",\n" +
            "                    \"maScore202107AllnScore\": \"0.****************\",\n" +
            "                    \"random18Smz\": \"0.425273521066\",\n" +
            "                    \"positiveChannel\": \"4.0\",\n" +
            "                    \"mob6Level\": \"L3\",\n" +
            "                    \"operatorInd\": \"1\",\n" +
            "                    \"idcardName\": \"李云杰\",\n" +
            "                    \"channelCodeNew\": \"None\",\n" +
            "                    \"v6Mob3MainScore\": \"684.0509\",\n" +
            "                    \"lineDeciseParam\": {\n" +
            "                        \"irr24Flag\": 0,\n" +
            "                        \"finalDeciseAmount\": \"3600.0\",\n" +
            "                        \"strategyType\": \"v1策略\"\n" +
            "                    },\n" +
            "                    \"liveRate\": \"-999.0\",\n" +
            "                    \"pidImageExists1\": \"1.0\",\n" +
            "                    \"rejectFlag24\": 0,\n" +
            "                    \"pbocHouseLoanAmount\": \"(-INF,0]\",\n" +
            "                    \"irrCusFlag\": \"24\",\n" +
            "                    \"v1StrategyLineParam\": {\n" +
            "                        \"cusV2Adjust\": \"24客群\",\n" +
            "                        \"finalAmt\": \"3600.0\",\n" +
            "                        \"levelV4Adjust\": \"E\",\n" +
            "                        \"irr36BaffleFlag\": 0,\n" +
            "                        \"irr24Flag\": 0,\n" +
            "                        \"specialRateTag\": 4.8\n" +
            "                    },\n" +
            "                    \"pbocV3EducationLevel\": \"91-初中及以下\",\n" +
            "                    \"idcardLegalityBackFrontWarnCode\": \"None\",\n" +
            "                    \"idcardLegalityBackScreen\": \"0.0\",\n" +
            "                    \"manualTagRandom\": \"0.664736618016\",\n" +
            "                    \"newTag\": \"new_coming\",\n" +
            "                    \"sourceTypeNew\": \"信息流\",\n" +
            "                    \"weichengLevel\": \"None\",\n" +
            "                    \"idcardLegalityBackBackWarnCode\": \"None\",\n" +
            "                    \"agentName\": \"百度-吉狮-LA8\",\n" +
            "                    \"idcardNumber\": \"620523197901083798\",\n" +
            "                    \"userKey\": \"8a25958d563a6a2be15cd288264c777c\",\n" +
            "                    \"loanAssignInfo\": {\n" +
            "                        \"rec90dLoanappEarlyMorningCnt\": \"0.0\",\n" +
            "                        \"rec7dLoanappCnt\": \"1.0\",\n" +
            "                        \"bgkProductFlag\": 3,\n" +
            "                        \"rhBlank\": \"0.0\",\n" +
            "                        \"avgDiffBadappTime\": \"-999.0\",\n" +
            "                        \"strategyType\": \"v1策略\",\n" +
            "                        \"loanappCnt\": \"4.0\",\n" +
            "                        \"levelV4Adjust\": \"E\",\n" +
            "                        \"cusV2Adjust\": \"24客群\",\n" +
            "                        \"irr36BaffleFlag\": 0,\n" +
            "                        \"random3\": \"0.************\",\n" +
            "                        \"finalDeciseAmount\": \"3600.0\",\n" +
            "                        \"isXxl\": \"1.0\"\n" +
            "                    },\n" +
            "                    \"idcardNation\": \"汉\",\n" +
            "                    \"idNation\": \"汉\",\n" +
            "                    \"B036LaohuiTag\": 0,\n" +
            "                    \"baffleInfo\": {\n" +
            "                        \"isIrr36Cap\": 0,\n" +
            "                        \"strategyType\": \"v1策略\"\n" +
            "                    },\n" +
            "                    \"bRuleHitFlag\": 0,\n" +
            "                    \"maScoreFpdv4Prob\": \"0.014414600096642971\",\n" +
            "                    \"B102-1\": \"1\",\n" +
            "                    \"cusSeg\": \"B\",\n" +
            "                    \"idcardLegalityBackTemporaryIdPhoto\": \"0.0\",\n" +
            "                    \"mob3LevelV2\": \"L3\",\n" +
            "                    \"rpRandom9\": \"0.331619112653\",\n" +
            "                    \"isPbocQuery\": 1,\n" +
            "                    \"rpRandom8\": \"0.664902920053\",\n" +
            "                    \"rpRandom7\": \"0.927896255812\",\n" +
            "                    \"idcardLegalityBackEdited\": \"0.0\",\n" +
            "                    \"rpRandom6\": \"0.787347534098\",\n" +
            "                    \"idcardLegalityBackPhotoCopy\": \"0.0\",\n" +
            "                    \"rpRandom5\": \"0.416942549812\",\n" +
            "                    \"idcardLegalityBackIdPhoto\": \"1.0\",\n" +
            "                    \"rpRandom4\": \"0.290821754523\",\n" +
            "                    \"isPdsbReject\": 0,\n" +
            "                    \"rpRandom3\": \"0.************\",\n" +
            "                    \"rpRandom2\": \"0.************\",\n" +
            "                    \"rpRandom1\": \"0.************\",\n" +
            "                    \"strategyType\": \"v1策略\",\n" +
            "                    \"AppVersion\": \"860\",\n" +
            "                    \"operationLogId\": \"**********\",\n" +
            "                    \"rejectFlag36B\": 0,\n" +
            "                    \"alsD7IdNbankOrgnum\": \"-999.0\",\n" +
            "                    \"rejectFlag36C\": 0,\n" +
            "                    \"rejectFlag24B\": 0,\n" +
            "                    \"idcardLegalityBackIdPhotoThreshold\": \"0.8\",\n" +
            "                    \"rejectFlag24C\": 0,\n" +
            "                    \"flowTag\": \"V1.2\",\n" +
            "                    \"maScoreMob3ascore202206\": \"0.****************\",\n" +
            "                    \"v6Mob1MktScore\": \"631.9895\",\n" +
            "                    \"idcardLegalityFrontEdited\": \"0.0\",\n" +
            "                    \"phoneRegister\": \"***********\",\n" +
            "                    \"maScoreMob6ascore202206\": \"621.0\",\n" +
            "                    \"rpRandom19\": \"0.************\",\n" +
            "                    \"rpRandom15\": \"0.************\",\n" +
            "                    \"tianjiTag\": \"tianji_v1\",\n" +
            "                    \"rpRandom10\": \"0.823229106718\",\n" +
            "                    \"idcardLegalityFrontBackWarnCode\": \"None\",\n" +
            "                    \"levelV4Adjust\": \"E\",\n" +
            "                    \"rejectFlag36\": 0,\n" +
            "                    \"netIncome\": \"3000.0\",\n" +
            "                    \"ageId\": \"44.0\",\n" +
            "                    \"age\": \"44.0\"\n" +
            "                }";

    @Test
    public void specialRateTagHandler() {
        VerifyResult verifyResult = new VerifyResult();
        verifyResult.setReasonCode(reasonCode);

        hhFLWriteBackTaskService.specialRateTagHandler(verifyResult);
    }
}