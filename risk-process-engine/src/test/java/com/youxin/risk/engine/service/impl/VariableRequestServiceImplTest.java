package com.youxin.risk.engine.service.impl;

import com.youxin.risk.engine.clients.VariableCenterClient;
import com.youxin.risk.engine.vo.VariableCenterResponse;
import com.youxin.risk.engine.vo.VariableRequestParams;
import org.junit.Test;
import org.mockito.Mockito;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

public class VariableRequestServiceImplTest {

    @Test
    public void requestVariable() {
        //
        VariableCenterClient variableCenterClient = Mockito.mock(VariableCenterClient.class);
        VariableRequestServiceImpl variableRequestService = new VariableRequestServiceImpl(variableCenterClient);

        //网络异常返回
        when(variableCenterClient.requestVariableCenter(any())).thenReturn(VariableCenterResponse.error("net error"));
        boolean flag = variableRequestService.requestVariable(VariableRequestParams.builder().build());
        assertFalse(flag);


        when(variableCenterClient.requestVariableCenter(any())).thenReturn(VariableCenterResponse.error("返回空值",
                VariableCenterResponse.RESPONSE_BLANK_ERROR));
        flag = variableRequestService.requestVariable(VariableRequestParams.builder().build());
        assertFalse(flag);

        when(variableCenterClient.requestVariableCenter(any())).thenReturn(VariableCenterResponse.build(null));
        flag = variableRequestService.requestVariable(VariableRequestParams.builder().build());
        assertFalse(flag);

        when(variableCenterClient.requestVariableCenter(any())).thenReturn(VariableCenterResponse.build("{\"status\":0}"));
        flag = variableRequestService.requestVariable(VariableRequestParams.builder().build());
        assertTrue(flag);

    }
}