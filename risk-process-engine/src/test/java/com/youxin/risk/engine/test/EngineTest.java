package com.youxin.risk.engine.test;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.youxin.risk.commons.apollo.ApolloClientAdapter;
import com.youxin.risk.commons.constants.SourceSystemEnum;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.utils.ContextUtil;
import com.youxin.risk.commons.utils.SystemUtil;
import com.youxin.risk.engine.activiti.context.ProcessContext;
import com.youxin.risk.engine.activiti.runtime.ProcessEngine;
import com.youxin.risk.engine.activiti.runtime.ProcessEngines;
import com.youxin.risk.engine.activiti.runtime.ProcessInstance;
import org.apache.commons.io.IOUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mvel2.MVEL;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.io.FileInputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.youxin.risk.commons.apollo.ApolloNamespaceEnum.FS_SPACE;
import static com.youxin.risk.commons.constants.ApolloKey.SAVE_ENTER_FEATURE_TIME_EVENT;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration("classpath:spring/spring-config.xml")
public class EngineTest {
    private static final String DC_PLATFORM_URI = "/engine/getLastNotNullPlatform";

    @Value("${dc.inside.url}")
    private String dcInsideUrl;
    @Test
    public void testStart() {
        System.out.println(ContextUtil.getBean("testService"));
    }


    @Test
    public void testProcessEngine() throws Exception {
        List<String> lines = IOUtils.readLines(new FileInputStream(SystemUtil.getAbsolutePath("activiti/process_prea.xml")));
        String xml = "";
        for (String line : lines) {
            xml += line;
        }
        ProcessEngines.registerProcessEngine(xml);

        ProcessEngine pe = ProcessEngines.getProcessEngine("processPrea");
        ProcessInstance pi = pe.buildInstance();

        Map<String, Object> map = new HashMap<>();
        map.put("name", "Velocity");
        map.put("project", "Jakarta");
        map.put("foo", "11");
        map.put("testkey", "345");
        map.put("event", new Event());

        ProcessContext context = new ProcessContext(pi);
        context.setVariables(map);
        pi.execute(context);

//        System.out.println(pi);
    }

    @Test
    public void testProcess() {
        String message = "{\"params\":{\"eventCode\":\"prepareHaoHuanApply\",\"appVersion\":\"230\",\"jailBroken\":0,\"sourceSystem\":\"HAO_HUAN\",\"latitude\":\"39.999563\",\"wifiSSID\":\"YOUXIN-WiFi\",\"ip\":\"************\",\"channel\":\"oppo\",\"deviceName\":\"OPPOR11s\",\"deviceId\":\"00000000-3871-d5db-0000-0000152b0e9a-14de8a112f613d7d\",\"userKey\":\"d0a1eb466cb67a562fef590133dbf163\",\"platform\":\"Android\",\"batteryPlugType\":\"1\",\"osVersion\":\"7.1.1\",\"mobileModel\":\"OPPOR11s\",\"userStatusInfo\":{\"taobaoStatus\":null,\"taobaoTime\":0,\"alipayStatus\":\"0\",\"alipayTime\":0,\"creditCardStatus\":\"0\",\"creditCardTime\":0,\"phoneInfoStatus\":1,\"isShowCreditCard\":1},\"wifiMac\":\"38:17:c3:0b:75:e2\",\"wifiLevel\":\"4\",\"device\":\"OPPO R11s\",\"isCopyPackage\":0,\"loanId\":66666788,\"longitude\":\"116.336788\",\"batteryLevel\":63,\"channelCode\":\"ucredit\"}}";
        Event event = JSONObject.parseObject(message, Event.class);
        event.setEventCode("prepareHaoHuanApply");
//        processEngineService.handler(event);
        System.out.println(event.getDataVo());
        System.out.println(event.getXml());
    }

    @Test
    public void testVerifyTransactionProcess() throws InterruptedException {
        String message = "{\"params\":{\"type\":\"async\",\"requestId\":\"20170601195303_A32284815745610721\",\"message\":{\"eventCode\":\"haoHuanTransaction\",\"userKey\":\"0025af938e44cc3a691d59dfa253739c\",\"appVersion\":\"440\",\"latitude\":\"23.093549\",\"longitude\":\"113.846038\",\"bankMobile\":[\"***********\",\"***********\"],\"emergencyContact\":[{\"mobile\":\"***********\",\"relationType\":\"配偶\"},{\"mobile\":\"***********\",\"relationType\":\"朋友\"}],\"ip\":\"************\",\"orderHasFirstPay\":0,\"orderId\":\"****************\",\"orderTime\":\"*************\",\"receiverAddress\":\"广东东莞市茶山镇京山第三工业区威利广\",\"receiverMobile\":\"***********\",\"receiverName\":\"吴*平\",\"registerMobile\":\"***********\",\"tdFingerprint\":\"eyJvcyI6ImFuZHJvaWQiLCJ2ZXJzaW9uIjoiMy4xLjciLCJwYWNrYWdl\",\"transaction\":[{\"productBranch\":\"\",\"productCount\":\"1\",\"productName\":\"木林森（MULINSEN）男鞋 时尚潮流运动\",\"productPeriodNo\":\"9\",\"productPrice\":\"149\",\"productType\":\"\",\"totalPrice\":\"149\"}],\"wifiMac\":\"null\",\"loan\":\"********\",\"loanId\":1241,\"msgType\":\"transaction\",\"orderNo\":\"****************\",\"sourceSystem\":\"HAO_HUAN\",\"systemId\":\"hh\"}}}";
        Event event = JSONObject.parseObject(message, Event.class);
        event.setEventCode("haoHuanTransaction");
        event.setSourceSystem(SourceSystemEnum.HAO_HUAN.name());
        event.getParams().putAll(event.get("message", Map.class));
//        processEngineService.handler(event);
        Thread.sleep(60 * 60 * 1000);
        System.out.println(event.getDataVo());
        System.out.println(event.getXml());
    }

    @Test
    public void testHhVerify() throws InterruptedException {
//        EventVo eventVo = eventDao.getBySessionId("**************_2821457779106932655");
        String message = "{\"agencyCode\":\"\",\"currentNodeId\":\"\",\"dataVo\":{\"userLevelConfigList\":[{\"amountMax\":\"2000.0\",\"userLevel\":\"A\",\"amountMin\":\"2000.0\",\"installmentAmountMax\":\"2000.0\",\"rateMax\":\"0.003\",\"rateMin\":\"0.003\",\"scoreMax\":900,\"scoreMin\":700},{\"amountMax\":\"1800.0\",\"userLevel\":\"B\",\"amountMin\":\"1800.0\",\"installmentAmountMax\":\"1800.0\",\"rateMax\":\"0.003\",\"rateMin\":\"0.003\",\"scoreMax\":700,\"scoreMin\":600},{\"amountMax\":\"1500.0\",\"userLevel\":\"C\",\"amountMin\":\"1500.0\",\"installmentAmountMax\":\"1500.0\",\"rateMax\":\"0.004\",\"rateMin\":\"0.004\",\"scoreMax\":600,\"scoreMin\":550},{\"amountMax\":\"1300.0\",\"userLevel\":\"D\",\"amountMin\":\"1300.0\",\"installmentAmountMax\":\"1300.0\",\"rateMax\":\"0.004\",\"rateMin\":\"0.004\",\"scoreMax\":550,\"scoreMin\":525},{\"amountMax\":\"1100.0\",\"userLevel\":\"E\",\"amountMin\":\"1100.0\",\"installmentAmountMax\":\"1100.0\",\"rateMax\":\"0.006\",\"rateMin\":\"0.006\",\"scoreMax\":525,\"scoreMin\":450},{\"amountMax\":\"1000.0\",\"userLevel\":\"F\",\"amountMin\":\"1000.0\",\"installmentAmountMax\":\"1000.0\",\"rateMax\":\"0.006\",\"rateMin\":\"0.006\",\"scoreMax\":449,\"scoreMin\":0}],\"REGISTER\":\"{\\\"id\\\":17249,\\\"createTime\\\":\\\"20180709203446\\\",\\\"updateTime\\\":\\\"20180709203446\\\",\\\"operationLogId\\\":43105,\\\"userKey\\\":\\\"3cb36a822898b02b2d711cb5eec583f5\\\",\\\"mobile\\\":\\\"***********\\\",\\\"tongdunFingerprint\\\":\\\"eyJvcyI6IkFuZHJvaWQiLCJ2ZXJzaW9uIjoiMi4xLjQiLCJzZXNzaW9uX2lkIjoidWNyZWRpdDk3ZTExODFiNDMxMWNlNGM4YzA3MzQ0YmQzMTk2ZjMzIiwiZGV2aWNlX2lkIjoiTEZcL1dcL1YzWVhcL04xN0FxMDlxRlg5XC9vNFRsVlE3RTY4N29FOFRFYzM0RWMwSkRJejJpNm5JNFluWE5yZmRMcD0iLCJidW5kbGUiOiJjb20ucmVucmVuZGFpLmhhb2h1YW5fMjMwIiwiZGF0ZSI6IjE1MzExMzkzMDQ5MjciLCJkYXRhIjoibzVsR1RDaExMU3FsNCtGZVhFRkhXbGhOTEpLPSJ9\\\"}\",\"livingAddressPinpoint\":\"False\",\"score\":\"451.0\",\"userLevel\":\"E\",\"ADDRESS\":\"{\\\"id\\\":4578,\\\"createTime\\\":\\\"20180605202842\\\",\\\"updateTime\\\":\\\"20180605202842\\\",\\\"operationLogId\\\":67506,\\\"userKey\\\":\\\"3cb36a822898b02b2d711cb5eec583f5\\\",\\\"province\\\":\\\"北京市\\\",\\\"city\\\":\\\"北京市\\\",\\\"district\\\":\\\"东城区\\\",\\\"liveAddress\\\":\\\"123456\\\",\\\"marriage\\\":\\\"2_1_未婚\\\"}\",\"submitJobVo\":{\"industry\":\"4_1_政府、非盈利机构\",\"companyPosition\":\"5_1_工薪族\",\"salary\":\"6_1_小于1000元\",\"payDay\":\"7_1_1\",\"userKey\":\"3cb36a822898b02b2d711cb5eec583f5\",\"createTime\":1528201722000},\"JOB\":\"{\\\"id\\\":1950,\\\"createTime\\\":\\\"20180605202842\\\",\\\"updateTime\\\":\\\"20180605202842\\\",\\\"operationLogId\\\":11152,\\\"userKey\\\":\\\"3cb36a822898b02b2d711cb5eec583f5\\\",\\\"industry\\\":\\\"4_1_政府、非盈利机构\\\",\\\"companyPosition\\\":\\\"5_1_工薪族\\\",\\\"salary\\\":\\\"6_1_小于1000元\\\",\\\"payDay\\\":\\\"7_1_1\\\"}\",\"ID_CARD\":\"{\\\"id\\\":3846,\\\"createTime\\\":\\\"20180522121032\\\",\\\"updateTime\\\":\\\"20190228144047\\\",\\\"operationLogId\\\":63663,\\\"userKey\\\":\\\"3cb36a822898b02b2d711cb5eec583f5\\\",\\\"idcardNumber\\\":\\\"350426198509113027\\\",\\\"idcardName\\\":\\\"陈佳佳\\\",\\\"idcardAddress\\\":\\\"福建省尤溪县汤川乡汤三村16号\\\",\\\"pidPositiveUrl\\\":\\\"pid/positivePid/09/12/912_1526961827.jpg\\\",\\\"pidNegativeUrl\\\":\\\"pid/negativePid/09/12/912_1526961842.jpg\\\",\\\"faceUrl\\\":\\\"face/face/09/12/f4250f9f321647c0f0e27aa83ea2e3f3.jpg\\\",\\\"faceScore\\\":\\\"44.249\\\",\\\"faceThreshold\\\":\\\"{\\\\\\\"1e-3\\\\\\\":62.169,\\\\\\\"1e-4\\\\\\\":69.315,\\\\\\\"1e-5\\\\\\\":74.399,\\\\\\\"1e-6\\\\\\\":78.038}\\\",\\\"pidAuth\\\":true,\\\"idcardNation\\\":\\\"汉\\\",\\\"idcardIssue\\\":\\\"上海市公安局徐汇分局\\\",\\\"idcardValid\\\":\\\"2005.10.08-2025.10.08\\\",\\\"idcardLegalityFront\\\":\\\"{\\\\\\\"edited\\\\\\\":0.001,\\\\\\\"photocopy\\\\\\\":0.004,\\\\\\\"screen\\\\\\\":0.991,\\\\\\\"idphoto\\\\\\\":0.004,\\\\\\\"temporary_id_photo\\\\\\\":0}\\\",\\\"idcardLegalityBack\\\":\\\"{\\\\\\\"edited\\\\\\\":0.001,\\\\\\\"photocopy\\\\\\\":0,\\\\\\\"screen\\\\\\\":0.999,\\\\\\\"idphoto\\\\\\\":0,\\\\\\\"temporary_id_photo\\\\\\\":0}\\\",\\\"faceGenuineness\\\":\\\"{\\\\\\\"synthetic_face_confidence\\\\\\\":0,\\\\\\\"synthetic_face_threshold\\\\\\\":0.5,\\\\\\\"mask_confidence\\\\\\\":0,\\\\\\\"mask_threshold\\\\\\\":0.5,\\\\\\\"screen_replay_confidence\\\\\\\":0,\\\\\\\"screen_replay_threshold\\\\\\\":0.5,\\\\\\\"face_replaced\\\\\\\":0}\\\"}\",\"XW\":\"{\\\"id\\\":2119,\\\"createTime\\\":\\\"20180709205042\\\",\\\"updateTime\\\":\\\"20180709205042\\\",\\\"loanId\\\":1178,\\\"userKey\\\":\\\"3cb36a822898b02b2d711cb5eec583f5\\\",\\\"sourceSystem\\\":\\\"HAO_HUAN\\\",\\\"mobile\\\":\\\"***********\\\",\\\"name\\\":\\\"邢丽杰\\\",\\\"certNo\\\":\\\"131127199205207002\\\",\\\"certDate\\\":\\\"2005-10-08\\\",\\\"certExpiryDate\\\":\\\"2025-10-08\\\",\\\"certState\\\":\\\"2\\\",\\\"certCity\\\":\\\"36\\\",\\\"certDistCode\\\":\\\"36\\\",\\\"certPlace\\\":\\\"福建省尤溪县汤川乡汤三村16号\\\",\\\"certAuthority\\\":\\\"上海市公安局徐汇分局\\\",\\\"gender\\\":\\\"2\\\",\\\"ethnic\\\":\\\"01\\\",\\\"maritalStatus\\\":\\\"1\\\",\\\"livingCondition\\\":\\\"9\\\",\\\"addressType\\\":\\\"01\\\",\\\"provinceCode\\\":\\\"2\\\",\\\"cityCode\\\":\\\"36\\\",\\\"addrStreet\\\":\\\"123456\\\",\\\"bankCardno\\\":\\\"6221885200053349249\\\",\\\"bankMobile\\\":\\\"***********\\\",\\\"loanAmount\\\":\\\"3000\\\",\\\"loanPurpose\\\":\\\"06\\\",\\\"loanTenor\\\":\\\"2\\\",\\\"loanTenorUnit\\\":\\\"01\\\",\\\"rate\\\":\\\"120000\\\",\\\"idcardF\\\":\\\"http://************/haohuan/pid/positivePid/09/12/912_1526961827.jpg\\\",\\\"idcardB\\\":\\\"http://************/haohuan/pid/negativePid/09/12/912_1526961842.jpg\\\",\\\"bankCardType\\\":\\\"D\\\"}\"},\"eventCode\":\"haoHuanVerify\",\"loanKey\":\"hh_**************_2821457779106932655\",\"occurTime\":\"**************\",\"params\":{\"isSumOverdueGt3t\":\"false\",\"appVersion\":\"472\",\"sourceSystem\":\"HAO_HUAN\",\"latitude\":\"40.************\",\"wifiSSID\":\"TP-LINK_5G_6746\",\"occurTime\":\"**************\",\"channel\":\"AppStore\",\"deviceId\":\"ccc44777e748b384f3cc7493ba7f03b1dffb0ca2\",\"deviceName\":\"iPhone\",\"platform\":\"iPhone\",\"hasNotSettled\":\"false\",\"osVersion\":\"11.4.1\",\"mobileModel\":\"iPhone10,3\",\"userStatusInfo\":\"{\\\"taobaoStatus\\\":\\\"1\\\",\\\"taobaoTime\\\":*************,\\\"alipayStatus\\\":\\\"0\\\",\\\"alipayTime\\\":0,\\\"creditCardStatus\\\":\\\"0\\\",\\\"creditCardTime\\\":0,\\\"phoneInfoStatus\\\":0,\\\"isShowCreditCard\\\":0}\",\"agencyRequestId\":\"7dd131733c1dfa21809999\",\"loanKey\":\"hh_**************_2821457779106932655\",\"wifiLevel\":\"0\",\"channelCode\":\"AppStore\",\"longitude\":\"111.***********\",\"batteryLevel\":\"32\",\"icode\":\"0100010001\",\"jailBroken\":\"0\",\"ip\":\"*************, ***********\",\"currentBalance\":\"0\",\"lowBatteryMode\":\"0\",\"loanCount\":\"0\",\"isMaxOverdueGt10d\":\"false\",\"zaCustType\":\"1\",\"sessionId\":\"**************_2821457779106932655\",\"userKey\":\"3cb36a822898b02b2d711cb5eec583f5\",\"eventCode\":\"haoHuanVerify\",\"authVersion\":\"1\",\"batteryPlugType\":\"1\",\"isOverdueGt1d\":\"false\",\"datakeyJobId\":\"eb2fa4c0-8b45-459d-a2d4-d838819ed0b5\",\"wifiMac\":\"dc:fe:18:9c:67:48\",\"device\":\"iPhone10,3\",\"isCopyPackage\":\"0\",\"loanId\":\"4447685\",\"firstLoanTime\":\"\",\"processSplitFlowType\":\"ONLINE\",\"processDefId\":\"hhVerify\",\"isPreAReject\":false,\"step\":\"A\",\"dataInput\":{\"isSumOverdueGt3t\":\"false\",\"appVersion\":\"472\",\"sourceSystem\":\"HAO_HUAN\",\"latitude\":\"40.************\",\"wifiSSID\":\"TP-LINK_5G_6746\",\"occurTime\":\"**************\",\"isPreAReject\":false,\"channel\":\"AppStore\",\"deviceId\":\"ccc44777e748b384f3cc7493ba7f03b1dffb0ca2\",\"deviceName\":\"iPhone\",\"platform\":\"iPhone\",\"hasNotSettled\":\"false\",\"processSplitFlowType\":\"ONLINE\",\"osVersion\":\"11.4.1\",\"mobileModel\":\"iPhone10,3\",\"userStatusInfo\":\"{\\\"taobaoStatus\\\":\\\"1\\\",\\\"taobaoTime\\\":*************,\\\"alipayStatus\\\":\\\"0\\\",\\\"alipayTime\\\":0,\\\"creditCardStatus\\\":\\\"0\\\",\\\"creditCardTime\\\":0,\\\"phoneInfoStatus\\\":0,\\\"isShowCreditCard\\\":0}\",\"agencyRequestId\":\"7dd131733c1dfa21809999\",\"loanKey\":\"hh_**************_2821457779106932655\",\"processDefId\":\"hhVerify\",\"wifiLevel\":\"0\",\"channelCode\":\"AppStore\",\"longitude\":\"111.***********\",\"batteryLevel\":\"32\",\"icode\":\"0100010001\",\"jailBroken\":\"0\",\"systemId\":\"HAO_HUAN\",\"ip\":\"*************, ***********\",\"currentBalance\":\"0\",\"lowBatteryMode\":\"0\",\"loanCount\":\"0\",\"isMaxOverdueGt10d\":\"false\",\"zaCustType\":\"1\",\"sessionId\":\"**************_2821457779106932655\",\"userKey\":\"3cb36a822898b02b2d711cb5eec583f5\",\"eventCode\":\"haoHuanVerify\",\"authVersion\":\"1\",\"batteryPlugType\":\"1\",\"isOverdueGt1d\":\"false\",\"datakeyJobId\":\"eb2fa4c0-8b45-459d-a2d4-d838819ed0b5\",\"wifiMac\":\"dc:fe:18:9c:67:48\",\"step\":\"A\",\"device\":\"iPhone10,3\",\"isCopyPackage\":\"0\",\"loanId\":\"4447685\",\"firstLoanTime\":\"\"}},\"requestType\":\"apply\",\"sessionId\":\"**************_2821457779106932655\",\"sourceSystem\":\"HAO_HUAN\",\"userKey\":\"3cb36a822898b02b2d711cb5eec583f5\",\"xml\":\"\"}";
        Event event = JSONObject.parseObject(message, Event.class);
//        event.setEventCode("hhVerify");
        event.setEventCode("haoHuanVerify");
//        processEngineService.handler(event);
        System.out.println(event.getDataVo());
        System.out.println(event.getXml());
        Thread.sleep(60 * 60 * 1000);
    }

    @Test
    public void testRegisterProcess() throws Exception {
        List<String> lines = IOUtils.readLines(new FileInputStream(SystemUtil.getAbsolutePath("activiti/process_rrd_daizhong.xml")));
        String xml = "";
        for (String line : lines) {
            xml += line;
        }
        ProcessEngines.registerProcessEngine(xml);
    }

    @Test
    public void testQuery() {
        List<String> eventList = ApolloClientAdapter.getListConfig(FS_SPACE, SAVE_ENTER_FEATURE_TIME_EVENT, String.class);
        System.out.println();
    }

    public static void main(String[] args) {
        String expression = "if(dataVo.?getLastIncomeCacheDataService == null && params.?userKey != null) {return java.lang.Math.abs(params.?userKey.hashCode()) % 2 == 0;} else {return false}";
        String mapStr = "{\"agencyCode\":\"\",\"batch\":false,\"dataId\":{},\"dataVo\":{\"randoms\":{\"random2\":266,\"random1\":489,\"random4\":114,\"random3\":591,\"random5\":257},\"cachedDataCode\":{},\"dataRef\":{}},\"eventCode\":\"hhEngineTestBZG\",\"eventCodeForPoint\":\"hhEngineTestBZG\",\"loanKey\":\"hh_20231109152718_4497613543488467632\",\"occurTime\":\"20231109152718\",\"params\":{\"eventCode\":\"hhEngineTestBZG\",\"icode\":\"0100030001\",\"gatewayCreateTime\":1699514838781,\"sourceSystem\":\"HAO_HUAN\",\"occurTime\":\"20231109152718\",\"agencyRequestId\":\"6db20f4a-bd00-400e-a0b9-395b405a1ac4-1\",\"isBatchEvent\":false,\"sessionId\":\"20231109152718_4497613543488467632\",\"loanKey\":\"hh_20231109152718_4497613543488467632\",\"userKey1\":\"c5495bf12c3d1916ac0038f163a19fcd\"},\"requestType\":\"\",\"sessionId\":\"20231109152718_4497613543488467632\",\"simpleData\":{},\"sourceSystem\":\"HAO_HUAN\",\"userKey\":\"c5495bf12c3d1916ac0038f163a19fcd\",\"xml\":\"\"}";
        Map<String, Object> ctx = JSONObject.parseObject(mapStr, Map.class);
        Object eval = MVEL.eval(expression, ctx);
        System.out.println(eval);
        System.out.println(Math.abs("c5495bf12c3d1916ac0038f163a19fcd".hashCode()) % 2);
    }

    @Test
    public void testQueryPlatform(){
        String url = dcInsideUrl + DC_PLATFORM_URI + "?userKey=" + "fc31ac2413b5d38ca15cf540e84e3677";
        String result = SyncHTTPRemoteAPI.get(url,30 * 60 * 1000);
        String platform = (String) JSONPath.read(result,"$.data.platform");
        System.out.println(platform);
    }
}
