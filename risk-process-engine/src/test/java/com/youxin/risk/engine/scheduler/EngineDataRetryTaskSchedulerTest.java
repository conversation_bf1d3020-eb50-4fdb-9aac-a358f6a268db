package com.youxin.risk.engine.scheduler;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.junit.Assert.*;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration("classpath:spring/spring-config.xml")
public class EngineDataRetryTaskSchedulerTest {

    @Autowired
    private EngineDataRetryTaskScheduler EngineDataRetryTaskScheduler;
    @Test
    public void process() {
        EngineDataRetryTaskScheduler.process();
        System.out.println("aaa");
    }
}