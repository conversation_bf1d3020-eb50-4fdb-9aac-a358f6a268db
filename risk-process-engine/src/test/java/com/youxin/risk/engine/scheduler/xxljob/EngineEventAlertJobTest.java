package com.youxin.risk.engine.scheduler.xxljob;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.junit.Assert.*;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration("classpath:spring/spring-config.xml")
public class EngineEventAlertJobTest {

    @Autowired
    private EngineEventAlertJob engineEventAlertJob;
    @Autowired
    private EngineQuickAlertJob engineQuickAlertJob;

    @Test
    public void execJobHandler() {
//        engineQuickAlertJob.execJobHandler("");
        engineEventAlertJob.execJobHandler("");
    }
}