<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns="http://www.springframework.org/schema/beans"
       xmlns:task="http://www.springframework.org/schema/task"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
     http://www.springframework.org/schema/beans/spring-beans-4.0.xsd



     http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-4.0.xsd">


    <bean id="xxlJobConfig" class="com.youxin.risk.engine.conf.XxlJobConfig"/>

    <bean id="engineEventInitTaskScheduler" class="com.youxin.risk.engine.scheduler.EngineEventInitTaskScheduler">
        <property name="lockName" value="engine_event_init_task_scheduler"/>
    </bean>
    <bean id="engineEventReadyTaskScheduler" class="com.youxin.risk.engine.scheduler.EngineEventReadyTaskScheduler">
        <property name="lockName" value="engine_event_ready_task_scheduler"/>
    </bean>

    <bean id="engineEventSuspendedTaskScheduler" class="com.youxin.risk.engine.scheduler.EngineEventSuspendedTaskScheduler">
        <property name="lockName" value="engine_event_suspended_task_scheduler"/>
    </bean>

    <bean id="engineDataWaitTimeoutTaskScheduler" class="com.youxin.risk.engine.scheduler.EngineDataWaitTimeoutTaskScheduler">
        <property name="lockName" value="engine_data_wait_timeout_task_scheduler"/>
    </bean>

    <bean id="updateNodeDataParamsScheduler" class="com.youxin.risk.engine.scheduler.UpdateNodeDataParamsScheduler">
    </bean>

<!--    <task:executor id="engineDataRetryExecutor" pool-size="5-10" queue-capacity="200" rejection-policy="CALLER_RUNS"/>-->

    <task:executor id="executor" pool-size="5" />
    <task:scheduler id="scheduler" pool-size="5" />

    <task:annotation-driven scheduler="scheduler" executor="executor" proxy-target-class="true"/>
    <!--如果修改定时任务频率，需要更改此处com.youxin.risk.commons.scheduler.BaseScheduler.isExecute衰减策略-->
    <task:scheduled-tasks scheduler="scheduler">
        <task:scheduled ref="updateNodeDataParamsScheduler" method="updateCache" cron="0/30 * * * * ?"/>
    </task:scheduled-tasks>
</beans>
