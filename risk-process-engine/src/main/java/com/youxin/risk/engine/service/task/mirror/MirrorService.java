package com.youxin.risk.engine.service.task.mirror;

import com.youxin.risk.commons.model.Event;

/**
 * 镜像服务接口
 * 定义了镜像服务的核心功能和类型
 *
 * <AUTHOR>
 */
public interface MirrorService {
    
    /**
     * 启动镜像流程
     * 根据事件和流程定义ID启动对应的镜像流程
     *
     * @param event 事件对象
     * @param processDefId 流程定义ID
     * @return 是否成功启动镜像
     */
    boolean startMirror(final Event event, String processDefId);

    /**
     * 获取镜像类型
     * 用于工厂模式中识别不同的镜像服务实现
     *
     * @return 镜像类型枚举值
     */
    MirrorType getMirrorType();

    /**
     * 镜像类型枚举
     * 定义了系统支持的镜像类型
     */
    enum MirrorType {
        /**
         * 策略类型镜像
         * 用于子流程的镜像处理
         */
        STRATEGY_TYPE,
        
        /**
         * 事件镜像
         * 用于主流程的镜像处理
         */
        EVENT
    }
}
