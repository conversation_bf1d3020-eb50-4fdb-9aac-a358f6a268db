package com.youxin.risk.engine.scheduler.xxljob;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import com.youxin.risk.commons.xxl.job.XxlJobBase;
import com.youxin.risk.engine.scheduler.EngineDataRetryTaskScheduler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class EngineDataRetryJob implements XxlJobBase {

    private static final Logger logger = LoggerFactory.getLogger(EngineDataRetryJob.class);

    @Autowired
    private EngineDataRetryTaskScheduler engineDataRetryTaskScheduler;

    @XxlJob(value="engineDataRetryJob")
    @Override
    public ReturnT<String> execJobHandler(String param) {
        try {
            logger.info("xxljob:engineDataRetryJob execute start");
            engineDataRetryTaskScheduler.process();
            logger.info("xxljob:engineDataRetryJob execute end");
        }catch (Exception e){
            XxlJobLogger.log("执行{}失败，失败信息为：","engineDataRetryJob",e);
            return  ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }


}
