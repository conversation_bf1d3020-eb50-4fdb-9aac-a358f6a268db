package com.youxin.risk.engine.utils;

import com.youxin.risk.commons.event.annotation.EventAnnotation;
import com.youxin.risk.commons.kafkav2.sender.KafkaDynamicSender;
import com.youxin.risk.commons.kafkav2.sender.KafkaSyncSender;
import com.youxin.risk.conf.KafkaMirrorConfiguration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;


/**
 * @ClassName EventUtils
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/12/21
 **/
@Component
@Lazy(false)
public class EventUtils {

    private final static Logger LOG = LoggerFactory.getLogger(EventUtils.class);

    public static final String KAFKA_SEND_EVENT = "kafka.send.event";
    public static final String KAFKA_TEMPLATE_ASYNC_SEND_TEMPLATE = "kafkaTemplate.async.send.template";

    @Autowired
    private KafkaMirrorConfiguration kafkaMirrorConfiguration;

    @Autowired
    private KafkaDynamicSender kafkaDynamicSender;

//    @Autowired
//    private KafkaTemplate kafkaMirrorTemplate;

    @EventAnnotation(value = KAFKA_SEND_EVENT)
    public void sendMessage(KafkaSyncSender sender, String data){
        KafkaSyncSender mirrorKafkaSyncSender = kafkaMirrorConfiguration.getSendMirrorMap().get(sender);
        kafkaDynamicSender.sendMessage(sender, mirrorKafkaSyncSender, data);
    }

    @EventAnnotation(value = KAFKA_TEMPLATE_ASYNC_SEND_TEMPLATE)
    public void sendTemplateMessage(KafkaTemplate sender, String topic, String partitionKey, String data){
        kafkaDynamicSender.sendMessageUsePartition(sender,sender,topic, null, partitionKey, null, data);
    }
}
