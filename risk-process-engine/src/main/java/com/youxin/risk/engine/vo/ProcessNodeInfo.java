package com.youxin.risk.engine.vo;

import com.youxin.risk.commons.utils.DateUtil;
import com.youxin.risk.ra.utils.DateUtils;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 节点信息
 */
@Setter
@Getter
public class ProcessNodeInfo {
    // 同loanKey
    private String tid;
    private String eventCode;
    private String processDefId;
    private String nodeId;
    private long cost;
    private Object input;
    private Object output;
    private Object codeId;
    private String createTime;
    private String currentRunNodeId;

    public ProcessNodeInfo() {
        this.createTime = DateUtils.getCurrentTime();
    }
}
