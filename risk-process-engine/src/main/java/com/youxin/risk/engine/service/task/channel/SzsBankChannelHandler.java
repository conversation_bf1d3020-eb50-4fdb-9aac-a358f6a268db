/*
 * Copyright (C) 2020 Baidu, Inc. All Rights Reserved.
 */
package com.youxin.risk.engine.service.task.channel;

import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.google.common.collect.Maps;
import com.youxin.risk.engine.activiti.context.ProcessContext;
import com.youxin.risk.commons.model.Event;

@Service
public class SzsBankChannelHandler extends BaseChannelHandler{

    public static final Logger LOGGER = LoggerFactory.getLogger(SzsBankChannelHandler.class);

    @Override
    public String getFundChannel() {
        return "SZS_BANK";
    }

    @Override
    public Map<String, Object> buildRequestParams(ProcessContext processContext, Event event) {
        return Maps.newHashMap();
    }

    @Override
    public String getResultData(ProcessContext processContext,Event event) {
        return "{}";
    }

    @Override
    public Boolean buildLocalData(ProcessContext processContext,Event event) {
        return true;
    }
}