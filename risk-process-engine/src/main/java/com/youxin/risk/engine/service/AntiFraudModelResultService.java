/*
 * Copyright (C) 2019 Baidu, Inc. All Rights Reserved.
 */
package com.youxin.risk.engine.service;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.youxin.risk.commons.mongo.MongoDao;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.ra.vo.AntiFraudModelResult;
import com.youxin.risk.ra.vo.RiskModelResultVo;

@Service
public class AntiFraudModelResultService {

	protected Logger LOGGER = LoggerFactory.getLogger(getClass());

	@Resource
	private MongoDao mongoDao;

	public void saveModelResult(AntiFraudModelResult antiFraudModelResult) {
		// 留存mongodb模型结果记录, 异常重试
		int retryTime = 1, maxRetryTime = 3;
		while (retryTime <= maxRetryTime) {
			try {
				RiskModelResultVo riskModelResultVo =
						JSON.parseObject(JSON.toJSONString(antiFraudModelResult), RiskModelResultVo.class);
				boolean flag = mongoDao.insert("ModelResult", riskModelResultVo);
				if (!flag) {
					throw new Exception("saveMongoFailed");
				}
				return;
			} catch (Exception e) {
				if(retryTime >= maxRetryTime) {
					LoggerProxy.error("saveModelResultError", LOGGER, "params="+JSON.toJSONString(antiFraudModelResult),e);
				}
				retryTime ++;
			}
		}
	}


}
