package com.youxin.risk.engine.service.task;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.engine.activiti.context.ProcessContext;
import com.youxin.risk.engine.activiti.runtime.task.AbstractTaskService;
import com.youxin.risk.commons.constants.VerifySubmitUserinfoTypeEnum;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.service.engine.EventService;
import com.youxin.risk.engine.clients.VerifyClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;

/**
 * 回写用户基础信息到ra(先推到verify)
 */
@Service
public class RaPushUserInfoTaskService extends AbstractTaskService {

    @Resource
    private VerifyClient verifyClient;

    @Resource
    private EventService eventService;

    private static HashMap<VerifySubmitUserinfoTypeEnum, String> tableAttrs = new HashMap<>();

    static {
        tableAttrs.put(VerifySubmitUserinfoTypeEnum.register, "mobile,tongdunFingerprint,imei,idfa");
//        tableAttrs.put(VerifySubmitUserinfoTypeEnum.reportRequest, "loanId,loanKey,loanDuration,principalAmount,limitAmount,longitude,latitude,wifiSSID,wifiLevel," +
//                "wifiMac,batteryLevel,batteryPlugType,deviceName,lowBatteryMode,rong360NeedAudit,periodNo,step,userStatusInfo,verifySource,loanCount,currentBalance," +
//                "isMaxOverdueGt10d,hasNotSettled,firstLoanTime,zaCustType,isSumOverdueGt3t,isOverdueGt1d,authVersion,datakeyJobId");
        tableAttrs.put(VerifySubmitUserinfoTypeEnum.idcard, "idcardNumber,idcardName,pidPositiveUrl,pidNegativeUrl,faceUrl,curveFaceUrl,faceScore,pidAuth,faceThreshold,faceGenuineness,idcardAddress,idcardNation," +
                "idcardIssue,idcardValid,idcardLegalityFront,idcardLegalityBack,faceChannel,positiveChannel,negativeChannel,liveRate");
        tableAttrs.put(VerifySubmitUserinfoTypeEnum.job, "industry,companyProvince,companyCity,companyDistrict,companyPosition,salary,payDay,companyName,companyAddress,companyPhone");
        tableAttrs.put(VerifySubmitUserinfoTypeEnum.address, "province,city,district,liveAddress,liveDuration,marriage,childNum");
        tableAttrs.put(VerifySubmitUserinfoTypeEnum.bankCard, "bankcardNo,bankName,bankAddress,reservedMobile,validation");

    }

    private static final Logger LOGGER = LoggerFactory.getLogger(RaPushUserInfoTaskService.class);

    @Override
    public void execute(ProcessContext processContext) {
        Event event = processContext.getEvent();
        String table = processContext.getAttribute("tables");
        String[] tables = table.split(",");
        // 调用rrd接口保存
        for (String item : tables) {
            if (!VerifySubmitUserinfoTypeEnum.reportRequest.name().equals(item)) {
                VerifySubmitUserinfoTypeEnum raSubmitUserinfoTypeEnum = VerifySubmitUserinfoTypeEnum.valueOf(item);
                verifyClient.submitDataToRa(raSubmitUserinfoTypeEnum, getPostDataByType(raSubmitUserinfoTypeEnum, event, processContext));
            }
        }
        if (event.get("plist") != null) {
            verifyClient.submitDataToRa(VerifySubmitUserinfoTypeEnum.plist, buildPlistData(event));
        }
        verifyClient.submitDataToRa(VerifySubmitUserinfoTypeEnum.contactList, buildContactListData(event));
        verifyClient.submitDataToRa(VerifySubmitUserinfoTypeEnum.loansInfo, buildLoansInfoData(event));
        //add taojinyun to datavo
        if (event.get("taojinyun") != null) {
            event.getDataVo().put("taojinyun", event.get("taojinyun"));
//            this.eventService.updateEventToMongo(event);
        }
    }

    /**
     * 根据配置文件中的字段配置拼接要回写到ra的提交信息
     *
     * @param raSubmitUserinfoTypeEnum
     * @return
     */
    private JSONObject getPostDataByType(VerifySubmitUserinfoTypeEnum raSubmitUserinfoTypeEnum, Event event, ProcessContext processContext) {
        JSONObject postData = new JSONObject();
//        String[] commonFields = processContext.getAttribute("commonFields").split(",");
//        for(String item : commonFields){
//            postData.put(item, event.getParams().get(item));
//        }
        addCommonData(postData, event);
        buildJSONObject(raSubmitUserinfoTypeEnum, event, postData);
        return postData;
    }

    public static JSONObject buildJSONObject(VerifySubmitUserinfoTypeEnum raSubmitUserinfoTypeEnum, Event event, JSONObject exsitJsonData) {
        String[] fields = tableAttrs.get(raSubmitUserinfoTypeEnum).split(",");
        for (String field : fields) {
            exsitJsonData.put(field, event.getParams().get(field));
            if (VerifySubmitUserinfoTypeEnum.bankCard.equals(raSubmitUserinfoTypeEnum) && "validation".equals(field)) {
            	exsitJsonData.put(field, true);
			}
        }
        return exsitJsonData;
    }

    private void addCommonData(JSONObject data, Event event) {
        data.put("appVersion", event.getString("appVersion"));
        data.put("channelCode", event.getString("channelCode"));
        data.put("mobileModel", event.getString("mobileModel"));
        data.put("deviceId", event.getString("deviceId"));
        data.put("platform", event.getString("platform"));
        data.put("osVersion", event.getString("osVersion"));
        data.put("sourceSystem", event.getString("sourceSystem"));
        data.put("userKey", event.getString("userKey"));
        data.put("ip", event.getString("ip"));
        data.put("applyId", event.getString("applyId"));
        data.put("createTime", event.getString("createTime"));
        data.put("channel", event.getString("channel"));
        data.put("jailBroken", event.getString("jailBroken"));
        data.put("device", event.getString("device"));
        data.put("isCopyPackage", event.getString("isCopyPackage"));
        data.put("jobId", event.getString("jobId"));
    }

    private JSONObject buildPlistData(Event event) {
        JSONObject postData = new JSONObject();
        addCommonData(postData, event);
        postData.put("plist", event.get("plist"));
        return postData;
    }

    private JSONObject buildContactListData(Event event) {
        JSONObject postData = new JSONObject();
        addCommonData(postData, event);
        postData.put("contactList", event.get("contactList"));
        return postData;
    }

    private JSONObject buildLoansInfoData(Event event) {
        JSONObject postData = new JSONObject();
        addCommonData(postData, event);
        postData.put("loanUsage", event.getString("loanUsage"));
        postData.put("otherLoan", event.get("otherLoan"));
        return postData;
    }
}