package com.youxin.risk.engine.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/8/20 10:27
 */
@Getter
@AllArgsConstructor
public enum RateTypeEnum {

    FIX("fix", "固定费率"),
    TEMP("temp", "临时费率");

    private final String code;
    private final String name;

    public static Optional<RateTypeEnum> getByCode(String code){
        for(RateTypeEnum rateTypeEnum : values()){
            if(rateTypeEnum.getCode().equals(code)){
                return Optional.of(rateTypeEnum);
            }
        }
        return Optional.empty();
    }
}
