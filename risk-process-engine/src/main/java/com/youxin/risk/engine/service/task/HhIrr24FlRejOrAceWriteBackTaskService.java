package com.youxin.risk.engine.service.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.constants.EventVariableKeyEnum;
import com.youxin.risk.commons.constants.RedisKeyEnum;
import com.youxin.risk.commons.dao.verify.VerifyResultHitMapper;
import com.youxin.risk.commons.dao.verify.VerifyResultIrr24FlMapper;
import com.youxin.risk.commons.event.impl.EventService;
import com.youxin.risk.commons.kafkav2.sender.KafkaSyncSender;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.model.verify.VerifyResult;
import com.youxin.risk.commons.model.verify.VerifyResultHit;
import com.youxin.risk.commons.tools.redis.RetryableJedis;
import com.youxin.risk.commons.utils.JsonKeyFormatUtil;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.StringUtils;
import com.youxin.risk.engine.activiti.context.ProcessContext;
import com.youxin.risk.engine.activiti.runtime.task.AbstractTaskService;
import com.youxin.risk.engine.utils.EventUtils;
import com.youxin.risk.ra.utils.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * 好还24分流结果写表定制节点
 */
public class HhIrr24FlRejOrAceWriteBackTaskService extends AbstractTaskService {
    private static final Logger LOGGER = LoggerFactory.getLogger(HhIrr24FlRejOrAceWriteBackTaskService.class);

    private static final String RONG_VERIFY = "rong360Verify";
    private static final String ZHAOLIAN_VERIFY_EVENT_CODE = "zhaolianVerify";
    private static final String RONG_360 = "RONG_360";
    private static final Double DEFAULT_REDUCE_AMOUNT = 500.0;
    private static final Integer DEFAULT_NEW_AMOUNT_VALID_DAYS = 30;

    @Autowired
    private EventService eventService;
    @Autowired
    private VerifyResultIrr24FlMapper verifyResultIrr24FlMapper;
    @Autowired
    private VerifyResultHitMapper verifyResultHitMapper;
    @Autowired
    private KafkaSyncSender verifyResultMessageSender;
    @Resource
    private RetryableJedis retryableJedis;


    @Override
    public void execute(ProcessContext processContext) {
        Event event = processContext.getEvent();
        Map<String, Object> verifyResult = event.getVerifyResult();
        String reasonCode = null;
        try {
            if (verifyResult != null && verifyResult.get(EventVariableKeyEnum.originResult.name()) != null) {
                JSONObject originResult = JSON.parseObject((String) verifyResult.get(EventVariableKeyEnum.originResult.name()));
                reasonCode = Objects.isNull(originResult) ? "" : originResult.get("reason_code").toString();
            }
        } catch (Exception e) {
            LoggerProxy.error("sendEventMessageError", LOGGER, "", e);
        }

        if (StringUtils.isNotBlank(reasonCode)) {
            pointIrr24FlagData(event.getUserKey(), event.getLoanKey(),reasonCode);
        }

        Map<String, Object> params = event.getParams();
        insertVerifyResult(params, JSON.parseObject(JSON.toJSONString(verifyResult)), reasonCode);
    }


    public void insertVerifyResult(Map<String, Object> params, JSONObject resultJson, String reasonCode) {
        VerifyResult verifyResult = new VerifyResult();
        verifyResult.setLoanId(Integer.valueOf(params.get("loanId").toString()));
        verifyResult.setLoanKey((String) params.get("loanKey"));
        verifyResult.setUserKey((String) params.get("userKey"));
        String sourceSystem = (String) params.get("sourceSystem");
        String eventCode = (String) params.get("eventCode");
        if (StringUtils.isNotBlank(eventCode) && RONG_VERIFY.equals(eventCode)) {
            sourceSystem = RONG_360;
        } else if (ZHAOLIAN_VERIFY_EVENT_CODE.equals(eventCode)) {
            sourceSystem = "ZHAOLIAN";
        }
        verifyResult.setSourceSystem(sourceSystem);

        verifyResult.setScore(Double.valueOf(resultJson.getString("score")));
        verifyResult.setScore2(Double.valueOf(resultJson.getString("score2")));
        verifyResult.setIsAutoPassed(resultJson.getBoolean("isPassed"));
        verifyResult.setAutoVerifyTime(new Date());
        verifyResult.setReasonCode(reasonCode);
        verifyResult.setIsManual(resultJson.getBoolean("isManual"));
        verifyResult.setStrategyId(resultJson.getInteger("strategyId"));
        verifyResult.setLevel(resultJson.getString("level"));
        verifyResult.setSegment(resultJson.getString("segment"));
        verifyResult.setStep((String) params.get("step"));
        verifyResult.setLoanAmount(Double.valueOf(resultJson.getString("loanAmount")));
        verifyResult.setLoanPeriodNos(resultJson.getInteger("loanPeriodNos"));
        verifyResult.setLoanRate(Double.valueOf(resultJson.getString("loanRate")));
        verifyResult.setPeriodAmount(resultJson.getString("periodAmount"));
        verifyResult.setBtAmount(Double.valueOf(resultJson.getString("btAmount")));
        verifyResult.setBtPeriodNos(resultJson.getInteger("btPeriodNos"));
        verifyResult.setBtRate(Double.valueOf(resultJson.getString("btRate")));
        verifyResult.setTotalAmount(Double.valueOf(resultJson.getString("totalAmount")));
        if (verifyResult.getIsManual() == null) {
            verifyResult.setIsManual(false);
        }

        Boolean isReduceAmountPass = resultJson.getBoolean("isReduceAmountPass");
        Double newAmount = null;
        Date newAmountExpiry = null;
        if (Boolean.TRUE.equals(isReduceAmountPass)) {
            newAmount = Double.valueOf(resultJson.getString("newAmount"));
            if (newAmount == null) {
                newAmount = DEFAULT_REDUCE_AMOUNT;
            }
            Integer validDays = resultJson.getInteger("validDays");
            if (validDays == null) {
                validDays = DEFAULT_NEW_AMOUNT_VALID_DAYS;
            }
            newAmountExpiry = DateUtils.getNextDayDate(new Date(), validDays);
        }

        Integer lockDays = resultJson.getInteger("lockDays");
        // 审核结果设置降额后额度和有效期
        verifyResult.setIsReduceAmountPass(isReduceAmountPass);
        verifyResult.setNewAmount(newAmount);
        verifyResult.setNewAmountExpiry(newAmountExpiry);
        Date lockDate = DateUtils.getNextDayDate(new Date(), lockDays);
        verifyResult.setAutoLockDays(lockDate);

        if (!verifyResult.getIsAutoPassed() || !verifyResult.getIsManual()) {
            LOGGER.info("irr process :userKey={},loanKey={} not need manual verify", verifyResult.getUserKey(), verifyResult.getLoanKey());
            verifyResult.setFinalVerifyTime(new Date());
            verifyResult.setIsFinalPassed(verifyResult.getIsAutoPassed());
        }
        verifyResult.setUpdateTime(new Date());
        verifyResult.setCreateTime(new Date());
        verifyResult.setVersion(0);
        this.verifyResultIrr24FlMapper.insertVerifyResultIrr(verifyResult);
        this.insertVerifyResultHitFL24Monitor(verifyResult);

        try{
            Map<String, Object> verifyResultNew = JSON.parseObject(JSON.toJSONString(verifyResult));
            // 发送36进件审核结果给数仓进行消费落表
            verifyResultNew.put("loanType", "24");
            // 数据用于区分dt的
            verifyResultNew.put("timestamp", System.currentTimeMillis());
            // 审核成功，expireTime通过策略字段line_valid_days确定
            Integer lineValidDays = resultJson.getInteger("lineValidDays");
            verifyResultNew.put("lineValidDays", lineValidDays);
            eventService.fireAsynchronous(EventUtils.KAFKA_SEND_EVENT, verifyResultMessageSender, JSONObject.toJSONString(verifyResultNew));
            LoggerProxy.info("verifyResultKafkaSender", LOGGER, "Sent 24 verify result to Kafka successfully");
        }catch (Exception e) {
            LoggerProxy.error("verifyResultKafkaSender", LOGGER, "Sent 24 verify result to Kafka failed", e);
        }
    }

    private void insertVerifyResultHitFL24Monitor(VerifyResult verifyResult) {
        try {
            VerifyResultHit verifyResultHit = new VerifyResultHit();
            BeanUtils.copyProperties(verifyResult, verifyResultHit);
            String reasonCode = verifyResult.getReasonCode();
            if (StringUtils.isNotEmpty(reasonCode)) {
                JSONObject jsonObject = JSON.parseObject(reasonCode);
                if (jsonObject.containsKey("new_tag")) {
                    verifyResultHit.setNewTag(jsonObject.getString("new_tag"));
                }
            }
            verifyResultHitMapper.insertVerifyResultHitFL24(verifyResultHit);
        } catch (BeansException e) {
            LOGGER.error("insertVerifyResultHitFLError , userKey={} ,loanKey={}", verifyResult.getUserKey(), verifyResult.getLoanKey(), e);
        }
    }


    /**
     * 【贷前策略组-实时变量开发需求（2个）】
     * https://www.tapd.cn/tapd_fe/48583176/story/detail/1148583176001033163
     * <AUTHOR>
     * @date 2025/4/9 19:19
     */
    public void pointIrr24FlagData(String userKey, String loanKey, String reasonCode) {
        try {
            JSONObject userVerifyInfo = JSON.parseObject(reasonCode).getJSONObject("user_verify_info");
            if (userVerifyInfo == null) {
                return;
            }
            // 值不多，方便后续扩展
            Integer irr24Flag = userVerifyInfo.getInteger("irr_24_flag");
            if (irr24Flag == null) {
                return;
            }

            String redisKey = RedisKeyEnum.irr24_flag_prefix.value + irr24Flag;
            // 防止局部成功导致key不过期
            Long ttl = retryableJedis.ttl(redisKey);
            retryableJedis.sAdd(redisKey,loanKey);
            if (ttl == null || ttl < 0) {
                //获取当前时间到月末的秒数
                int time = DateUtils.getSecondsUntilEndOfMonth();
                retryableJedis.expire(redisKey,time);
                LOGGER.info("pointIrr24FlagData , userKey={}, loanKey={}, redisKey={}, time={}",userKey, loanKey, redisKey, time);
                return;
            }
            LOGGER.info("pointIrr24FlagData, redisKey={}, userKey={}, loanKey={}, ttl={}", redisKey, userKey, loanKey, ttl);
        } catch (Exception e) {
            LOGGER.error("pointIrr24FlagData error,userKey={}, loanKey={}, reasonCode={}",userKey, loanKey, reasonCode, e);
        }
    }


}
