package com.youxin.risk.engine.scheduler;

import com.youxin.risk.commons.constants.EngineAsyncRequestLogStatusEnum;
import com.youxin.risk.commons.delayqueue.DelayMessageHandler;
import com.youxin.risk.commons.model.EngineAsyncRequestLog;
import com.youxin.risk.commons.service.engine.EngineAsyncRequestLogService;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.engine.service.ProcessWakeupService;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;

@Component
public class DataTimeoutDelayMessageHandler implements DelayMessageHandler {

	public static String ASYNC_REQUESTID_KEY="ASYNC_REQUESTID";

	private final static Logger logger = LoggerFactory.getLogger(DataTimeoutDelayMessageHandler.class);

	@Resource
	protected EngineAsyncRequestLogService engineAsyncRequestLogService;

	@Resource
	private ProcessWakeupService processWakeupService;



	@Override
	public Boolean handle(Map<String, Object> params) {
		String id = MapUtils.getString(params,ASYNC_REQUESTID_KEY);
		if(StringUtils.isBlank(id)){
			return true;
		}
		try {
			EngineAsyncRequestLog requestLog = engineAsyncRequestLogService.getByAsyncRequestId(id);
			if(requestLog == null ){
				LoggerProxy.info("accept",logger,"task is end,record[{}] is not found",id);
				return true;
			}
			//如果已经是终态，不处理，用于解决更新服务时 ，队列存量数据发送端和接收端的gap
			if (EngineAsyncRequestLogStatusEnum.isFinalStatus(requestLog.getStatus())) {
				return true;
			}
			String oldStatus = requestLog.getStatus();
			//如果是FETCHED_OLD 更新为FETCHED
			if (EngineAsyncRequestLogStatusEnum.FETCHED_OLD.name().equals(requestLog.getStatus())) {
				requestLog.setStatus(EngineAsyncRequestLogStatusEnum.FETCHED.name());
			} else {
				// INIT,RETRY 状态
				requestLog.setStatus(EngineAsyncRequestLogStatusEnum.FAILED.name());
			}

			requestLog.setUpdateTime(new Date());
			engineAsyncRequestLogService.updateStatus(requestLog, EngineAsyncRequestLogStatusEnum.valueOf(oldStatus));
			LoggerProxy.info("accept",logger,"reset delay asyncRequestId:{}",id);
			processWakeupService.tryWakeup(requestLog);
		}catch (Exception ex){
			LoggerProxy.info("handle",logger,"wakeup  asyncRequestId:{} error",id);
		}
		return true;
	}
}
