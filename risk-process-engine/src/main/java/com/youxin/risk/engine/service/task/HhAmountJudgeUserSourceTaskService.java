package com.youxin.risk.engine.service.task;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.NacosClientAdapter;
import com.youxin.risk.commons.utils.StringUtils;
import com.youxin.risk.engine.activiti.context.ProcessContext;
import com.youxin.risk.engine.activiti.runtime.task.AbstractTaskService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024-06-03 16:15:13
 * 判断用户来源是否是api用户
 */
public class HhAmountJudgeUserSourceTaskService extends AbstractTaskService {

    private static final Logger LOGGER = LoggerFactory.getLogger(HhAmountJudgeUserSourceTaskService.class);

    @Override
    public void execute(ProcessContext processContext) {
        Event event = processContext.getEvent();
        event.getDataVo().put("userSourceFromApi", false);
        Boolean changeNewJudgeCondition = NacosClientAdapter.getBooleanConfig("changeNewJudgeCondition", false);
        try {
            if(changeNewJudgeCondition){
                JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(event.getDataVo())).getJSONObject("thirdPartyData")
                        .getJSONObject("lastPassResultByUserKeyVarGroup");
                if(Objects.nonNull(jsonObject) && "1".equals(jsonObject.getString("or_is_last_verify_sucess_by_api"))){
                    event.getDataVo().put("userSourceFromApi", true);
                }
            }else {
                JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(event.getDataVo())).getJSONObject("thirdPartyData")
                        .getJSONObject("cmsHfqOriginNameResVarGroup");
                if(Objects.nonNull(jsonObject)){
                    String userSourceFirstName = jsonObject.getString("user_source_first_name");
                    if(StringUtils.equals(userSourceFirstName,"ASSET_API")){
                        event.getDataVo().put("userSourceFromApi", true);
                    }
                }
            }
        }catch (Exception e){
            LoggerProxy.error(LOGGER,"HhAmountJudgeUserSourceTaskService,error:",e);
        }
    }

}
