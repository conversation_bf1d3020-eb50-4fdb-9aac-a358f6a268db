package com.youxin.risk.engine.activiti.service.impl;

import com.youxin.risk.engine.activiti.constants.NodeTypeEnum;
import com.youxin.risk.engine.activiti.context.ProcessContext;
import com.youxin.risk.engine.activiti.service.RouteService;

public class DefaultRouteService implements RouteService {

    @Override
    public void execute(ProcessContext processContext) {
        processContext.setCurrentNodeType(NodeTypeEnum.route);
    }
}
