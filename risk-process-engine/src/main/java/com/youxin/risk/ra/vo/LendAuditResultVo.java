package com.youxin.risk.ra.vo;

import com.youxin.risk.ra.utils.XmlEntity;
import com.youxin.risk.ra.utils.XmlNode;

/**
 * 放款审核数据
 *
 * <AUTHOR>
 * @version 创建时间：2018年8月30日 下午7:33:41
 */
@XmlEntity(name="lend_audit")
public class LendAuditResultVo {

    @XmlNode(name="user_key")
    private String userKey;

    @XmlNode(name="mobile")
    private String mobile;


    @XmlNode(name="fund_channel")
    private String fundChannel;

    @XmlNode(name="data")
    private String data;

    @XmlNode(name="third_party_result")
    private String thirdPartyResult;

    @XmlNode(name="loan_type")
    private String loanType;

    @XmlNode(name="loan_submit_time")
    private String loanSubmitTime;

    public String getLoanSubmitTime() {
        return loanSubmitTime;
    }

    public void setLoanSubmitTime(String loanSubmitTime) {
        this.loanSubmitTime = loanSubmitTime;
    }

    public String getUserKey() {
        return userKey;
    }

    public void setUserKey(String userKey) {
        this.userKey = userKey;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getFundChannel() {
        return fundChannel;
    }

    public void setFundChannel(String fundChannel) {
        this.fundChannel = fundChannel;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getThirdPartyResult() {
        return thirdPartyResult;
    }

    public void setThirdPartyResult(String thirdPartyResult) {
        this.thirdPartyResult = thirdPartyResult;
    }

    public String getLoanType() {
        return loanType;
    }

    public void setLoanType(String loanType) {
        this.loanType = loanType;
    }
}
