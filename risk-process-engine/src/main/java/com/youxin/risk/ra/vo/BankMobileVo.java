package com.youxin.risk.ra.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.youxin.risk.ra.utils.XmlEntity;
import com.youxin.risk.ra.utils.XmlNode;

@XmlEntity()
public class BankMobileVo {

    @JSONField(name = "bank_mobile")
    @XmlNode(name = "bank_mobile")
    private String bankMobile;

    @JSONField(name = "hfq_cnt_pid")
    @XmlNode(name = "hfq_cnt_pid")
    private int hfqCntPid;

    @JSONField(name = "rrd_cnt_pid")
    @XmlNode(name = "rrd_cnt_pid")
    private int rrdCntPid;

    @JSONField(name = "all_cnt_pid")
    @XmlNode(name = "all_cnt_pid")
    private int allCntPid;

    public void setBankMobile(String bankMobile) {
        this.bankMobile = bankMobile;
    }

    public int getHfqCntPid() {
        return hfqCntPid;
    }

    public void setHfqCntPid(int hfqCntPid) {
        this.hfqCntPid = hfqCntPid;
    }

    public int getRrdCntPid() {
        return rrdCntPid;
    }

    public void setRrdCntPid(int rrdCntPid) {
        this.rrdCntPid = rrdCntPid;
    }

    public int getAllCntPid() {
        return allCntPid;
    }

    public void setAllCntPid(int allCntPid) {
        this.allCntPid = allCntPid;
    }

    public String getBankMobile() {

        return bankMobile;
    }
}
