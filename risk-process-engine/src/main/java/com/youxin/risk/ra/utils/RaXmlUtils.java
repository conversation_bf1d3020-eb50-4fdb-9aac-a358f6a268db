package com.youxin.risk.ra.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.youxin.apollo.client.NacosClient;
import com.youxin.risk.commons.constants.ApolloNamespace;
import com.youxin.risk.commons.utils.XmlUtils;
import com.youxin.risk.engine.service.task.feature.FeatureResult;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.lang.reflect.Field;
import java.util.*;

public class RaXmlUtils {

    private static final Logger LOG = LoggerFactory.getLogger(RaXmlUtils.class);

    public static final String SPLIT_STR = "_";

    private static final String PACKAGE_SPLIT = "/";


    public static ElementComparator comparator = new ElementComparator();

    public static class ElementComparator implements Comparator<Element> {
        @Override
        public int compare(Element o1, Element o2) {
            return o1.getName().compareTo(o2.getName());
        }
    }


    @SuppressWarnings("unchecked")
    public static String toXmlString(Object obj)
            throws IllegalArgumentException, IllegalAccessException {
        if (obj == null) {
            return FeatureResult.INVALID.toString();
        }
        StringBuilder sb = new StringBuilder();
        Class<?> c = obj.getClass();
        if (obj instanceof Map) {
            Set<?> set = ((Map<?, ?>) obj).keySet();
            for (Object name : set) {
                String key = (String) name;
                sb.append(String.format("<%s>", key));
                sb.append(RaXmlUtils.toXmlString(((Map<?, ?>) obj).get(key)));
                sb.append(String.format("</%s>", key));
            }
        } else if (obj instanceof List) {
            for (Object o : (List<Object>) obj) {
                sb.append(RaXmlUtils.toXmlString(o));
            }
        } else if (c.isAnnotationPresent(XmlEntity.class)) {
            XmlEntity[] XmlEntitys = c.getAnnotationsByType(XmlEntity.class);
            String className = null;
            if (XmlEntitys != null && XmlEntitys.length > 0) {
                for (XmlEntity xmlEntity : XmlEntitys) {
                    if (!StringUtils.isEmpty(xmlEntity.name())) {
                        className = xmlEntity.name();
                        break;
                    }
                }
            }
            if (className != null) {
                sb.append(String.format("<%s>", className));
            }

            for (Field field : c.getDeclaredFields()) {
                XmlNode[] annotations = field
                        .getDeclaredAnnotationsByType(XmlNode.class);
                if (annotations != null && annotations.length > 0) {
                    String nodeName = null;
                    for (XmlNode xmlNode : annotations) {
                        if (!StringUtils.isEmpty(xmlNode.name())) {
                            nodeName = xmlNode.name();
                            break;
                        }
                    }

                    field.setAccessible(true);

                    if (nodeName != null && field.get(obj) != null) {
                        if (field.getType() == List.class) {
                            sb.append(String.format("<%s>", nodeName));
                            List<Object> list = (List<Object>) field.get(obj);
                            for (Object o : list) {
                                sb.append(RaXmlUtils.toXmlString(o));
                            }
                            sb.append(String.format("</%s>", nodeName));
                        } else {
                            sb.append(String.format("<%s>%s</%s>", nodeName,
                                    RaXmlUtils.toXmlString(field.get(obj)), nodeName));
                        }
                    }
                }
            }
            if (className != null) {
                sb.append(String.format("</%s>", className));
            }
        } else if (obj instanceof Boolean) {
            if (Boolean.TRUE.equals(obj)) {
                sb.append(FeatureResult.TRUE.toString());
            } else {
                sb.append(FeatureResult.FALSE.toString());
            }
        } else {
            sb.append(RaXmlUtils.replaceXmlIllegal(obj.toString()));
        }
        return sb.toString();
    }

    /**
     * 把下划线命名法转成驼峰命名法 把call_distribution_list转成callDistributionList
     */
    public static String underlineToCamel(String nodeName) {
        if (StringUtils.isEmpty(nodeName)) {
            return null;
        }
        String[] strs = nodeName.split(SPLIT_STR);

        StringBuilder sb = new StringBuilder();
        sb.append(strs[0]);
        if (strs.length > 1) {
            for (int i = 1; i < strs.length; i++) {
                sb.append(RaXmlUtils.firstCharUpperCase(strs[i]));
            }
        }
        return sb.toString();
    }

    public static String firstCharUpperCase(String s) {
        StringBuilder sb = new StringBuilder();
        sb.append(Character.toUpperCase(s.charAt(0)));
        sb.append(s.substring(1));
        return sb.toString();
    }

    public static String firstCharLowerCase(String s) {
        StringBuilder sb = new StringBuilder();
        sb.append(Character.toLowerCase(s.charAt(0)));
        sb.append(s.substring(1));
        return sb.toString();
    }

    public static String getGetterName(String fieldName) {
        return "get" + RaXmlUtils.firstCharUpperCase(fieldName);
    }

    public static String getSetterName(String fieldName) {
        return "set" + RaXmlUtils.firstCharUpperCase(fieldName);
    }

    public static String replaceXmlIllegal(String xml) {
        if (StringUtils.isBlank(xml)) {
            return xml;
        }

        return xml.replaceAll("&", "&amp;").replaceAll("<", "&lt;")
                .replaceAll(">", "&gt;");
    }

    private static void merge(Element mElement, Element vElement, String vElementPath, Element rootelement) {
        Element ele = mElement.element(vElement.getName());
        if (ele == null) {
            vElement.detach();
            mElement.add(vElement);
        } else if (vElement.isTextOnly()) {
            vElement.detach();
            mElement.remove(ele);
//            removeEleToBackupPath(ele, vElementPath, rootelement);
            mElement.add(vElement);
        } else {
            List<Element> list = vElement.elements();
            for (Element subElement : list) {
                merge(ele, subElement, vElementPath + "/" + vElement.getName(), rootelement);
            }
        }
    }


    /**
     * 把 newElement中的子元素 merge到 oldElement中
     *
     * @param oldElement
     * @param newElement
     */
    private static void mergeXml(Element oldElement, Element newElement) {
        XmlUtils.mergeXml(oldElement, newElement);
    }

    /**
     * 获得 xml 子元素名称对应 的map
     * key是 element的 name
     * value 是 单一element 或 同名element构成的list
     *
     * @param root
     * @return
     */
    private static Map<String, Object> getElementMap(Element root) {

        Map<String, Object> elementMap = new HashMap<>();
        List<Element> childElements = root.elements();
        String preElementName = null;
        Element preElement;
        List<Element> preElementList;
        for (Element element : childElements) {

            // 如果之前已经有了同名子元素，说明该子元素是一个 list
            if (elementMap.containsKey(element.getName())) {
                // 如果 map中的 object 是 list 说明已经放入过了，直接把当前元素加入 list即可
                if (elementMap.get(preElementName) instanceof List) {
                    preElementList = (List<Element>) elementMap.get(preElementName);
                    preElementList.add(element);
                }
                // 否则这个元素是这个名字下的第二个元素，把该子元素转换成 list
                else {
                    preElement = (Element) elementMap.get(preElementName);
                    preElementList = new ArrayList<>();
                    preElementList.add(preElement);
                    preElementList.add(element);
                    elementMap.put(preElementName, preElementList);
                }
            }
            // 否则 直接加入
            else {
                elementMap.put(element.getName(), element);
            }
            preElementName = element.getName();
        }
        return elementMap;
    }

    /**
     * @param element 原来的Element
     * @param entity  entity中的value 用来替换element中原来的子元素
     */
    private static void replaceByNewElement(Element element, Map.Entry<String, Object> entity) {

        // 如果是 list 调动添加 list的方法
        if (entity.getValue() instanceof List) {
            addNewElementList(element, entity);
        }
        // 添加单一 element
        else {
            addNewElement(element, entity);
        }
    }

    /**
     * @param element 原来的Element
     * @param entity  包含新添加的 elementList
     */
    private static void addNewElementList(Element element, Map.Entry<String, Object> entity) {

        List<Element> childElementList = (List<Element>) entity.getValue();
        for (Element child : childElementList) {
            child.detach();
            element.add(child);
        }
    }

    /**
     * @param element 原来的Element
     * @param entity  包含新添加的 element
     */
    private static void addNewElement(Element element, Map.Entry<String, Object> entity) {

        Element addElement = (Element) entity.getValue();
        addElement.detach();
        element.add(addElement);
    }


    //将mainxml中被覆盖的节点备份到backup对应节点下
    private static void removeEleToBackupPath(Element mElement, String vElementPath, Element rootelement) {
        //实时特征备份路径/backup/processMerge/。
        try {
            String[] nodes = ("/backup/processMerge/" + vElementPath).split("/");
            Element parent = rootelement;
            for (int i = 1; i < nodes.length; i++) {
                if (StringUtils.isNotEmpty(nodes[i])) {
                    Element element = parent.element(nodes[i]);
                    if (element == null) {
                        element = parent.addElement(nodes[i]);
                    }
                    parent = element;
                }
            }
            parent.add(mElement);
        } catch (Exception e) {
            LOG.error("mergeXmlRemoveEleToBackupPathError,melement=" + mElement.getName() + "vElementPath=" + vElementPath, e);
        }
    }


    /**
     * 将viceXml合并到mainXml, usePreEleWhileInvalid=true 如果main当前txt节点值是Invalid则取viceXml的值
     *
     * @param mainXml
     * @param viceXml
     * @param usePreEleWhileInvalid
     * @return
     */
    public static String mergeXml(String mainXml, String viceXml, boolean... usePreEleWhileInvalid) {
        if (StringUtils.isBlank(mainXml)) {
            return viceXml;
        }
        if (StringUtils.isBlank(viceXml)) {
            return mainXml;
        }
        try {
            mainXml = stripNonValidXMLCharacters(mainXml);
            viceXml = stripNonValidXMLCharacters(viceXml);
            Document mainDocument = new SAXReader().read(new ByteArrayInputStream(mainXml.getBytes("UTF-8")));
            Element rootelement = mainDocument.getRootElement();
            Document viceDocument = new SAXReader().read(new ByteArrayInputStream(viceXml.getBytes("UTF-8")));
            Element viceRootelement = viceDocument.getRootElement();
            List<Element> elements = viceRootelement.elements();


            if (usePreEleWhileInvalid.length == 0) {
                //获取新的merge方法的标志
                if (getNewMergeMethodFlag()) {
                    mergeXml(rootelement, viceRootelement);
                } else {
                    for (Element ele : elements) {
                        merge(rootelement, ele, "/", rootelement);
                    }
                }
            } else {
                for (Element ele : elements) {
                    mergeJudgeInvalid(rootelement, ele);
                }
            }

            return mainDocument.asXML()
                    .replace("<?xml version=\"1.0\" encoding=\"UTF-8\"?>", "")
                    .replace("\r", "").replace("\n", "");
        } catch (Exception e) {
            LOG.error("mergeXml error,errMsg=", e);
            throw new RuntimeException(e);
        }
    }

    public static String mergeXml2(String mainXml, String viceXml) {

        try {
            mainXml = stripNonValidXMLCharacters(mainXml);
            viceXml = stripNonValidXMLCharacters(viceXml);
            Document mainDocument = new SAXReader().read(new ByteArrayInputStream(mainXml.getBytes("UTF-8")));
            Element rootelement = mainDocument.getRootElement();
            Document viceDocument = new SAXReader().read(new ByteArrayInputStream(viceXml.getBytes("UTF-8")));
            Element viceRootelement = viceDocument.getRootElement();

            mergeXml(rootelement, viceRootelement);

            return mainDocument.asXML()
                    .replace("<?xml version=\"1.0\" encoding=\"UTF-8\"?>", "")
                    .replace("\r", "").replace("\n", "");
        } catch (Exception e) {
            LOG.error("mergeXml error,errMsg=", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取使用新的merge方法的标志
     *
     * @return
     */
    private static boolean getNewMergeMethodFlag() {

        String useNewMethod = "1";

        try {
            useNewMethod = NacosClient.getByNameSpace(ApolloNamespace.commonSpace, "xml.merge.new.method.flag", "1");
        } catch (Exception e) {
            LOG.error("getNewMergeMethodFlagError", e);
        }
        return "1".equals(useNewMethod);
    }


    public static void main(String[] args) {
        String xml1 = "<featurephone_test><standard><d>f</d></standard></featurephone_test>";
        String xml2 = "<featurephone_test><standard><td_service_sd><td_2794855_id_number>7.0</td_2794855_id_number><td_2794855_device_id_hit_rate>0.125</td_2794855_device_id_hit_rate><fraudResult_hitRules><decision>Accept</decision><score>2.0</score><id>2794309</id></fraudResult_hitRules><fraudResult_hitRules><decision>Accept</decision><score>2.0</score><id>2794339</id></fraudResult_hitRules><fraudResult_hitRules><decision>Accept</decision><score>6.0</score><id>2794397</id></fraudResult_hitRules><fraudResult_hitRules><decision>Accept</decision><score>10.0</score><id>2794437</id></fraudResult_hitRules><fraudResult_hitRules><decision>Accept</decision><score>3.0</score><id>2794439</id></fraudResult_hitRules><fraudResult_hitRules><decision>Accept</decision><score>0.0</score><id>2794441</id></fraudResult_hitRules><fraudResult_hitRules><decision>Accept</decision><score>0.0</score><id>2794443</id></fraudResult_hitRules><fraudResult_hitRules><decision>Accept</decision><score>0.0</score><id>2794445</id></fraudResult_hitRules><fraudResult_hitRules><decision>Accept</decision><score>0.0</score><id>2794447</id></fraudResult_hitRules><fraudResult_hitRules><decision>Accept</decision><score>0.0</score><id>2794449</id></fraudResult_hitRules><fraudResult_hitRules><decision>Accept</decision><score>6.0</score><id>2794451</id></fraudResult_hitRules><td_2794845_id_number_o2o>-999.0</td_2794845_id_number_o2o><td_2794869_p2p>-999.0</td_2794869_p2p><td_score_prob>0.************</td_score_prob><td_2794849_hlw_rate>-999.0</td_2794849_hlw_rate><td_2794849_hlw>-999.0</td_2794849_hlw><td_rule_result_2794855>8.0</td_rule_result_2794855><td_2794855_wsyh>1.0</td_2794855_wsyh><td_2794855_id_number_hit_rate>0.875</td_2794855_id_number_hit_rate><td_2794855_account_mobile_hit_rate>0.75</td_2794855_account_mobile_hit_rate><td_2794855_xyk>1.0</td_2794855_xyk><td_2794843_ybxf>1.0</td_2794843_ybxf><td_2794845_id_number_o2o_hit_rate>-999.0</td_2794845_id_number_o2o_hit_rate><td_2794855_device_id>1.0</td_2794855_device_id><td_2794851_yhxj>-999.0</td_2794851_yhxj><td_2794845_id_number>3.0</td_2794845_id_number><td_2794869_p2p_rate>-999.0</td_2794869_p2p_rate><td_2794849_yygr>-999.0</td_2794849_yygr><td_2794849_account_mobile>4.0</td_2794849_account_mobile><td_2794869_account_mobile>-999.0</td_2794869_account_mobile><td_2794855_dsf>-999.0</td_2794855_dsf><td_2794841_ybxf>1.0</td_2794841_ybxf><td_2794855_wsyh_rate>0.125</td_2794855_wsyh_rate><td_2794855_account_mobile>6.0</td_2794855_account_mobile><td_2794841_xedk>1.0</td_2794841_xedk><td_rule_result_2794845>3.0</td_rule_result_2794845><td_rule_result_2794869>-999.0</td_rule_result_2794869><td_2794847_xedk>2.0</td_2794847_xedk><td_2794855_dsf_rate>-999.0</td_2794855_dsf_rate><td_2794849_yygr_rate>-999.0</td_2794849_yygr_rate><td_rule_result_2794849>6.0</td_rule_result_2794849></td_service_sd></standard></featurephone_test>";
        String xml3 = "<featurephone_test><standard><td_service_sd><td_2794855_id_number>7.0</td_2794855_id_number><td_2794855_device_id_hit_rate>0.125</td_2794855_device_id_hit_rate><td_2794845_id_number_o2o>-999.0</td_2794845_id_number_o2o><td_2794869_p2p>-999.0</td_2794869_p2p><td_score_prob>0.************</td_score_prob><td_2794849_hlw_rate>-999.0</td_2794849_hlw_rate><td_2794849_hlw>-999.0</td_2794849_hlw><td_rule_result_2794855>8.0</td_rule_result_2794855><td_2794855_wsyh>1.0</td_2794855_wsyh><td_2794855_id_number_hit_rate>0.875</td_2794855_id_number_hit_rate><td_2794855_account_mobile_hit_rate>0.75</td_2794855_account_mobile_hit_rate><td_2794855_xyk>1.0</td_2794855_xyk><td_2794843_ybxf>1.0</td_2794843_ybxf><td_2794845_id_number_o2o_hit_rate>-999.0</td_2794845_id_number_o2o_hit_rate><td_2794855_device_id>1.0</td_2794855_device_id><td_2794851_yhxj>-999.0</td_2794851_yhxj><td_2794845_id_number>3.0</td_2794845_id_number><td_2794869_p2p_rate>-999.0</td_2794869_p2p_rate><td_2794849_yygr>-999.0</td_2794849_yygr><td_2794849_account_mobile>4.0</td_2794849_account_mobile><td_2794869_account_mobile>-999.0</td_2794869_account_mobile><td_2794855_dsf>-999.0</td_2794855_dsf><td_2794841_ybxf>1.0</td_2794841_ybxf><td_2794855_wsyh_rate>0.125</td_2794855_wsyh_rate><td_2794855_account_mobile>6.0</td_2794855_account_mobile><td_2794841_xedk>1.0</td_2794841_xedk><td_rule_result_2794845>3.0</td_rule_result_2794845><td_rule_result_2794869>-999.0</td_rule_result_2794869><td_2794847_xedk>2.0</td_2794847_xedk><td_2794855_dsf_rate>-999.0</td_2794855_dsf_rate><td_2794849_yygr_rate>-999.0</td_2794849_yygr_rate><td_rule_result_2794849>6.0</td_rule_result_2794849></td_service_sd></standard></featurephone_test>";
        String xml4 = "<featurephone_test><standard><td_service_sd>Invoid</td_service_sd></standard></featurephone_test>";

        String xmlPa = read("C:\\Users\\<USER>\\Desktop\\merge-xml\\xml1.xml");
        String xmlA = read("C:\\Users\\<USER>\\Desktop\\merge-xml\\xml2.xml");


//        xmlPa = read("C:\\Users\\<USER>\\Desktop\\merge-xml\\xml1.xml");
//        xmlA = read("C:\\Users\\<USER>\\Desktop\\merge-xml\\xml2.xml");
//    	System.out.println(mergeXml(xml1,xml2));
//    	System.out.println(mergeXml(xml2,xml1));
//    	System.out.println(mergeXml(xml2,xml3));
//    	System.out.println(mergeXml(xml3,xml2));
//    	System.out.println(mergeXml(xml2,xml4));
        System.out.println(mergeXml(xmlPa, xmlA));
        System.out.println(mergeXml2(xmlPa, xmlA));
    }


    /***
     * 读取文件文本信息
     * @param fileName 文件名
     * @return 文件名
     */
    public static String read(String fileName) {
        File file = new File(fileName);
        BufferedReader reader = null;
        StringBuilder sbf = new StringBuilder();
        try {
            reader = new BufferedReader(new FileReader(file));
            String tempStr;
            while ((tempStr = reader.readLine()) != null) {
                sbf.append(tempStr);
            }
            reader.close();
            return sbf.toString();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
        }
        return sbf.toString();
    }

    public static String mergeXml(String xmlReport, Object feature, String nodePath, String nodeName) {
        Document document = null;
        try {
            document = new SAXReader().read(new ByteArrayInputStream(xmlReport.getBytes()));
        } catch (DocumentException e) {
            LOG.error("合并xml失败，", e);
        }
        if (document == null) {
            return xmlReport;
        }
        String[] nodes = nodePath.split(PACKAGE_SPLIT);
        if (nodes == null || nodes.length == 0) {
            LOG.error("feature路径[%s]无效", nodePath);
            return xmlReport;
        }
        Element root = document.getRootElement();
        Element parent = root;
        for (int i = 1; i < nodes.length; i++) {
            Element element = parent.element(nodes[i]);
            if (element == null) {
                element = parent.addElement(nodes[i]);
            }
            parent = element;
        }
        String featureXml = null;
        try {
            featureXml = RaXmlUtils.toXmlString(feature);
            if (!StringUtils.isEmpty(featureXml)) {
                featureXml = String.format("<%s>%s</%s>", nodeName, featureXml, nodeName);
            } else {
                featureXml = String.format("<%s>%s</%s>", nodeName, FeatureResult.INVALID.toString(), nodeName);
            }
        } catch (Exception e) {
            LOG.error("transfer obj to xml error", e);
        }
        if (featureXml != null) {
            try {
                Document featureDocument = new SAXReader().read(new ByteArrayInputStream(featureXml.getBytes()));
                Element featureRoot = featureDocument.getRootElement();
                parent.add(featureRoot);
            } catch (DocumentException e) {
                LOG.error("xml添加新新feature失败，", e);
            }
        }
        String xml = document.asXML();
        xml = xml.replace("<?xml version=\"1.0\" encoding=\"UTF-8\"?>", "");
        xml = xml.replace("\r", "");
        xml = xml.replace("\n", "");
        return xml;
    }

    public static String json2XmlString(JSONObject json, String rootName) {

        if (json == null) {
            return "";
        }
        StringBuffer sb = new StringBuffer();
        sb.append("<").append(rootName).append(">");
        for (Object key : json.keySet()) {
            sb.append("<").append(key).append(">");
            Object value = json.get(key);
            sb.append(iteraorJson(value));
            sb.append("</").append(key).append(">");
        }
        sb.append("</").append(rootName).append(">");
        return sb.toString();
    }

    /**
     * 迭代判断value是否还包含jSONObject
     *
     * @param value
     * @return
     */
    private static Object iteraorJson(Object value) {
        StringBuffer sb = new StringBuffer();
        if (value instanceof JSONObject) {
            JSONObject obj = (JSONObject) value;
            if (!obj.isEmpty()) {
                for (Object key : obj.keySet()) {
                    sb.append("<").append(key).append(">");
                    sb.append(iteraorJson(obj.get(key)));
                    sb.append("</").append(key).append(">");
                }
            }
        } else if (value instanceof JSONArray) {
            JSONArray array = (JSONArray) value;
            if (!array.isEmpty()) {
                for (int i = 0; i < array.size(); i++) {
                    Object obj = array.get(i);
                    if (obj instanceof JSONObject) {
                        sb.append(iteraorJson(obj));
                    } else {
                        sb.append("<item>");
                        sb.append(iteraorJson(obj));
                        sb.append("</item>");
                    }
                }
            }
        } else {
            if (value instanceof String) {
                sb.append(RaXmlUtils.replaceXmlIllegal((String) value));
            } else {
                sb.append(value);
            }

        }

        return sb.toString();

    }

    public static Map xml2map(String xmlStr, boolean needRootKey)
            throws DocumentException {
        Document doc = DocumentHelper.parseText(xmlStr);
        Element root = doc.getRootElement();
        Map<String, Object> map = RaXmlUtils.xml2map(root);
        if (root.elements().size() == 0 && root.attributes().size() == 0) {
            return map;
        }
        if (needRootKey) {
            //在返回的map里加根节点键（如果需要）
            Map<String, Object> rootMap = new HashMap<String, Object>();
            rootMap.put(root.getName(), map);
            return rootMap;
        }
        return map;
    }

    /**
     * xml转map 不带属性
     *
     * @param e
     * @return
     */

    private static Map xml2map(Element e) {
        Map map = new JSONObject();
        List list = e.elements();
        if (list.size() > 0) {
            for (int i = 0; i < list.size(); i++) {
                Element iter = (Element) list.get(i);
                List mapList = new ArrayList();

                if (iter.elements().size() > 0) {
                    Map m = RaXmlUtils.xml2map(iter);
                    if (map.get(iter.getName()) != null) {
                        Object obj = map.get(iter.getName());
                        if (!(obj instanceof List)) {
                            mapList = new ArrayList();
                            mapList.add(obj);
                            mapList.add(m);
                        }
                        if (obj instanceof List) {
                            mapList = (List) obj;
                            mapList.add(m);
                        }
                        map.put(iter.getName(), mapList);
                    } else {
                        map.put(iter.getName(), m);
                    }
                } else {
                    if (map.get(iter.getName()) != null) {
                        Object obj = map.get(iter.getName());
                        if (!(obj instanceof List)) {
                            mapList = new ArrayList();
                            mapList.add(obj);
                            mapList.add(iter.getText());
                        }
                        if (obj instanceof List) {
                            mapList = (List) obj;
                            mapList.add(iter.getText());
                        }
                        map.put(iter.getName(), mapList);
                    } else {
                        map.put(iter.getName(), iter.getText());
                    }
                }
            }
        } else {
            map.put(e.getName(), e.getText());
        }
        return map;
    }

    private static void mergeJudgeInvalid(Element mElement, Element vElement) {
        Element ele = mElement.element(vElement.getName());
        if (ele == null) {
            vElement.detach();
            mElement.add(vElement);
        } else if (ele.isTextOnly() && (StringUtils.isEmpty(ele.getText()) || "Invalid".equals(ele.getText()))) {//如果前面有值后面invalid就用前面的(未判断pre值是否正常)
            vElement.detach();
            ele.detach();
            mElement.add(vElement);
            //LOG.info("emptyInvaildNodePath={},BeleVal={},AeleVal={}", ele.getPath(), ele.getText(), vElement.getText());
        } else if (ele.isTextOnly()) {//后面一步b的节点是text节点并且有值

        } else if (!vElement.isTextOnly()) {//b中是复杂节点并且a中的节点也是复杂的则继续递归
            List<Element> list = vElement.elements();
            for (Element subElement : list) {
                mergeJudgeInvalid(ele, subElement);
            }
        }
    }

    /**
     * @Description:根据w3c规定xml合法字符保留合法字符,链接https://www.w3.org/TR/2004/REC-xml-20040204/#charsets
     * @Param: [in]
     * @return: java.lang.String
     * @Author: LingYin·Fan
     * @Date: 2020/1/2
     */
    public static String stripNonValidXMLCharacters(String in) {

        StringBuffer out = new StringBuffer(); // Used to hold the output.
        char current; // Used to reference the current character.

        if (StringUtils.isEmpty(in)) return "";
        for (int i = 0; i < in.length(); i++) {
            current = in.charAt(i);
            if ((current == 0x9) ||
                    (current == 0xA) ||
                    (current == 0xD) ||
                    ((current >= 0x20) && (current <= 0xD7FF)) ||
                    ((current >= 0xE000) && (current <= 0xFFFD)) ||
                    ((current >= 0x10000) && (current <= 0x10FFFF)))
                out.append(current);
        }
        return out.toString();
    }
}
