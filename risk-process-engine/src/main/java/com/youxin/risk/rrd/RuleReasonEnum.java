package com.youxin.risk.rrd;

import com.google.common.base.Strings;

/**
 *
 * rrd-verify
 * com.heika.verify.policy.enums
 */
public enum RuleReasonEnum {

    R1("R1", "资质类", 3*30),
    R2("R2", "欺诈类", 365),
    R3("R3", "信用类", 6 * 30),
    R4("R4", "取消类", 0),
    R5("R5", "其他类", 30),
    T0("T0", "通过", 0),
    T1("T1", "标记通过", 0),
    B1("B1", "照片类", 0),
    B2("B2", "联系类", 0),
    B3("B3", "填写类", 0),

    ;

    private String param;
    private String desc;
    private Integer expireTime;

    RuleReasonEnum(String param, String desc, Integer expireTime) {
        this.param = param;
        this.desc = desc;
        this.expireTime = expireTime;
    }

    public String getParam() {
        return param;
    }

    public void setParam(String param) {
        this.param = param;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public Integer getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Integer expireTime) {
        this.expireTime = expireTime;
    }

    public static RuleReasonEnum find(String param){
        if(Strings.isNullOrEmpty(param))
            return null;
        for(RuleReasonEnum ruleReasonEnum:values()){
            if(ruleReasonEnum.name().equals(param))
                return ruleReasonEnum;
        }
        return null;
    }

}
