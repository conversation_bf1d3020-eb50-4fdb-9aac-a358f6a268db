package com.youxin.risk.verify.vo;

import com.youxin.risk.commons.utils.JsonUtils;
import org.apache.commons.lang3.builder.EqualsBuilder;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

public class VerifyUserLineManagementBakVo implements Serializable {

    /**
     * @Fields serialVersionUID:
     */
    private static final long serialVersionUID = 245592111526495156L;

    private String userKey;

    private String sourceSystem;
    private String source_system;

    private String lineChangeType;
    private String line_change_type;

    private Double creditLine;
    private Double credit_line;

    private Double availLine;
    private Double avail_line;

    private Double utilLine;
    private Double util_line;

    private Double loanLine;
    private Double loan_line;

    private Double loanAvailLine;
    private Double loan_avail_line;

    private Double loanActualLine;
    private Double loan_actual_line;

    private Double loanUtilLine;
    private Double loan_util_line;

    private Double loanRate;
    private Double loan_rate;

    private Integer loanPeriod;
    private Integer loan_period;

    private Double btLine;
    private Double bt_line;

    private Double btAvailLine;
    private Double bt_avail_line;

    private Double btActualLine;
    private Double bt_actual_line;

    private Double btUtilLine;
    private Double bt_util_line;

    private Double btRate;
    private Double bt_rate;

    private Integer btPeriod;
    private Integer bt_period;

    private Double shopLine;
    private Double shop_line;

    private Double shopAvailLine;
    private Double shop_avail_line;

    private Double shopActualLine;
    private Double shop_actual_line;

    private Double shopUtilLine;
    private Double shop_util_line;

    private Double shopRate;
    private Double shop_rate;

    private Integer shopPeriod;
    private Integer shop_period;

    private String periodLineRate;
    private String period_line_rate;

    private String accountStatus;
    private String account_status;

    private Boolean isClosed;
    private Boolean is_closed;

    private Double userPoint;
    private Double user_point;

    private String userLevel;
    private String user_level;

    private Date lineAssignTime;
    private Date line_assign_time;

    private String reasonCode;
    private String reason_code;

    private String segmentCode;
    private String segment_code;

    private String testCode;
    private String test_code;

    private Boolean status;

    //回调字段
    private String lineType; // 额度管控触发类型
    private String line_type;

    private String transId; // 额度管控触发唯一key
    private String trans_id;

    private String ext1;

    private String sessionId;

    private String requestId;

    private String amountPeriodRateManagement;
    private String amount_period_rate_management;

    private String eventCode;

    private Double apiLine;
    private Double api_line;

    private Double apiAvailLine;
    private Double api_avail_line;

    private Double apiUtilLine;
    private Double api_util_line;

    private String apiPeriodLineRate;
    private String api_period_line_rate;

    private String apiOutputTag;
    private String api_output_tag;

    public Boolean getClosed() {
        return isClosed;
    }

    public void setClosed(Boolean closed) {
        isClosed = closed;
    }

    public Double getApiLine() {
        return apiLine;
    }

    public void setApiLine(Double apiLine) {
        this.apiLine = apiLine;
    }

    public Double getApiAvailLine() {
        return apiAvailLine;
    }

    public void setApiAvailLine(Double apiAvailLine) {
        this.apiAvailLine = apiAvailLine;
    }

    public Double getApiUtilLine() {
        return apiUtilLine;
    }

    public void setApiUtilLine(Double apiUtilLine) {
        this.apiUtilLine = apiUtilLine;
    }

    public String getApiPeriodLineRate() {
        return apiPeriodLineRate;
    }

    public void setApiPeriodLineRate(String apiPeriodLineRate) {
        this.apiPeriodLineRate = apiPeriodLineRate;
    }

    public String getApiOutputTag() {
        return apiOutputTag;
    }

    public void setApiOutputTag(String apiOutputTag) {
        this.apiOutputTag = apiOutputTag;
    }

    public String getEventCode() {
        return eventCode;
    }

    public void setEventCode(String eventCode) {
        this.eventCode = eventCode;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getAmountPeriodRateManagement() {
        return amountPeriodRateManagement;
    }

    public void setAmountPeriodRateManagement(String amountPeriodRateManagement) {
        this.amountPeriodRateManagement = amountPeriodRateManagement;
    }

    public VerifyUserLineManagementBakVo() {
        creditLine = 0.0;
        availLine = 0.0;
        utilLine = 0.0;
        reasonCode = "{}";
        segmentCode = "{}";
        testCode = "{}";
    }

    public String getExt1() {
        return ext1;
    }

    public void setExt1(String ext1) {
        this.ext1 = ext1;
    }

    public String getLineType() {
        return this.lineType;
    }

    public void setLineType(String lineType) {
        this.lineType = lineType;
    }

    public String getTransId() {
        return this.transId;
    }

    public void setTransId(String transId) {
        this.transId = transId;
    }

    public Boolean getStatus() {
        return this.status;
    }

    public void setStatus(Boolean status) {
        this.status = status;
    }

    public String getReasonCode() {
        return this.reasonCode;
    }

    public void setReasonCode(String reasonCode) {
        this.reasonCode = reasonCode;
    }

    public String getSegmentCode() {
        return this.segmentCode;
    }

    public void setSegmentCode(String segmentCode) {
        this.segmentCode = segmentCode;
    }

    public String getTestCode() {
        return this.testCode;
    }

    public void setTestCode(String testCode) {
        this.testCode = testCode;
    }

    public String getUserKey() {
        return this.userKey;
    }

    public void setUserKey(String userKey) {
        this.userKey = userKey == null ? null : userKey.trim();
    }

    public String getSourceSystem() {
        return this.sourceSystem;
    }

    public void setSourceSystem(String sourceSystem) {
        this.sourceSystem = sourceSystem == null ? null : sourceSystem.trim();
    }

    public String getLineChangeType() {
        return this.lineChangeType;
    }

    public void setLineChangeType(String lineChangeType) {
        this.lineChangeType = lineChangeType == null ? null : lineChangeType.trim();
    }

    public Double getCreditLine() {
        return this.creditLine;
    }

    public void setCreditLine(Double creditLine) {
        this.creditLine = creditLine;
    }

    public Double getAvailLine() {
        return this.availLine;
    }

    public void setAvailLine(Double availLine) {
        this.availLine = availLine;
    }

    public Double getUtilLine() {
        return this.utilLine;
    }

    public void setUtilLine(Double utilLine) {
        this.utilLine = utilLine;
    }

    public Double getLoanLine() {
        return this.loanLine;
    }

    public void setLoanLine(Double loanLine) {
        this.loanLine = loanLine;
    }

    public Double getLoanAvailLine() {
        return this.loanAvailLine;
    }

    public void setLoanAvailLine(Double loanAvailLine) {
        this.loanAvailLine = loanAvailLine;
    }

    public Double getLoanActualLine() {
        return this.loanActualLine;
    }

    public void setLoanActualLine(Double loanActualLine) {
        this.loanActualLine = loanActualLine;
    }

    public Double getLoanUtilLine() {
        return this.loanUtilLine;
    }

    public void setLoanUtilLine(Double loanUtilLine) {
        this.loanUtilLine = loanUtilLine;
    }

    public Double getLoanRate() {
        return this.loanRate;
    }

    public void setLoanRate(Double loanRate) {
        this.loanRate = loanRate;
    }

    public Integer getLoanPeriod() {
        return this.loanPeriod;
    }

    public void setLoanPeriod(Integer loanPeriod) {
        this.loanPeriod = loanPeriod;
    }

    public Double getBtLine() {
        return this.btLine;
    }

    public void setBtLine(Double btLine) {
        this.btLine = btLine;
    }

    public Double getBtAvailLine() {
        return this.btAvailLine;
    }

    public void setBtAvailLine(Double btAvailLine) {
        this.btAvailLine = btAvailLine;
    }

    public Double getBtActualLine() {
        return this.btActualLine;
    }

    public void setBtActualLine(Double btActualLine) {
        this.btActualLine = btActualLine;
    }

    public Double getBtUtilLine() {
        return this.btUtilLine;
    }

    public void setBtUtilLine(Double btUtilLine) {
        this.btUtilLine = btUtilLine;
    }

    public Double getBtRate() {
        return this.btRate;
    }

    public void setBtRate(Double btRate) {
        this.btRate = btRate;
    }

    public Integer getBtPeriod() {
        return this.btPeriod;
    }

    public void setBtPeriod(Integer btPeriod) {
        this.btPeriod = btPeriod;
    }

    public Double getShopLine() {
        return this.shopLine;
    }

    public void setShopLine(Double shopLine) {
        this.shopLine = shopLine;
    }

    public Double getShopAvailLine() {
        return this.shopAvailLine;
    }

    public void setShopAvailLine(Double shopAvailLine) {
        this.shopAvailLine = shopAvailLine;
    }

    public Double getShopActualLine() {
        return this.shopActualLine;
    }

    public void setShopActualLine(Double shopActualLine) {
        this.shopActualLine = shopActualLine;
    }

    public Double getShopUtilLine() {
        return this.shopUtilLine;
    }

    public void setShopUtilLine(Double shopUtilLine) {
        this.shopUtilLine = shopUtilLine;
    }

    public Double getShopRate() {
        return this.shopRate;
    }

    public void setShopRate(Double shopRate) {
        this.shopRate = shopRate;
    }

    public Integer getShopPeriod() {
        return this.shopPeriod;
    }

    public void setShopPeriod(Integer shopPeriod) {
        this.shopPeriod = shopPeriod;
    }

    public String getPeriodLineRate() {
        return this.periodLineRate;
    }

    public void setPeriodLineRate(String periodLineRate) {
        this.periodLineRate = periodLineRate;
    }

    public String getAccountStatus() {
        return this.accountStatus;
    }

    public void setAccountStatus(String accountStatus) {
        this.accountStatus = accountStatus == null ? null : accountStatus.trim();
    }

    public Boolean getIsClosed() {
        return this.isClosed;
    }

    public void setIsClosed(Boolean isClosed) {
        this.isClosed = isClosed;
    }

    public Double getUserPoint() {
        return this.userPoint;
    }

    public void setUserPoint(Double userPoint) {
        this.userPoint = userPoint;
    }

    public String getUserLevel() {
        return this.userLevel;
    }

    public void setUserLevel(String userLevel) {
        this.userLevel = userLevel == null ? null : userLevel.trim();
    }

    public Date getLineAssignTime() {
        return this.lineAssignTime;
    }

    public void setLineAssignTime(Date lineAssignTime) {
        this.lineAssignTime = lineAssignTime;
    }

    public String getSource_system() {
        return source_system;
    }

    public void setSource_system(String source_system) {
        this.source_system = source_system;
    }

    public String getLine_change_type() {
        return line_change_type;
    }

    public void setLine_change_type(String line_change_type) {
        this.line_change_type = line_change_type;
    }

    public Double getCredit_line() {
        return credit_line;
    }

    public void setCredit_line(Double credit_line) {
        this.credit_line = credit_line;
    }

    public Double getAvail_line() {
        return avail_line;
    }

    public void setAvail_line(Double avail_line) {
        this.avail_line = avail_line;
    }

    public Double getUtil_line() {
        return util_line;
    }

    public void setUtil_line(Double util_line) {
        this.util_line = util_line;
    }

    public Double getLoan_line() {
        return loan_line;
    }

    public void setLoan_line(Double loan_line) {
        this.loan_line = loan_line;
    }

    public Double getLoan_avail_line() {
        return loan_avail_line;
    }

    public void setLoan_avail_line(Double loan_avail_line) {
        this.loan_avail_line = loan_avail_line;
    }

    public Double getLoan_actual_line() {
        return loan_actual_line;
    }

    public void setLoan_actual_line(Double loan_actual_line) {
        this.loan_actual_line = loan_actual_line;
    }

    public Double getLoan_util_line() {
        return loan_util_line;
    }

    public void setLoan_util_line(Double loan_util_line) {
        this.loan_util_line = loan_util_line;
    }

    public Double getLoan_rate() {
        return loan_rate;
    }

    public void setLoan_rate(Double loan_rate) {
        this.loan_rate = loan_rate;
    }

    public Integer getLoan_period() {
        return loan_period;
    }

    public void setLoan_period(Integer loan_period) {
        this.loan_period = loan_period;
    }

    public Double getBt_line() {
        return bt_line;
    }

    public void setBt_line(Double bt_line) {
        this.bt_line = bt_line;
    }

    public Double getBt_avail_line() {
        return bt_avail_line;
    }

    public void setBt_avail_line(Double bt_avail_line) {
        this.bt_avail_line = bt_avail_line;
    }

    public Double getBt_actual_line() {
        return bt_actual_line;
    }

    public void setBt_actual_line(Double bt_actual_line) {
        this.bt_actual_line = bt_actual_line;
    }

    public Double getBt_util_line() {
        return bt_util_line;
    }

    public void setBt_util_line(Double bt_util_line) {
        this.bt_util_line = bt_util_line;
    }

    public Double getBt_rate() {
        return bt_rate;
    }

    public void setBt_rate(Double bt_rate) {
        this.bt_rate = bt_rate;
    }

    public Integer getBt_period() {
        return bt_period;
    }

    public void setBt_period(Integer bt_period) {
        this.bt_period = bt_period;
    }

    public Double getShop_line() {
        return shop_line;
    }

    public void setShop_line(Double shop_line) {
        this.shop_line = shop_line;
    }

    public Double getShop_avail_line() {
        return shop_avail_line;
    }

    public void setShop_avail_line(Double shop_avail_line) {
        this.shop_avail_line = shop_avail_line;
    }

    public Double getShop_actual_line() {
        return shop_actual_line;
    }

    public void setShop_actual_line(Double shop_actual_line) {
        this.shop_actual_line = shop_actual_line;
    }

    public Double getShop_util_line() {
        return shop_util_line;
    }

    public void setShop_util_line(Double shop_util_line) {
        this.shop_util_line = shop_util_line;
    }

    public Double getShop_rate() {
        return shop_rate;
    }

    public void setShop_rate(Double shop_rate) {
        this.shop_rate = shop_rate;
    }

    public Integer getShop_period() {
        return shop_period;
    }

    public void setShop_period(Integer shop_period) {
        this.shop_period = shop_period;
    }

    public String getPeriod_line_rate() {
        return period_line_rate;
    }

    public void setPeriod_line_rate(String period_line_rate) {
        this.period_line_rate = period_line_rate;
    }

    public String getAccount_status() {
        return account_status;
    }

    public void setAccount_status(String account_status) {
        this.account_status = account_status;
    }

    public Boolean getIs_closed() {
        return is_closed;
    }

    public void setIs_closed(Boolean is_closed) {
        this.is_closed = is_closed;
    }

    public Double getUser_point() {
        return user_point;
    }

    public void setUser_point(Double user_point) {
        this.user_point = user_point;
    }

    public String getUser_level() {
        return user_level;
    }

    public void setUser_level(String user_level) {
        this.user_level = user_level;
    }

    public Date getLine_assign_time() {
        return line_assign_time;
    }

    public void setLine_assign_time(Date line_assign_time) {
        this.line_assign_time = line_assign_time;
    }

    public String getReason_code() {
        return reason_code;
    }

    public void setReason_code(String reason_code) {
        this.reason_code = reason_code;
    }

    public String getSegment_code() {
        return segment_code;
    }

    public void setSegment_code(String segment_code) {
        this.segment_code = segment_code;
    }

    public String getTest_code() {
        return test_code;
    }

    public void setTest_code(String test_code) {
        this.test_code = test_code;
    }

    public String getLine_type() {
        return line_type;
    }

    public void setLine_type(String line_type) {
        this.line_type = line_type;
    }

    public String getTrans_id() {
        return trans_id;
    }

    public void setTrans_id(String trans_id) {
        this.trans_id = trans_id;
    }

    public String getAmount_period_rate_management() {
        return amount_period_rate_management;
    }

    public void setAmount_period_rate_management(String amount_period_rate_management) {
        this.amount_period_rate_management = amount_period_rate_management;
    }

    public Double getApi_line() {
        return api_line;
    }

    public void setApi_line(Double api_line) {
        this.api_line = api_line;
    }

    public Double getApi_avail_line() {
        return api_avail_line;
    }

    public void setApi_avail_line(Double api_avail_line) {
        this.api_avail_line = api_avail_line;
    }

    public Double getApi_util_line() {
        return api_util_line;
    }

    public void setApi_util_line(Double api_util_line) {
        this.api_util_line = api_util_line;
    }

    public String getApi_period_line_rate() {
        return api_period_line_rate;
    }

    public void setApi_period_line_rate(String api_period_line_rate) {
        this.api_period_line_rate = api_period_line_rate;
    }

    public String getApi_output_tag() {
        return api_output_tag;
    }

    public void setApi_output_tag(String api_output_tag) {
        this.api_output_tag = api_output_tag;
    }

    @Override
    public boolean equals(Object o) {

        if (this == o) {
            return true;
        }

        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        VerifyUserLineManagementBakVo that = (VerifyUserLineManagementBakVo) o;

        return new EqualsBuilder()
                .append(userKey, that.userKey)
                .append(sourceSystem, that.sourceSystem)
                .append(lineChangeType, that.lineChangeType)
                .append(creditLine, that.creditLine)
                .append(availLine, that.availLine)
                .append(utilLine, that.utilLine)
                .append(loanLine, that.loanLine)
                .append(loanAvailLine, that.loanAvailLine)
                .append(loanActualLine, that.loanActualLine)
                .append(loanUtilLine, that.loanUtilLine)
                .append(loanRate, that.loanRate)
                .append(loanPeriod, that.loanPeriod)
                .append(btLine, that.btLine)
                .append(btAvailLine, that.btAvailLine)
                .append(btActualLine, that.btActualLine)
                .append(btUtilLine, that.btUtilLine)
                .append(btRate, that.btRate)
                .append(btPeriod, that.btPeriod)
                .append(shopLine, that.shopLine)
                .append(shopAvailLine, that.shopAvailLine)
                .append(shopActualLine, that.shopActualLine)
                .append(shopUtilLine, that.shopUtilLine)
                .append(shopRate, that.shopRate)
                .append(shopPeriod, that.shopPeriod)
                .append(periodLineRate, that.periodLineRate)
                .append(accountStatus, that.accountStatus)
                .append(isClosed, that.isClosed)
                .append(userPoint, that.userPoint)
                .append(userLevel, that.userLevel)
//                .append(lineAssignTime, that.lineAssignTime)
                .append(JsonUtils.toObject(reasonCode, Map.class), JsonUtils.toObject(that.reasonCode, Map.class))
                .append(JsonUtils.toObject(segmentCode, Map.class), JsonUtils.toObject(that.segmentCode, Map.class))
                .append(JsonUtils.toObject(testCode, Map.class), JsonUtils.toObject(that.testCode, Map.class))
                .append(status, that.status)
                .append(lineType, that.lineType)
                .append(transId, that.transId)
                .isEquals();
    }

}