package com.youxin.risk.verify.service.impl;

import com.youxin.risk.commons.utils.JsonUtils;
import com.youxin.risk.verify.enums.SubmitDataType;
import com.youxin.risk.verify.service.DcSystemService;
import com.youxin.risk.verify.service.SubmitService;
import com.youxin.risk.verify.service.VerifyHttpRequestService;
import com.youxin.risk.verify.vo.DcReqVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.concurrent.*;

@Service
@Transactional
public class SubmitServiceImpl implements SubmitService {

    @Value("${risk-datacenter.url}")
    private String dcUrl;

    @Autowired
    private VerifyHttpRequestService requestService;
    @Autowired
    DcSystemService dcSystemService;
    //自定义线程池 提交到dc
    private ExecutorService dcHandlerThreadPool = new ThreadPoolExecutor(100, 100,
            5, TimeUnit.MINUTES, new ArrayBlockingQueue<>(10000), new ThreadPoolExecutor.CallerRunsPolicy());

    private static final Logger LOG = LoggerFactory.getLogger(SubmitServiceImpl.class);

    /**
     * 提交业务端数据到datacenter
     * @param obj
     * @param submitDataType
     */
    @Override
    public void submitInformationToDc( Object obj , SubmitDataType submitDataType , String userKey) {
        try{
            HashMap<String,Object> paras = new HashMap<>();
            paras.put("submitData", obj);
            DcReqVo dcReqVo = dcSystemService.buildDcReq(paras, submitDataType, userKey);
            //构建dc请求
            this.dcHandlerThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    SubmitServiceImpl.this.submitDcData(dcReqVo);
                }
            });
        }catch (Exception ex){
            LOG.error("submitDcData error,postData={},submitDataType={}", obj, submitDataType);
        }
    }

    private void submitDcData(Object obj) {
        try{
            boolean b = dcSystemService.submitDcData(obj);
            if(!b){//提交数据到dc失败
                this.requestService.addNewAppRequest(dcUrl, DcSystemServiceImpl.URL, obj);
                LOG.error("submitDcData error,postData={}",JsonUtils.toJson(obj));
            }else{
                LOG.info("submitDcData success,data={}", JsonUtils.toJson(obj));
            }
        }catch (Exception e){
            LOG.error("submitDcData error,data={}",JsonUtils.toJson(obj),e);
        }
    }
}
