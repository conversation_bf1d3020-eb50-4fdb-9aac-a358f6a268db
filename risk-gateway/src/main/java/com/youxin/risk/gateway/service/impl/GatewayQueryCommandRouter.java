package com.youxin.risk.gateway.service.impl;

import com.youxin.risk.commons.constants.EventVariableKeyEnum;
import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.model.GwRequest;
import com.youxin.risk.commons.utils.JacksonUtil;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.StringUtils;
import com.youxin.risk.gateway.service.GatewayRouter;
import com.youxin.risk.gateway.service.GwRequestModelService;
import com.youxin.risk.gateway.vo.GatewayVo;
import org.apache.commons.beanutils.BeanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Service("gatewayQueryCommandRouter")
public class GatewayQueryCommandRouter implements GatewayRouter, InitializingBean {


    private Logger logger = LoggerFactory.getLogger(GatewayQueryCommandRouter.class);

    private static final String SEPARATOR_CHAR = ",";

    private Map<String, String> queryParams = new HashMap<>();

    @Resource
    private GwRequestModelService gwRequestModelService;

    @Override
    public void afterPropertiesSet() throws Exception {
        queryParams.put(EventVariableKeyEnum.requestId.name(), EventVariableKeyEnum.requestId.name());
    }

    @Override
    public void route(GatewayVo gwvo) {

        check(gwvo);
        if (RetCodeEnum.ILLEGAL_ARGUMENT == gwvo.getRetCode()) {
            return;
        }
        GwRequest gwRequest = buildQueryModel(gwvo);
        if (RetCodeEnum.ILLEGAL_ARGUMENT == gwvo.getRetCode()) {
            return;
        }
        GwRequest ret = gwRequestModelService.select(gwRequest);
        if (null == ret) {
            buildRes(gwvo, RetCodeEnum.NO_DATA, "");
            return;
        }
        if (StringUtils.isBlank(ret.getCallbackMessage())) {
            buildRes(gwvo, RetCodeEnum.PROCESSING, "");
            return;
        }

        gwvo.setResultPlaintext(JacksonUtil.toJson(JacksonUtil.toObject(ret.getCallbackMessage(), Map.class)));
        LoggerProxy.info("gwQueryResult", logger, "callbackMessgae=" + gwvo.getResultPlaintext());
    }

    private GwRequest buildQueryModel(GatewayVo gwvo) {
        Event event = gwvo.getEvent();
        String queryKey = event.getString(EventVariableKeyEnum.queryKey.name());
        String queryValue = event.getString(EventVariableKeyEnum.queryValue.name());
        String[] keys = StringUtils.split(queryKey, SEPARATOR_CHAR);
        String[] values = StringUtils.split(queryValue, SEPARATOR_CHAR);
        try {
            GwRequest gwRequest = new GwRequest();
            gwRequest.setIcode(gwvo.getIcode());
            for (int i = 0, size = keys.length; i < size; i++) {
                String key = keys[i];
                String value = values[i];
                BeanUtils.setProperty(gwRequest, queryParams.get(key), value);
            }
            return gwRequest;
        } catch (Exception e) {
            LoggerProxy.warn("queryGatewayRequestException", logger, "", e);
            buildRes(gwvo, RetCodeEnum.ILLEGAL_ARGUMENT,
                    "'" + EventVariableKeyEnum.queryKey + "' or '" + EventVariableKeyEnum.queryValue + "'");
        }
        return null;
    }

    private void check(GatewayVo gwvo) {
        Event event = gwvo.getEvent();
        String queryKey = event.getString(EventVariableKeyEnum.queryKey.name());
        String queryValue = event.getString(EventVariableKeyEnum.queryValue.name());
        if (StringUtils.isBlank(queryKey)) {
            buildRes(gwvo, RetCodeEnum.ILLEGAL_ARGUMENT, "'" + EventVariableKeyEnum.queryKey + "'");
            return;
        }
        if (StringUtils.isBlank(queryValue)) {
            buildRes(gwvo, RetCodeEnum.ILLEGAL_ARGUMENT, "'" + EventVariableKeyEnum.queryValue + "'");
            return;
        }
        String[] keys = StringUtils.split(queryKey, SEPARATOR_CHAR);
        String[] values = StringUtils.split(queryValue, SEPARATOR_CHAR);
        if (null == keys || 0 == keys.length) {
            buildRes(gwvo, RetCodeEnum.ILLEGAL_ARGUMENT, "'" + EventVariableKeyEnum.queryKey + "'");
            return;
        }
        if (null == values || 0 == values.length) {
            buildRes(gwvo, RetCodeEnum.ILLEGAL_ARGUMENT, "'" + EventVariableKeyEnum.queryValue + "'");
            return;
        }
        if (keys.length != values.length) {
            buildRes(gwvo, RetCodeEnum.ILLEGAL_ARGUMENT,
                    "'" + EventVariableKeyEnum.queryKey + "' and '" + EventVariableKeyEnum.queryValue + "'");
            return;
        }
        for (int i = 0, size = keys.length; i < size; i++) {
            if (StringUtils.isBlank(keys[i]) || !queryParams.containsKey(keys[i])) {
                buildRes(gwvo, RetCodeEnum.ILLEGAL_ARGUMENT, "'" + EventVariableKeyEnum.queryKey + "'");
                return;
            }
            if (StringUtils.isBlank(values[i])) {
                buildRes(gwvo, RetCodeEnum.ILLEGAL_ARGUMENT, "'" + EventVariableKeyEnum.queryKey + "'");
                return;
            }
        }
    }


    private void buildRes(GatewayVo gwvo, RetCodeEnum retCodeEnum, String retMsg) {
        Map<String, String> res = new HashMap<>();
        res.put(EventVariableKeyEnum.retCode.name(), retCodeEnum.getValue());
        res.put(EventVariableKeyEnum.retMsg.name(), null == retMsg ? "" : retMsg);
        gwvo.setRetCode(retCodeEnum);
        gwvo.setRetMsg(null == retMsg ? "" : retMsg);
        gwvo.setResultPlaintext(JacksonUtil.toJson(res));
    }
}
