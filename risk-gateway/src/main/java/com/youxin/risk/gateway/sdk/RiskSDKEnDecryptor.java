package com.youxin.risk.gateway.sdk;

import com.youxin.risk.gateway.sdk.codec.Base64;

import java.security.PrivateKey;
import java.security.PublicKey;
import java.util.HashMap;
import java.util.Map;

public class RiskSDKEnDecryptor {

    /**
     * 解密&延签api
     *
     * @param publicKey  对方公钥
     * @param privateKey 己方私钥
     * @param paramMap   请求参数
     * @return
     * @throws Exception
     */
    public static String decrypt(PublicKey publicKey, PrivateKey privateKey, Map<String, String> paramMap) throws Exception {

        String sign = paramMap.remove("sign");
        if (null == sign || "".equals(sign.trim())) {
            throw new RiskSDKSecRuntimeException(RiskSDKRetCodeEnum.UNSAFE, "验证sign失败");
        }
        String key = paramMap.get("key");
        String salt = paramMap.get("salt");
        String data = paramMap.get("data");

        if (!RiskSDKSecurity.verifySignWithSHA256RSA(publicKey, RiskSDKSecurity.map2strAsc(paramMap), sign)) {
            throw new RiskSDKSecRuntimeException(RiskSDKRetCodeEnum.UNSAFE, "验证sign失败");
        }
        // 解密业务数据
        String plainKey = RiskSDKSecurity.decryptRSA(privateKey, key);
        String plainSalt = RiskSDKSecurity.decryptRSA(privateKey, salt);
        String plaintext = RiskSDKSecurity.decryptAES(Base64.decodeBase64(plainKey), Base64.decodeBase64(plainSalt), data);
        return plaintext;
    }

    /**
     * 加密&生成签名
     *
     * @param icode      生成签名icode
     * @param publicKey  对方公钥
     * @param privateKey 已方私钥
     * @param plaintext  待加密明文
     * @return 加密后的map，字段含义见接口文档
     * @throws Exception
     */
    public static Map<String, String> encrypt(String icode, PublicKey publicKey, PrivateKey privateKey, String plaintext) throws Exception {

        byte[] aesKey = RiskSDKSecurity.getRandomAES128SecretKey();
        byte[] aesIV = RiskSDKSecurity.getRandomAES16IV();

        String result = RiskSDKSecurity.encryptAES(aesKey, aesIV, plaintext);
        String key = RiskSDKSecurity.encryptRSA(publicKey, Base64.encodeBase64String(aesKey));
        String salt = RiskSDKSecurity.encryptRSA(publicKey, Base64.encodeBase64String(aesIV));

        Map<String, String> paramMap = new HashMap<String, String>();
        paramMap.put("icode", icode);
        paramMap.put("salt", salt);
        paramMap.put("key", key);
        paramMap.put("data", result);

        String sign = RiskSDKSecurity.genSignWithSHA256RSA(privateKey, RiskSDKSecurity.map2strAsc(paramMap));
        paramMap.put("sign", sign);
        return paramMap;
    }
}