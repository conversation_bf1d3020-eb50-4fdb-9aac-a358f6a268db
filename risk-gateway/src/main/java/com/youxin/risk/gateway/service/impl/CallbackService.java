package com.youxin.risk.gateway.service.impl;

import com.alibaba.fastjson.JSONPath;
import com.youxin.risk.commons.cache.CacheApi;
import com.youxin.risk.commons.constants.EventVariableKeyEnum;
import com.youxin.risk.commons.constants.GatewayParameterName;
import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.exception.RiskRuntimeException;
import com.youxin.risk.commons.model.AdminAgency;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.model.EventInfo;
import com.youxin.risk.commons.model.EventOutput;
import com.youxin.risk.commons.utils.JacksonUtil;
import com.youxin.risk.commons.utils.JsonGrayFeatureUtil;
import com.youxin.risk.commons.utils.JsonKeyFormatUtil;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.ValueComparisonUtil;
import com.youxin.risk.gateway.contants.RequestType;
import com.youxin.risk.gateway.service.GwEventCustomHandlerAgent;
import com.youxin.risk.gateway.service.GwMiniRequesModelService;
import com.youxin.risk.gateway.service.GwRequestModelService;
import com.youxin.risk.gateway.utils.GatewayPointUtil;
import com.youxin.risk.gateway.vo.GatewayDataVo;
import com.youxin.risk.gateway.vo.GatewayVo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Service
public class CallbackService {

    private Logger logger = LoggerFactory.getLogger(CallbackService.class);

    @Resource
    private GwRequestModelService gwRequestModelService;

    @Autowired
    private GwMiniRequesModelService gwMiniRequesModelService;

    @Resource
    private GwEventCustomHandlerAgent gwEventCustomHandlerAgent;

    @Resource
    private Map<String, String> retCodeChecker;

    @Resource(name = "gatewayCallBusinessThreadPool")
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    public void callback(Event event) {

        String icode = event.getString(EventVariableKeyEnum.icode.name());
        AdminAgency adminAgency = CacheApi.getAgencyByIcode(icode);
        if (null == adminAgency) {
            LoggerProxy.error("notFoundAdminAgency", logger, "icode={}", icode);
            return;
        }
        // todo 这里对haoHuanLendAuditReloan进行转换
        if (StringUtils.equals(event.getEventCode(), "haoHuanLendAuditReloan")) {
            event.setEventCode("haoHuanLendAudit");
        }

        EventInfo eventInfo = CacheApi.getEventInfo(event.getEventCode());
        if (null == eventInfo) {
            LoggerProxy.error("notFountEventConfig", logger, "eventCode=" + event.getEventCode());
            return;
        }

        GatewayVo gatewayVo = buildCallbackGwvo(eventInfo, event, adminAgency);
        gatewayVo.setSessionId(event.getSessionId());
        gatewayVo.setEvent(event);
        // 设置gw_request的状态为FETCHED，标志engine回调成功
        updateGwRequest(gatewayVo.getResultPlaintext(), gatewayVo.getSessionId());
        // 异步回调上游应用
        callback(gatewayVo, event);
    }

    public void callback(GatewayVo gatewayVo, Event event) {
        threadPoolTaskExecutor.execute(new CallbackTask(gatewayVo, event));
    }

    private GatewayVo buildCallbackGwvo(EventInfo eventInfo, Event event, AdminAgency adminAgency) {
        GatewayDataVo gatewayDataVo = new GatewayDataVo();
        gatewayDataVo.setMessage(buildMessage(eventInfo, event));
        gatewayDataVo.setRequestId(event.getSessionId());
        gatewayDataVo.setAgencyRequestId(event.getString(EventVariableKeyEnum.agencyRequestId.name()));
        gatewayDataVo.setType(RequestType.async.name());

        GatewayVo gatewayVo = new GatewayVo();
        gatewayVo.setResultPlaintext(JacksonUtil.toJson(gatewayDataVo));
        gatewayVo.setAdminAgency(adminAgency);
        gatewayVo.setIcode(adminAgency.getIcode());
        gatewayVo.setSessionId(event.getSessionId());
        return gatewayVo;
    }

    private void updateGwRequest(String callbackMessage, String sessionId) {
        try {
            gwRequestModelService.updateCallbackMsg(callbackMessage, sessionId);
        } catch (Exception e) {
            LoggerProxy.error("updateCallbackMsgException", logger, "", e);
        }
    }

    private Map<String, Object> buildMessage(EventInfo eventInfo, Event event) {
        Map<String, Object> message = new HashMap<>();
        // 公共字段
        message.put(EventVariableKeyEnum.loanKey.name(), event.getLoanKey());
        message.put(EventVariableKeyEnum.sessionId.name(), event.getSessionId());
        message.put(EventVariableKeyEnum.eventCode.name(), event.getEventCode());
        if (CollectionUtils.isEmpty(eventInfo.getOutputs())) {
            LoggerProxy.info("eventResultVariablesIsNull", logger, "eventCode=" + event.getEventCode());
            return message;
        }

        Map<String, Object> messageNew = new HashMap<>();
        // 灰度兼容下划线
        if (JsonGrayFeatureUtil.checkGrayFeature(event.getUserKey())) {
            for (EventOutput output : eventInfo.getOutputs()) {
                String resultKey = output.getIsTrans() ? output.getTransCode() : output.getVariableCode();
                String resultUnderScoreKey = JsonKeyFormatUtil.camelToUnderscore(resultKey);

                String variableCode = JsonKeyFormatUtil.camelToUnderscore(output.getVariableCode());
                Object underScoreValue = caseValue(event, variableCode);

                if (output.getIsRequired() && (null == underScoreValue || StringUtils.isBlank(underScoreValue.toString()))) {
                    LoggerProxy.warn("mustUnderScoreResultVariableIsNull", logger
                            , "variableCode={}, eventCode={}", variableCode, event.getEventCode());
                    continue;
                }
                messageNew.put(resultUnderScoreKey, underScoreValue);
            }
        }

        for (EventOutput output : eventInfo.getOutputs()) {
            String resultKey = output.getIsTrans() ? output.getTransCode() : output.getVariableCode();
            Object value = caseValue(event, output.getVariableCode());

            if (output.getIsRequired() && (null == value || StringUtils.isBlank(value.toString()))) {
                LoggerProxy.warn("mustResultVariableIsNull", logger, "variableCode={}, eventCode={}", output.getVariableCode(), event.getEventCode());
                continue;
            }
            message.put(resultKey, value);
        }

        if (JsonGrayFeatureUtil.checkGrayFeature(event.getUserKey())) {
            for (EventOutput output : eventInfo.getOutputs()) {
                String resultKey = output.getIsTrans() ? output.getTransCode() : output.getVariableCode();
                String resultUnderScoreKey = JsonKeyFormatUtil.camelToUnderscore(resultKey);

                Object underScoreValue = messageNew.get(resultUnderScoreKey);
                Object value = message.get(resultKey);

                // 增加下划线值和原来key的值对比，值可能是json
                ValueComparisonUtil.compareValues(resultKey, resultUnderScoreKey, value, underScoreValue,
                    event.getUserKey(), event.getEventCode(), "callbackValueDifference");
            }
        }
        return message;
    }

    private Object caseValue(Event event, String variableKey) {
        if (null == event) {
            return "";
        }
        Object value;
        if (null != CacheApi.getOriginVariable(variableKey)) {
            value = event.get(variableKey);
        } else {
            value = MapUtils.isEmpty(event.getVerifyResult()) ? null : event.getVerifyResult().get(variableKey);
        }
        if (null == value) {
            return "";
        }
        if (value instanceof Number || value instanceof String
                || value instanceof Boolean || value instanceof Character) {
            return String.valueOf(value);
        }
        return value;
    }



    /**
     * 回调业务方任务
     *    1.构建回调请求参数
     *    2.回调业务方
     *    3.返回结果的检查
     *    4.返回结果正确更新gw_request状态为CALLBACK
     *    5.打点&监控
     *    6.释放卡单标识
     */
    private class CallbackTask implements Runnable {
        private final GatewayVo gatewayVo;
        private final Event event;

        private CallbackTask(GatewayVo gatewayVo, Event event) {
            this.gatewayVo = gatewayVo;
            this.event = event;
        }

        @Override
        public void run() {
            try {
                long start = System.currentTimeMillis();
                // 1.构建回调请求参数
                Map<String, String> callbackParams = buildCallbackParams(gatewayVo);
                String callbackRes = "";
                try {
                    // 2.回调业务方
                    callbackRes = gwEventCustomHandlerAgent.callback(callbackParams, gatewayVo);
                    // 3.返回结果的检查
                    checkRetCode(gatewayVo,callbackRes);
                    checkRet(gatewayVo, callbackRes);
                    // 4.更新gw_request状态为CALLBACK

                    updateGwRequestCallbacked(gatewayVo.getSessionId());
                    // 5.打点&监控
                    GatewayPointUtil.gatewayNotifyPoint(gatewayVo);
                    // 6.释放卡单标识
                    releaseFlag();
                } finally {
                    log(callbackParams, callbackRes, gatewayVo.getDataPlaintext(), start);
                }
            }catch (Exception e) {
                // 此处异常不处理，有补偿任务
                LoggerProxy.warn("callbackException", logger, "", e);
            }
        }

        private void releaseFlag() {
            try {
                gwMiniRequesModelService.deleteBySessionId(gatewayVo.getSessionId());
            }catch (Exception e){
                LoggerProxy.error("deleteGwMiniRequestError", logger, "delete requestMini fail", e);
            }
        }

        private Map<String, String> buildCallbackParams(GatewayVo gatewayVo) {
            Map<String, Object> responseMsg = gatewayVo.buildResponseMsg();
            Map<String, String> callbackParams = new HashMap<>();
            for (Map.Entry<String, Object> e : responseMsg.entrySet()) {
                if (e.getValue() instanceof String) {
                    callbackParams.put(e.getKey(), (String) e.getValue());
                } else {
                    callbackParams.put(e.getKey(), JacksonUtil.toJson(e.getValue()));
                }
            }
            return callbackParams;
        }

        private void checkRetCode(GatewayVo gwvo, String response) {
            if (retCodeChecker == null) {
                LoggerProxy.warn("retCodeCheckerNull", logger, "retCodeChecker is null");
                return;
            }
            String kv = retCodeChecker.get(gwvo.getEvent().getEventCode());
            if (StringUtils.isBlank(kv)) {
                LoggerProxy.info("eventNotCheck", logger, "do not check retCode for event={}", gwvo.getEvent().getEventCode());
                return;
            }

            String[] array = kv.split("=");
            if (array.length != 2) {
                LoggerProxy.warn("retCodeCheckConfigErr", logger, "retCodeChecker config error,eventCode={},config={}",
                        gwvo.getEvent().getEventCode(), kv);
                return;
            }
            String key = array[0];
            String value = array[1];
            String retCode = "" + JSONPath.read(response, key);
            if (!value.equals(retCode)) {
                LoggerProxy.error("retCodeErr",logger,"retCodeErr event={},response={},sessionId={},userKey:{}",gwvo.getEvent().getEventCode(),response,gwvo.getSessionId(), gwvo.getEvent().getUserKey());
                throw new RiskRuntimeException(RetCodeEnum.FAILED, "callback retCode is " + retCode);
            }
        }

        private void checkRet(GatewayVo gwvo, String response) {
            Map<String, Object> responseMap = JacksonUtil.toObject(response, Map.class);
            gwvo.setSalt((String) responseMap.get(GatewayParameterName.salt.name()));
            gwvo.setKey((String) responseMap.get(GatewayParameterName.key.name()));
            gwvo.setSign((String) responseMap.get(GatewayParameterName.sign.name()));
            if (responseMap.get(GatewayParameterName.data.name()) instanceof String) {
                gwvo.setData((String) responseMap.get(GatewayParameterName.data.name()));
            } else {
                gwvo.setData(JacksonUtil.toJson(responseMap.get(GatewayParameterName.data.name())));
            }
            String dataPlaintext = gwvo.getDataPlaintext();

            Map<String, Object> retPlaintextMap = JacksonUtil.toObject(dataPlaintext, Map.class);
            if (MapUtils.isEmpty(retPlaintextMap)) {
                throw new RiskRuntimeException(RetCodeEnum.FAILED, "callback response=" + dataPlaintext);
            }
            String retCode = (String) retPlaintextMap.get(EventVariableKeyEnum.retCode.toString());
            if (!RetCodeEnum.isSuccess(retCode)) {
                // 回调非成功retCode，暂时不重试
                // throw new RiskRuntimeException(RetCodeEnum.FAILED, "callback retCode is " + retCode);
                LoggerProxy.warn("callbackRetcodeIsNotSucess", logger, "retCode=" + retCode);
            }
        }

        private void updateGwRequestCallbacked(String sessionId) {
            try {
                long start = System.currentTimeMillis();
                int ret = gwRequestModelService.updateCallbacked(sessionId);
                LoggerProxy.info("updateCallbacked", logger, "ret={} cost={} sessionId{}", ret, System.currentTimeMillis() - start,sessionId);
            } catch (Exception e) {
                LoggerProxy.error("updateCallbackedException", logger, "", e);
            }


        }

        private void log(Map<String, String> callbackParams, String callbackRet, String plaintextRet, long start) {
            StringBuilder s = new StringBuilder("request=[");
            for (Map.Entry<String, String> e : callbackParams.entrySet()) {
                s.append(e.getKey()).append("=").append(e.getValue()).append("&");
            }
            s.append("]");
            s.append(" callbackRet=").append(callbackRet);
            s.append(" plaintextRet=").append(plaintextRet);
            s.append(" cost=").append(System.currentTimeMillis() - start);
            LoggerProxy.info("callbackURL", logger, s.toString());
        }
    }
}