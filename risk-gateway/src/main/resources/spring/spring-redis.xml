<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="
		http://www.springframework.org/schema/beans
		http://www.springframework.org/schema/beans/spring-beans-3.1.xsd">

    <bean id="genericObjectPoolConfig" class="org.apache.commons.pool2.impl.GenericObjectPoolConfig">
        <property name="maxTotal" value="${redis.maxTotal}" />
        <property name="maxIdle" value="${redis.maxIdle}"/>
        <property name="minIdle" value="${redis.minIdle}"/>
        <property name="maxWaitMillis" value="${redis.maxWaitMillis}"/>
        <property name="testOnBorrow" value="${redis.testOnBorrow}" />
    </bean>
    <bean id="jedisCluster" class="com.youxin.risk.commons.tools.redis.JedisClusterFactory">
        <property name="genericObjectPoolConfig" ref="genericObjectPoolConfig"/>
        <property name="connectionTimeout" value="${redis.cluster.connectionTimeout}"/>
        <property name="soTimeout" value="${redis.cluster.soTimeout}"/>
        <property name="maxAttempts" value="${redis.cluster.maxAttempts}"/>
        <property name="password" value="${redis.cluster.password}"/>
        <property name="nodes" value="${redis.cluster.nodes}"/>
    </bean>

    <bean id="retryableJedis" class="com.youxin.risk.commons.tools.redis.RetryableJedis">
        <property name="jedisCluster" ref="jedisCluster"/>
    </bean>



    <bean id="jedisPoolConfig" class="redis.clients.jedis.JedisPoolConfig">
        <property name="maxTotal" value="5000" />
        <property name="maxIdle" value="100" />
        <property name="maxWaitMillis" value="5000" />
        <property name="testOnBorrow" value="false" />
    </bean>

    <!-- jedisCluster config -->
    <bean id="cacheRedisService" class="com.youxin.risk.verify.service.impl.RedisClusterServiceImpl">
        <constructor-arg index="0" value="${verify.redis.cluster.nodes}" />
        <constructor-arg index="1" value="5000" />
        <constructor-arg index="2" value="60000" />
        <constructor-arg index="3" value="3" />
        <constructor-arg index="4" value="${verify.redis.cluster.password}" />
        <constructor-arg index="5" ref="jedisPoolConfig" />
    </bean>


</beans>