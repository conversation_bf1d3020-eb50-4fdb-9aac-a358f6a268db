<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.gateway.mapper.GatewayBatchMessageLogMapper">
    <resultMap id="BaseResultMap" type="com.youxin.risk.gateway.vo.GatewayMessageLog">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_key" property="userKey" jdbcType="VARCHAR"/>
        <result column="session_id" property="sessionId" jdbcType="VARCHAR"/>
        <result column="event_code" property="eventCode" jdbcType="VARCHAR"/>
        <result column="gateway_message" property="gatewayMessage" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="table_name">
      gw_batch_message_log
    </sql>

    <sql id="save_Base_Column_List">
        user_key,session_id, event_code,gateway_message,create_time
    </sql>

    <sql id="Base_Column_List">
        id, <include refid="save_Base_Column_List"/>
    </sql>


    <insert id="insert" parameterType="com.youxin.risk.gateway.vo.GatewayMessageLog">
        insert into <include refid="table_name"/> (
        user_key,session_id, loan_key,event_code,gateway_message,create_time
        ) values
        (
        #{userKey},
        #{sessionId},
        #{loanKey},
        #{eventCode},
        #{gatewayMessage},
        #{createTime}
        )
    </insert>

    <delete id="deleteById">
      delete  from <include refid="table_name"/>
      where id = #{id}
    </delete>
    <delete id="deleteByIdList">
        delete  from <include refid="table_name"/>
        where id in
        <foreach collection="idList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <select id="selectList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from <include refid="table_name"/>
        order by id limit #{limit}
    </select>

    <select id="getListByEventCodeAndLimit" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from <include refid="table_name"/>
        where event_code = #{eventCode}
        order by id DESC limit #{limit}
    </select>

    <select id="getListByExclusionEventCodeAndLimit" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from <include refid="table_name"/>
        where event_code not in
        <foreach collection="eventCodeList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by id limit #{limit}
    </select>

    <select id="getListByCondition" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM <include refid="table_name"/>
        WHERE event_code = #{condition.eventCode}
        <if test="condition.delayTime != null">
            AND TIMESTAMPDIFF(SECOND, create_time, NOW()) > #{condition.delayTime}
        </if>
        ORDER BY id LIMIT #{condition.limit}
    </select>

    <select id="getListByConditionForUserLevel" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM <include refid="table_name"/>
        WHERE id >= 179526189 and event_code = #{condition.eventCode}
        ORDER BY id LIMIT #{condition.limit}
    </select>
</mapper>