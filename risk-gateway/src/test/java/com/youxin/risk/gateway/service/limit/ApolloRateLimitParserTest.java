package com.youxin.risk.gateway.service.limit;

import com.youxin.apollo.client.NacosClient;
import com.youxin.risk.commons.apollo.ApolloClientAdapter;
import com.youxin.risk.commons.apollo.ApolloNamespaceEnum;
import com.youxin.risk.commons.limiter.ApolloRateLimitParser;
import com.youxin.risk.commons.limiter.RateLimit;
import com.youxin.risk.commons.limiter.RateLimitWithDateTime;
import com.youxin.risk.commons.limiter.RateLimitWithTime;
import junit.framework.TestCase;
import org.hamcrest.core.IsCollectionContaining;
import org.hamcrest.core.IsEqual;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Map;

import static org.hamcrest.MatcherAssert.assertThat;

@RunWith(PowerMockRunner.class)
@PrepareForTest({NacosClient.class})
public class ApolloRateLimitParserTest extends TestCase {
    private static final String CONFIG_KEY = "configKey";
    private static final ApolloNamespaceEnum TEST_NAMESPACE = ApolloNamespaceEnum.COMMON_SPACE;

    @Test
    public void testParseTime() {
        PowerMockito.mockStatic(NacosClient.class);
        Mockito.when(NacosClient.getByNameSpace(TEST_NAMESPACE.namespace, CONFIG_KEY, ApolloClientAdapter.EMPTY_CONFIG)).thenReturn(getTimeJsonString());
        ApolloRateLimitParser parser = new ApolloRateLimitParser(TEST_NAMESPACE, CONFIG_KEY);
        Map<String, List<RateLimit>> rateLimitMap = parser.parse();
        assertThat(rateLimitMap.size(), IsEqual.equalTo(1));
        assertThat(rateLimitMap.keySet(), IsCollectionContaining.hasItem("hhPreLoanAudit"));
        assertThat(rateLimitMap.get("hhPreLoanAudit").size(), IsEqual.equalTo(2));
        assertThat(rateLimitMap.get("hhPreLoanAudit"), IsCollectionContaining.hasItem(new RateLimitWithTime(0.01, LocalTime.of(9, 0), LocalTime.of(11, 0))));
        assertThat(rateLimitMap.get("hhPreLoanAudit"), IsCollectionContaining.hasItem(new RateLimitWithDateTime(1.5,
                LocalDateTime.of(2021, 12, 18, 11, 0), LocalDateTime.of(2021, 12, 18, 16, 30))));
    }

    private String getTimeJsonString() {
        return "{\n" +
                "    \"hhPreLoanAudit\": [\n" +
                "        {\n" +
                "            \"rate\": \"0.01\",\n" +
                "            \"startTime\": \"09:00\",\n" +
                "            \"endTime\": \"11:00\",\n" +
                "            \"type\":\"TIME\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"rate\": \"1.5\",\n" +
                "            \"startTime\": \"2021-12-18 11:00\",\n" +
                "            \"endTime\": \"2021-12-18 16:30\",\n" +
                "            \"type\":\"DATETIME\"\n" +
                "        }\n" +
                "    ]\n" +
                "}";
    }
}