package com.youxin.risk.gateway.test;

import com.youxin.risk.verify.service.impl.VerifyLibraryService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration("classpath:spring/spring-config.xml")
public class VerifyLibraryServiceTest {

    @Autowired
    private VerifyLibraryService verifyLibraryService;

    @Test
    public void sendKafka(){
        verifyLibraryService.sendLibraryOutB001("testUser");
    }

}
