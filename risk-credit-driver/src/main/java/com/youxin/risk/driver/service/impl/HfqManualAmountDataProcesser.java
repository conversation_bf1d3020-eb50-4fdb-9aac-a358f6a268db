/*
 * Copyright (C) 2019 Baidu, Inc. All Rights Reserved.
 */
package com.youxin.risk.driver.service.impl;

import java.util.HashMap;
import java.util.List;

import com.youxin.risk.commons.utils.*;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.mongo.OfflineBatchMongoDao;
import com.youxin.risk.commons.mongo.OfflineBatchRecordMongoDao;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.vo.OfflineBatchRecordVo;
import com.youxin.risk.commons.vo.OfflineBatchVo;
import com.youxin.risk.driver.common.CdConstant;
import com.youxin.risk.driver.interfaces.DataProcesser;
import com.youxin.risk.driver.utils.DateUtils;

//@Service()
public class HfqManualAmountDataProcesser implements DataProcesser {

    @Value("${gateway.crediting.url}")
    private String gatewayCreditingUrl;

    @Autowired
    private OfflineBatchMongoDao offlineBatchMongoDao;

    @Autowired
    private OfflineBatchRecordMongoDao offlineBatchRecordMongoDao;

    protected Logger logger = LoggerFactory.getLogger(getClass());

    @Override
    public void processData(Object data) {
        if (data == null) {
            return;
        }
        List<OfflineBatchVo> list = ( List<OfflineBatchVo>)data;
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        for (OfflineBatchVo batchVo : list) {
            try {
                handleBatch(batchVo);
            } catch (Exception e) {
                LoggerProxy.error("handleOfflineBatchError", logger, "handle offlineBatch error,batch={}",
                        JsonUtils.toJson(batchVo),e);
            }

        }
    }

    private void handleBatch(OfflineBatchVo batchVo) {
        LoggerProxy.info("handlerOfflineBatchRecord",logger,"start handle offlinebatch,batch={}",JsonUtils.toJson(batchVo));
        List<OfflineBatchRecordVo> records = this.offlineBatchRecordMongoDao.getRecords(batchVo.getBatchId(), CdConstant.OFF_LINE_PROCESSID_MANUAL);
        if (CollectionUtils.isEmpty(records)) {
            LoggerProxy.warn("offlineBatchRecordEmpty",logger,"offlineBatchRecord is empty,batch={}",JsonUtils.toJson(batchVo));
            this.offlineBatchMongoDao.update2Finished(batchVo);
            return;
        }

        Boolean isBatchFinished = true;
        for (OfflineBatchRecordVo recordVo : records) {
            if (!this.handleRecord(recordVo)) {
                isBatchFinished = false;
            }
            SystemUtil.threadSleep(100); //单次请求完成sleep 100ms,限流,降低gateway压力
        }

        if (isBatchFinished) {
            this.offlineBatchMongoDao.update2Finished(batchVo);
        }
        
    }


    private Boolean handleRecord(OfflineBatchRecordVo recordVo) {
        try {
            Integer recordId = recordVo.getRecordId();
            if (recordVo.getData() == null) {
                LoggerProxy.error("offlineRecordDataNull",logger,"offlineRecord data is null,recordId={}",recordId);
                return false;
            }
            String userKey = (String) recordVo.getData().get("userkey");
            if (StringUtils.isBlank(userKey)) {
                LoggerProxy.error("offlineRecordDataNull",logger,"offlineRecord userkey is blank,recordId={}",recordId);
                return false;
            }

            if (!this.callGateway(userKey, recordVo)) {
                return false;
            }

            this.offlineBatchRecordMongoDao.update2Success(recordVo);

            return true;
        } catch (Exception e) {
            LoggerProxy.error("offlineBatchRecordHandlerError",logger,"handle offlineBatchRecord error,record={}",JsonUtils.toJson(recordVo),e);
        }

       return false;
    }

    private Boolean callGateway(String userKey,OfflineBatchRecordVo recordVo) {
        try {
            JSONObject gwData = new JSONObject();
            gwData.put("type", "async");
            String requestId = this.getRequestId(userKey, recordVo);
            gwData.put("requestId", requestId);
            JSONObject gwMsg = new JSONObject();//拼gatewayjson

            // 组织调用gateway数据,参考现有贷中已结清
            gwMsg.put("eventCode", "haohuanManualAmount");
            gwMsg.put("userKey", userKey);
            gwMsg.put("sourceSystem", "HAO_HUAN");
            gwMsg.put("transId", requestId);

            gwData.put("message", gwMsg);
            HashMap<String, String> request = new HashMap<>();
            request.put("icode", "0100011005");
            request.put("data", gwData.toJSONString());
            String result = SyncHTTPRemoteAPI.post(gatewayCreditingUrl, request, 10000);
            LoggerProxy.info("postHfqManualAmountToGatewaySuccess", logger, "request={},resp={}",
                    JSONObject.toJSONString(request), result);
        } catch (Exception ex) {
            LoggerProxy.error("postHfqManualAmountToGatewayError", logger, "partnerUserId=" + userKey + ",recordId"+recordVo.getRecordId(), ex);
            return false;
        }

        return true;
    }

    private String getRequestId(String userKey,OfflineBatchRecordVo recordVo) {
        return GlobalUtil.getSessionId();
    }
}
