<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns="http://www.springframework.org/schema/beans"
       xmlns:task="http://www.springframework.org/schema/task"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
     http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
     http://www.springframework.org/schema/task
     http://www.springframework.org/schema/task/spring-task.xsd">

    <!-- 配置任务线性池 -->
    <task:executor id="executor" pool-size="3" />
    <task:scheduler id="scheduler" pool-size="3" />

    <!-- 启用annotation方式 -->
    <task:annotation-driven scheduler="scheduler" executor="executor" proxy-target-class="true" />

    <task:scheduled-tasks scheduler="scheduler">
        <task:scheduled ref="abstractChannelRequestWaitSubmitTask" method="doJob" fixed-delay="10000" fixed-rate="10000"/>
        <task:scheduled ref="abstractChannelRequestLockTimeoutTask" method="doJob" fixed-delay="60000" fixed-rate="60000"/>
        <task:scheduled ref="abstractChannelRequestFailedTask" method="doJob" fixed-delay="60000" fixed-rate="60000"/>
        <task:scheduled ref="abstractChannelRequestWaitCallbackTask" method="doJob" fixed-delay="60000" fixed-rate="60000"/>
        <task:scheduled ref="abstractChannelDelaySubmitTask" method="doJob" fixed-delay="60000" fixed-rate="10000"/>
        <task:scheduled ref="abstractChannelBlockWarnTask" method="doJob" fixed-delay="60000" fixed-rate="600000"/>
        <task:scheduled ref="channelRequestHeikaCallbackTask" method="doJob" fixed-delay="10000" fixed-rate="10000"/>
        <task:scheduled ref="channelRequestDeleteTask" method="doJob" fixed-delay="300000" fixed-rate="300000" />
        <task:scheduled ref="channelRequestAgencyDeleteTask" method="doJob" fixed-delay="300000" fixed-rate="300000" />
        <task:scheduled ref="channelRequestUndealTask" method="doJob" fixed-delay="30000" fixed-rate="30000" />
    </task:scheduled-tasks>
</beans>