package com.youxin.risk.channel.listener;

import com.youxin.risk.commons.utils.LoggerProxy;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.support.ProducerListener;

/**
 * <AUTHOR> @date 2018/9/28 11:01
 */
public class DpMessageProducerListener implements ProducerListener<String, String> {
    private static final Logger LOGGER = LoggerFactory.getLogger(DpMessageProducerListener.class);

//    @Resource
//    private DiTaskHandler diTaskHandler;

    @Override
    public void onSuccess(String topic, Integer partition, String key, String value, RecordMetadata recordMetadata) {
        LoggerProxy.info("send kafka data success", LOGGER, "key={},value={}", key, value);
    }

    @Override
    public void onError(String topic, Integer partition, String key, String value, Exception exception) {

    }

    @Override
    public boolean isInterestedInSuccess() {
        return true;
    }
}
