package com.youxin.risk.channel.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.youxin.risk.channel.constants.ChannelConstant;
import com.youxin.risk.channel.constants.ChannelRequestModelStatus;
import com.youxin.risk.channel.constants.FundPlatRequestType;
import com.youxin.risk.channel.service.builder.AgencyMessageBuilder;
import com.youxin.risk.channel.service.fundplatrequest.FundPlatRequestService;
import com.youxin.risk.commons.constants.RequestType;
import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.exception.RiskRuntimeException;
import com.youxin.risk.commons.model.ChannelRequestAgency;
import com.youxin.risk.commons.model.ChannelRequestModel;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.remote.http.api.sync.SyncHTTPRemoteAPI;
import com.youxin.risk.commons.service.channel.ChannelRequestAgencyService;
import com.youxin.risk.commons.service.channel.ChannelRequestModelService;
import com.youxin.risk.commons.utils.JacksonUtil;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.metrics.MetricsAPI;
import com.youxin.risk.metrics.enums.MetricsOpType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

@Service("submitToFundPlatService")
public class SubmitToFundPlatService implements FundPlatRequestService{

    private Logger logger = LoggerFactory.getLogger(SubmitToFundPlatService.class);

    @Resource(name = "agencyMessageBuilderProxy")
    private AgencyMessageBuilder agencyMessageBuilder;

    @Resource
    private ChannelRequestModelService channelRequestModelService;

    @Resource
    private ChannelRequestAgencyService channelRequestAgencyService;

    @Resource
    private SubmitToDPService submitToDPService;

    private static final Set<String> SUCCESS_STATUS_SET = Sets.newHashSet("0000");
    private static Map<String, String> FUND_URL_BYTYPE;
    static {
        FUND_URL_BYTYPE = new HashMap<>();
        FUND_URL_BYTYPE.put(FundPlatRequestType.submit.name(), "/fund/application/submit");
        FUND_URL_BYTYPE.put(FundPlatRequestType.audit.name(), "/fund/application/audit");
        FUND_URL_BYTYPE.put(FundPlatRequestType.upload_idcard_front.name(), "/fund/file/upload");
        FUND_URL_BYTYPE.put(FundPlatRequestType.upload_idcard_back.name(), "/fund/file/upload");
        FUND_URL_BYTYPE.put(FundPlatRequestType.upload_extra_image.name(), "/fund/file/upload");
    }

    @Override
    public void submit(ChannelRequestModel record) {

        long start = System.currentTimeMillis();

        LoggerProxy.info("submitToFundPlatTask", logger, "model=" + record);

        Event event = JacksonUtil.toObject(record.getRequestMessage(), Event.class);

        // 先查，如果存在ChannelRequestAgency记录，则证明为补偿任务触发，取原applicationNo字段
        ChannelRequestAgency log = channelRequestAgencyService.selectByAgencyCode(record.getRequestId(), event.getAgencyCode());
        String applicationNo = "";
        if (log != null) {
            applicationNo = log.getDpJobId();
        }
        log = buildLog(record, event.getAgencyCode(), applicationNo);
		// 初始化返回码为请求处理中
		RetCodeEnum ret = RetCodeEnum.PROCESSING;
		try {
			// 进件信息提交、身份证照片上传、审核接口（不可重入），需前置数据提交接口返回通过后，才可进行审核接口调用，全部成功更新任务状态
			String httpret = remote(log, event, FundPlatRequestType.submit);
			ret = parseRet(httpret, record, log, FundPlatRequestType.submit);
			if (RetCodeEnum.SUCCESS != ret) {
				LoggerProxy.warn("submitToFundPlatTaskLoanMsgFail", logger, "userKey={}, cost={}", record.getUserKey(),
						System.currentTimeMillis() - start);
				// 参数非法，单独打印日志，更改主表状态为failed，配单独报警
				if (RetCodeEnum.ILLEGAL_ARGUMENT == ret) {
					LoggerProxy.warn("submitToFundPlatInvalidParam", logger,
							"req to fundplat submit param invalid, response={},param={}", httpret,
							log.getRequestAgencyMessage());
					submitToDPService.updateStatus(record, ChannelRequestModelStatus.FAILED);
				}
				if(RetCodeEnum.SIGN_ERROR == ret){
                    LoggerProxy.warn("submitToFundPlatSignError", logger,
                            "req to fundplat submit Sign Error, response={},param={}", httpret,
                            log.getRequestAgencyMessage());
                    channelRequestModelService.updateFiled( Collections.singletonList(record.getRequestId()));
                }
                if(RetCodeEnum.USER_BANK_DIFF == ret){
                    LoggerProxy.warn("submitToFundPlatUserInfoError", logger,
                            "req to fundplat submit UserInfo and Bank info is ERROR Error, response={},param={}", httpret,
                            log.getRequestAgencyMessage());
                    channelRequestModelService.updateFiled( Collections.singletonList(record.getRequestId()));
                }
				return;
			}
			// 进件接口成功，继续进行后续流程 ，
			if (RequestType.transaction.name().equals(record.getRequestType())) {
				if (!uploadUserCert(log, event, record)) {
					LoggerProxy.warn("submitToFundPlatTaskCertMsgFail", logger, "userKey={}, cost={}",
							record.getUserKey(), System.currentTimeMillis() - start);
                    channelRequestModelService.updateFiled( Collections.singletonList(record.getRequestId()));
					return;
				}
			}
			ChannelRequestAgency auditLog = buildAuditLog(record, log);
			String httpAuditRet = remote(auditLog, event, FundPlatRequestType.audit);
			ret = parseRet(httpAuditRet, record, log, FundPlatRequestType.audit);
			if (RetCodeEnum.SUCCESS == ret) {
				channelRequestAgencyService.updateJodId(log.getRequestAgencyId(), log.getDpJobId());
				submitToDPService.updateStatus(record, ChannelRequestModelStatus.SUBMITTED);
			}
            //未绑卡，强制前进
            if(RetCodeEnum.NOT_BIND_CARD == ret){
                channelRequestModelService.updateFiled( Collections.singletonList(record.getRequestId()));
            }
            if(RetCodeEnum.USER_BANK_DIFF == ret){
                channelRequestModelService.updateFiled( Collections.singletonList(record.getRequestId()));
            }
			// failed状态暂不应用
		} finally {
			if (RetCodeEnum.PROCESSING != ret) {
				Map<String, String> tags = Maps.newHashMap();
				tags.put("retCode", ret.getValue());
				tags.put("agencyCode", log.getAgencyCode());
				MetricsAPI.point(ChannelConstant.REQUEST_FUNDPLAT_COST_TIME_POINT, tags,
                        System.currentTimeMillis() - start,true, MetricsOpType.timer);
			}
		}
        LoggerProxy.info("submitToFundPlatTaskEnd", logger, "cost={}", System.currentTimeMillis() - start);
    }

    @Override
    public Boolean checkIsInvalid(ChannelRequestModel record) {
        return false;
    }

    private boolean uploadUserCert(ChannelRequestAgency log, Event event, ChannelRequestModel record) {
        Boolean result = false;
        try {
            String httpCertFRet = remote(buildCertLog(record, log, FundPlatRequestType.upload_idcard_front), event, FundPlatRequestType.upload_idcard_front);
            RetCodeEnum certFRet = parseRet(httpCertFRet, record, log, FundPlatRequestType.upload_idcard_front);
            if (RetCodeEnum.SUCCESS == certFRet) {
                // 身份证正面上传成功继续上传反面
                String httpCertBRet = remote(buildCertLog(record, log, FundPlatRequestType.upload_idcard_back), event, FundPlatRequestType.upload_idcard_back);
                RetCodeEnum certBRet = parseRet(httpCertBRet, record, log, FundPlatRequestType.upload_idcard_back);
                if (RetCodeEnum.SUCCESS == certBRet) {
                    result = true;
                }
                // todo upload other image
                result = uploadUserExtraImages(record, log, event);
            }
        } catch (Exception e) {
            LoggerProxy.error("uploadUserCertFailed", logger, "uploan user cert photo failed, userKey:" + record.getUserKey(), e);
        }
        return result;
    }

    private Boolean uploadUserExtraImages(ChannelRequestModel record, ChannelRequestAgency log, Event event) {
        // get user extra images
        String dataInfo = event.getString("data");
        if (StringUtils.isEmpty(dataInfo)) {
            return true;
        }
        JSONObject dataJson = JSONObject.parseObject(dataInfo);
        JSONArray imageList = dataJson.getJSONArray("images");
        if (CollectionUtils.isEmpty(imageList)) {
            return true;
        }
        for (int i = 0; i < imageList.size(); i++) {
            JSONObject image = imageList.getJSONObject(i);
            JSONObject extraData = (JSONObject) log.getExtraData();
            extraData.put("extraImageType", image.getString("type"));
            extraData.put("extraImageUri", image.getString("uri"));
            extraData.put("extraImageSuffix", image.getString("suffix"));
            String httpCertRet = remote(buildCertLog(record, log, FundPlatRequestType.upload_extra_image), event, FundPlatRequestType.upload_extra_image);
            RetCodeEnum certRet = parseRet(httpCertRet, record, log, FundPlatRequestType.upload_extra_image);
            if (RetCodeEnum.SUCCESS != certRet) {
                return false;
            }
        }
        return true;
    }

    private ChannelRequestAgency buildCertLog(ChannelRequestModel record, ChannelRequestAgency log, FundPlatRequestType fundReqType) {
        if (FundPlatRequestType.upload_idcard_front == fundReqType) {
            dealAgencyReqType(log, FundPlatRequestType.upload_idcard_front);
            log.setRequestAgencyMessage(agencyMessageBuilder.build(record, log));
        } else if (FundPlatRequestType.upload_idcard_back == fundReqType) {
            dealAgencyReqType(log, FundPlatRequestType.upload_idcard_back);
            log.setRequestAgencyMessage(agencyMessageBuilder.build(record, log));
        } else if (FundPlatRequestType.upload_extra_image == fundReqType) {
            dealAgencyReqType(log, FundPlatRequestType.upload_extra_image);
            log.setRequestAgencyMessage(agencyMessageBuilder.build(record, log));
        }
        return log;
    }

    private ChannelRequestAgency buildAuditLog(ChannelRequestModel record, ChannelRequestAgency log) {
        dealAgencyReqType(log, FundPlatRequestType.audit);
        log.setRequestAgencyMessage(agencyMessageBuilder.build(record, log));
        // 本方法是可重入的，所以不用事务
        // 审核参数信息保存
        channelRequestAgencyService.insertOrUpdate(log);
        return log;
    }

    private String remote(ChannelRequestAgency log, Event event, FundPlatRequestType fundReqType) {
        String url = agencyMessageBuilder.getAgencyUrl(log.getAgencyCode());
        String relativeUrl = FUND_URL_BYTYPE.get(fundReqType.name());
        url = url + relativeUrl;
        String json = log.getRequestAgencyMessage();
        String httpret = SyncHTTPRemoteAPI.postJson(url, json, null, 60000, false);
        LoggerProxy.info("submitToFundPlatResponse", logger, "agencyCode={},response={},request={}",
                event.getAgencyCode(), httpret, log.getRequestAgencyMessage());
        return httpret;
    }

    private ChannelRequestAgency buildLog(ChannelRequestModel record, String agencyCode, String applicationNo) {
        ChannelRequestAgency log = new ChannelRequestAgency();
        log.setRequestId(record.getRequestId());
        log.setAgencyCode(agencyCode);
        log.setDpJobId(applicationNo);
        // extraData 增加当前执行step对应枚举值属性，组织相应参数信息
        dealAgencyReqType(log, FundPlatRequestType.submit);
        log.setRequestAgencyMessage(agencyMessageBuilder.build(record, log));
        if (StringUtils.isBlank(log.getRequestAgencyId())) {
            log.setRequestAgencyId(record.getRequestId() + "_" + log.getAgencyCode());
        }
        // 本方法是可重入的，所以不用事务
        if (StringUtils.isEmpty(applicationNo)) {
            channelRequestAgencyService.insertOrUpdate(log);
        }
        return log;
    }

    private void dealAgencyReqType(ChannelRequestAgency log, FundPlatRequestType fundPlatRequestType) {
        JSONObject extraData = null;
        if (log.getExtraData() != null) {
            extraData = (JSONObject) log.getExtraData();
        } else {
            extraData = new JSONObject();
            log.setExtraData(extraData);
        }
        extraData.put("fundPlatRequestType", fundPlatRequestType);
    }

    private RetCodeEnum parseRet(String httpret, ChannelRequestModel record, ChannelRequestAgency log, FundPlatRequestType fundPlatType) {
        JSONObject map = JSONObject.parseObject(httpret);
        if (MapUtils.isEmpty(map)) {
            record.setRemark("fundplat response is null");
            throw new RiskRuntimeException(RetCodeEnum.FAILED, "fundplat response is null");
        }
        String status = String.valueOf(map.getString("status"));
        // 资金平台返回成功
        // 审核接口返回状态需要根据状态码+关键字判断
        if (SUCCESS_STATUS_SET.contains(status)) {
            JSONObject dataObj = map.getJSONObject("data");
            if (dataObj != null && FundPlatRequestType.submit == fundPlatType) {
                log.setDpJobId(dataObj.getString("applicationNo"));
                channelRequestAgencyService.updateJodId(log.getRequestAgencyId(), log.getDpJobId());
            }
            return RetCodeEnum.SUCCESS;
        }
        // 其他码每家机构不一致，单独解析
        RetCodeEnum ret = agencyMessageBuilder.parseResponse(log, httpret);
        if (RetCodeEnum.SUCCESS == ret) {
            return ret;
        }
		record.setRemark("fundplatStatus=" + status);
        // check是否属于云信维护期间
		Boolean applyWaitFlag = false; // 进件审核调云信是否处于维护期标识
		if ("10121046".equals(status) && RequestType.apply.name().equals(record.getRequestType())) {
			applyWaitFlag = true;
		}
		// 进件阶段，提交类异常不再重试
		if (FundPlatRequestType.submit == fundPlatType && ("********".equals(status) || applyWaitFlag)) {
			return RetCodeEnum.ILLEGAL_ARGUMENT;
		}
		LoggerProxy.error("submitToFundPlatTaskFailed", logger, "status=" + status);
		//用户未绑卡，强制前进
        if(ret == RetCodeEnum.NOT_BIND_CARD || ret == RetCodeEnum.SIGN_ERROR || ret == RetCodeEnum.USER_BANK_DIFF){
            return ret;
        }
        return RetCodeEnum.FAILED;
    }

}
