package com.youxin.risk.channel.service.impl;

import com.youxin.risk.channel.service.SubmitDcDataService;
import com.youxin.risk.channel.service.builder.DcPushDataMsgSender;
import com.youxin.risk.commons.model.datacenter.vo.DcSubmitKafkaDataVo;
import com.youxin.risk.commons.utils.LoggerProxy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.List;


public class SubmitDcDataServiceImpl implements SubmitDcDataService {

    private Logger logger = LoggerFactory.getLogger(SubmitDcDataServiceImpl.class);
    private List<DcPushDataMsgSender> dcPushDataMsgSenders;

    public List<DcPushDataMsgSender> getDcPushDataMsgSenders() {
        return dcPushDataMsgSenders;
    }
    public void setDcPushDataMsgSenders(List<DcPushDataMsgSender> dcPushDataMsgSenders) {
        this.dcPushDataMsgSenders = dcPushDataMsgSenders;
    }

    @Override
    public void submit(List<DcSubmitKafkaDataVo> dcSubmitKafkaDataVos) throws Exception{
        if(dcSubmitKafkaDataVos == null || dcSubmitKafkaDataVos.size() == 0){
            return;
        }
        long start = System.currentTimeMillis();
        DcPushDataMsgSender dcPushDataMsgSender = null;
        for(DcSubmitKafkaDataVo dcSubmitKafkaDataVo : dcSubmitKafkaDataVos){
            for(DcPushDataMsgSender sender : dcPushDataMsgSenders){
                if(sender.isWatchedDataType(dcSubmitKafkaDataVo.getSubmitDataType())){
                    dcPushDataMsgSender = sender;
                    String postJsonStr = dcPushDataMsgSender.buildPostJsonStr(dcSubmitKafkaDataVo);
                    if("{}".equals(postJsonStr)){//推phonebook、plist等列表数据如果是空则不推
                        LoggerProxy.info("buildPostJsonStrEmptyJson", logger, "dcSubmitKafkaDataVo={}", dcSubmitKafkaDataVo);
                        return;
                    }
//                    boolean httpResultFlag = dcPushDataMsgSender.getHttpResult(postJsonStr);
                }
            }
        }
        LoggerProxy.info("submitDataToThirdPartyEnd", logger, "cost={}", System.currentTimeMillis() - start);
    }
}