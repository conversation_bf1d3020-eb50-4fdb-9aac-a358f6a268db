package com.youxin.risk.metrics.dpc.springmvc.task;

import com.youxin.risk.commons.apollo.ApolloClientAdapter;
import com.youxin.risk.commons.apollo.ApolloNamespaceEnum;
import com.youxin.risk.commons.utils.LogUtil;
import com.youxin.risk.metrics.constants.Constent;
//import com.youxin.risk.metrics.dpc.influxdb.InfluxDBAPI;
import com.youxin.risk.metrics.dpc.springmvc.service.InfluxDBService;
import com.youxin.risk.metrics.enums.MetricsOpType;
import com.youxin.risk.metrics.helpers.LoggerUtils;
import com.youxin.risk.metrics.helpers.ThreadUtils;
import com.youxin.risk.metrics.model.PointModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;


/**
 * 手动聚合point的任务
 */
@Component
public class InfluxReporter implements InitializingBean {
    private static final Logger logger = LoggerFactory.getLogger(InfluxReporter.class);

    private ScheduledExecutorService scheduledExecutor;

    @Autowired
    private InfluxDBService influxDBService;

    public void init() {
        scheduledExecutor = Executors.newScheduledThreadPool(1,
                ThreadUtils.defaultThreadFactory(InfluxReporter.class.getSimpleName() + "-poll"));
        int interval = 30;
        scheduledExecutor.scheduleWithFixedDelay(new FixedDelayTask(), interval, interval, TimeUnit.SECONDS);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        init();
    }

    private class FixedDelayTask implements Runnable {

        @Override
        public void run() {
            try {
                doJob();
            } catch (Throwable t) {
                LoggerUtils.report("###FixedDelayTask exe error", t);
            }
        }


        private void doJob() {
            Collection<PointModel> cacheList = new ArrayList<>(BaseTask.getCacheData());
            LogUtil.buildAndBindLog();

            logger.info("###start write to influxdb, cache size={}", cacheList.size());
            // 目前线上有27个数据库 故设置为64
            Map<String, List<PointModel>> databaseAndPointModelsMap = new HashMap<>(64);
            for (PointModel pointModel : cacheList) {

                long count = pointModel.getCount().getAndSet(0);
                if (MetricsOpType.count.name().equals(pointModel.getOpType())) {
                    pointModel.getValue().put(Constent.INFLUXDB_VALUE_FIELD, count);
                } else if (MetricsOpType.timer.name().equals(pointModel.getOpType())) {
                    long sum = pointModel.getTimeSum().getAndSet(0);
                    long mean = 0;
                    if (count != 0) {
                        mean = sum / count;
                    }
                    pointModel.getValue().put(Constent.INFLUXDB_VALUE_FIELD, mean);
                }
                pointModel.getValue().put(Constent.INFLUXDB_COUNT_FIELD, count);
                pointModel.initTime();
                if (MetricsOpType.timer.name().equals(pointModel.getOpType())
                        && count == 0) {
                    continue;
                }

                //改为批量打点
                databaseAndPointModelsMap.computeIfAbsent(pointModel.getNamespace(), key -> new ArrayList<>()).add(pointModel);
            }

            influxDBService.batchWrite(databaseAndPointModelsMap);
            LogUtil.unbindLogId();
        }

    }
}
