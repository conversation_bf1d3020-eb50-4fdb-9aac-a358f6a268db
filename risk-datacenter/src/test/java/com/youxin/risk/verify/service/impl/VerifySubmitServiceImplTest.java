package com.youxin.risk.verify.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.verify.vo.VerifySubmitPlistVo;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * @Desc 限流测试
 * @Auth linchongbin
 * @Date 2022/5/13 11:34
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration("classpath:spring/spring-config.xml")
public class VerifySubmitServiceImplTest {

    @Autowired
    private VerifySubmitServiceImpl verifySubmitService;

    @Test
    public void testLimit() throws InstantiationException, IllegalAccessException {
        String paramStr = "{\"appVersion\":\"820\",\"jailBroken\":0,\"os\":\"Android\",\"sourceSystem\":\"HAO_HUAN\",\"ip\":\"**************\",\"channel\":\"huawei\",\"deviceId\":\"********-7b5b-8e14-ffff-ffffef05ac4a-070a0139d6ebd905\",\"platform\":\"Android\",\"userKey\":\"e74a16d6ec05bf24b2a6f2555287a93c\",\"plist\":[{\"launchTime\":*************,\"appName\":\"还呗\",\"packageName\":\"com.shuhekeji\",\"lastUpdateTime\":*************},{\"launchTime\":*************,\"appName\":\"爱奇艺\",\"packageName\":\"com.qiyi.video\",\"lastUpdateTime\":*************},{\"launchTime\":*************,\"appName\":\"焦作智慧金服\",\"packageName\":\"com.iflybank.jz\",\"lastUpdateTime\":*************},{\"launchTime\":*************,\"appName\":\"中国农业银行\",\"packageName\":\"com.android.bankabc\",\"lastUpdateTime\":*************},{\"launchTime\":*************,\"appName\":\"交管12123\",\"packageName\":\"com.tmri.app.main\",\"lastUpdateTime\":*************},{\"launchTime\":*************,\"appName\":\"QQ音乐\",\"packageName\":\"com.tencent.qqmusic\",\"lastUpdateTime\":*************},{\"launchTime\":*************,\"appName\":\"剪映\",\"packageName\":\"com.lemon.lv\",\"lastUpdateTime\":*************},{\"launchTime\":*************,\"appName\":\"QQ邮箱\",\"packageName\":\"com.tencent.androidqqmail\",\"lastUpdateTime\":*************},{\"launchTime\":*************,\"appName\":\"易收付\",\"packageName\":\"com.yeahka.yishoufu\",\"lastUpdateTime\":*************},{\"launchTime\":*************,\"appName\":\"河南农信\",\"packageName\":\"com.hnnx.pmbank\",\"lastUpdateTime\":*************},{\"launchTime\":*************,\"appName\":\"微信\",\"packageName\":\"com.tencent.mm\",\"lastUpdateTime\":*************},{\"launchTime\":*************,\"appName\":\"洋钱罐借款\",\"packageName\":\"com.lingyue.zebraloan\",\"lastUpdateTime\":*************},{\"launchTime\":*************,\"appName\":\"应用宝\",\"packageName\":\"com.tencent.android.qqdownloader\",\"lastUpdateTime\":*************},{\"launchTime\":*************,\"appName\":\"百度\",\"packageName\":\"com.baidu.searchbox\",\"lastUpdateTime\":*************},{\"launchTime\":*************,\"appName\":\"QQ同步助手\",\"packageName\":\"com.tencent.qqpim\",\"lastUpdateTime\":*************},{\"launchTime\":*************,\"appName\":\"58同城\",\"packageName\":\"com.wuba\",\"lastUpdateTime\":*************},{\"launchTime\":*************,\"appName\":\"拍拍贷借款\",\"packageName\":\"com.ppdai.loan\",\"lastUpdateTime\":*************},{\"launchTime\":*************,\"appName\":\"平安金管家\",\"packageName\":\"com.pingan.lifeinsurance\",\"lastUpdateTime\":1651277116175},{\"launchTime\":1609263747369,\"appName\":\"腾讯视频\",\"packageName\":\"com.tencent.qqlive\",\"lastUpdateTime\":1651916035484},{\"launchTime\":*************,\"appName\":\"QQ浏览器\",\"packageName\":\"com.tencent.mtt\",\"lastUpdateTime\":1651996794092},{\"launchTime\":*************,\"appName\":\"拼多多\",\"packageName\":\"com.xunmeng.pinduoduo\",\"lastUpdateTime\":1651277141932},{\"launchTime\":*************,\"appName\":\"今日头条\",\"packageName\":\"com.ss.android.article.news\",\"lastUpdateTime\":1651277227150},{\"launchTime\":1652353008711,\"appName\":\"好分期\",\"packageName\":\"com.renrendai.haohuan\",\"lastUpdateTime\":1652353008711},{\"launchTime\":*************,\"appName\":\"抖音\",\"packageName\":\"com.ss.android.ugc.aweme\",\"lastUpdateTime\":16***********},{\"launchTime\":*************,\"appName\":\"百度文库\",\"packageName\":\"com.baidu.wenku\",\"lastUpdateTime\":*************},{\"launchTime\":*************,\"appName\":\"快手极速版\",\"packageName\":\"com.kuaishou.nebula\",\"lastUpdateTime\":*************},{\"launchTime\":*************,\"appName\":\"WiFi万能钥匙\",\"packageName\":\"com.snda.wifilocating\",\"lastUpdateTime\":*************},{\"launchTime\":*************,\"appName\":\"江苏农商银行\",\"packageName\":\"com.yitong.mbank\",\"lastUpdateTime\":*************},{\"launchTime\":*************,\"appName\":\"百度地图\",\"packageName\":\"com.baidu.BaiduMap\",\"lastUpdateTime\":*************},{\"launchTime\":*************,\"appName\":\"中国建设银行\",\"packageName\":\"com.chinamworld.main\",\"lastUpdateTime\":*************},{\"launchTime\":*************,\"appName\":\"番茄免费小说\",\"packageName\":\"com.dragon.read\",\"lastUpdateTime\":*************},{\"launchTime\":*************,\"appName\":\"河南掌上登记\",\"packageName\":\"com.topsoft.qcdzhapp.ha\",\"lastUpdateTime\":*************},{\"launchTime\":*************,\"appName\":\"腾讯新闻\",\"packageName\":\"com.tencent.news\",\"lastUpdateTime\":*************},{\"launchTime\":*************,\"appName\":\"KY FPV\",\"packageName\":\"com.cooingdv.kyfpv\",\"lastUpdateTime\":*************},{\"launchTime\":*************,\"appName\":\"京东\",\"packageName\":\"com.jingdong.app.mall\",\"lastUpdateTime\":*************},{\"launchTime\":*************,\"appName\":\"全民K歌\",\"packageName\":\"com.tencent.karaoke\",\"lastUpdateTime\":*************},{\"launchTime\":*************,\"appName\":\"高德地图\",\"packageName\":\"com.autonavi.minimap\",\"lastUpdateTime\":*************},{\"launchTime\":*************,\"appName\":\"和家亲\",\"packageName\":\"com.cmri.universalapp\",\"lastUpdateTime\":*************},{\"launchTime\":*************,\"appName\":\"建行企业银行\",\"packageName\":\"com.ccb.companybank\",\"lastUpdateTime\":*************},{\"launchTime\":*************,\"appName\":\"快手\",\"packageName\":\"com.smile.gifmaker\",\"lastUpdateTime\":*************},{\"launchTime\":*************,\"appName\":\"好看视频\",\"packageName\":\"com.baidu.haokan\",\"lastUpdateTime\":*************},{\"launchTime\":*************,\"appName\":\"淘宝\",\"packageName\":\"com.taobao.taobao\",\"lastUpdateTime\":*************},{\"launchTime\":*************,\"appName\":\"支付宝\",\"packageName\":\"com.eg.android.AlipayGphone\",\"lastUpdateTime\":*************},{\"launchTime\":*************,\"appName\":\"WPS Office\",\"packageName\":\"cn.wps.moffice_eng\",\"lastUpdateTime\":*************},{\"launchTime\":*************,\"appName\":\"唯品会\",\"packageName\":\"com.achievo.vipshop\",\"lastUpdateTime\":*************}],\"isAuthorized\":2,\"osVersion\":\"1\",\"mobileModel\":\"FRL-AN00a\",\"isCopyPackage\":0,\"device\":\"FRL-AN00a\",\"channelCode\":\"mojitianqi\"}";
        JSONObject param = JSONObject.parseObject(paramStr);
        VerifySubmitPlistVo plistVo = JSON.toJavaObject(param, VerifySubmitPlistVo.class);
        verifySubmitService.submitPlist(plistVo);
    }
}
