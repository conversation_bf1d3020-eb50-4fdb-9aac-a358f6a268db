package com.youxin.risk.channel.test;

import com.alibaba.fastjson.JSON;
import com.youxin.risk.commons.model.verify.VerifyUserLineManagement;
import com.youxin.risk.datacenter.pojo.UserLineManagementHistory;
import com.youxin.risk.verify.service.impl.VerifyUserLineManagementService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration("classpath:spring/spring-config.xml")
public class ObjectCopyTest {

    @Autowired
    private VerifyUserLineManagementService userLineManagementService;

    public static final BeanCopier beanCopier = BeanCopier.create(VerifyUserLineManagement.class,UserLineManagementHistory.class,false);

    @Test
    public void test() {
        VerifyUserLineManagement oldUserLine = userLineManagementService.getByUserKey("HAO_HUAN", "ec045adeb8f1c77984bc4abadaa7d23");
        oldUserLine.setRemark("ceshi");
        System.out.println("old:" + JSON.toJSONString(oldUserLine));
        //对象赋值
        UserLineManagementHistory userLineManagementHistory = new UserLineManagementHistory();
        beanCopier.copy(oldUserLine, userLineManagementHistory,null);
        System.out.println("new:" + JSON.toJSONString(userLineManagementHistory));
        oldUserLine.setRemark("sunxin");
        System.out.println("after change:" + userLineManagementHistory.getRemark());

    }

}
