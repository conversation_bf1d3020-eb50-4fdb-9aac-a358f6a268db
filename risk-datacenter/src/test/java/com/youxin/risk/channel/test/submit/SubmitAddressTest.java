package com.youxin.risk.channel.test.submit;

import com.alibaba.fastjson.JSON;
import com.youxin.risk.ra.service.SubmitAddressService;
import com.youxin.risk.ra.service.SubmitBankCardService;
import com.youxin.risk.ra.service.SubmitJobService;
import com.youxin.risk.ra.vo.RaSubmitAddressVo;
import com.youxin.risk.ra.vo.RaSubmitBankCardVo;
import com.youxin.risk.ra.vo.RaSubmitJobVo;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;


@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration("classpath:spring/spring-config.xml")
public class SubmitAddressTest {

    @Autowired
    private SubmitAddressService submitAddressService;

    @Autowired
    private SubmitJobService raSubmitJobService;

    @Autowired
    private SubmitBankCardService submitBankCardService;

    @Autowired
    private com.youxin.risk.datacenter.service.SubmitAddressService dcSubmitAddressService;

    @Autowired
    private com.youxin.risk.datacenter.service.SubmitJobService dcSubmitJobService;

    @Autowired
    private com.youxin.risk.datacenter.service.SubmitBankCardService dcSubmitBankCardService;

    @Test
    public void testSubmitAddress() {
        RaSubmitAddressVo raSubmitAddressVo = submitAddressService.getSubmitAddressVoByUserKey("HAO_HUAN", "b33c0d0148916b3608c90b9079d4f");
        RaSubmitAddressVo dcSubmitAddressVo = dcSubmitAddressService.getRaSubmitAddressVoByUserKey("b33c0d0148916b3608c90b9079d4f", "HAO_HUAN", null);
        System.out.println(JSON.toJSONString(raSubmitAddressVo));
        System.out.println(JSON.toJSONString(dcSubmitAddressVo));
    }

    @Test
    public void testSubmitJob() {
        RaSubmitJobVo raSubmitJobVo = raSubmitJobService.getLastSubmitJobVoByUserKey("HAO_HUAN", "b33c0d0148916b3608c90b9079d4f");
        RaSubmitJobVo dcSubmitJobVo = dcSubmitJobService.getRaSubmitJobVoByUserKeyAndSourceSystem("HAO_HUAN", "b33c0d0148916b3608c90b9079d4f", null);
        System.out.println(JSON.toJSONString(raSubmitJobVo));
        System.out.println(JSON.toJSONString(dcSubmitJobVo));
    }


    @Test
    public void testSubmitBankCard() {
        RaSubmitBankCardVo raSubmitBankCardVo = submitBankCardService.getSubmitBankCardByUserKey("1ac265f2f5559c781fbc36f1c3aca83b");
        RaSubmitBankCardVo dcSubmitBankCardVo = dcSubmitBankCardService.getSubmitBankCardByUserKey("HAO_HUAN", "1ac265f2f5559c781fbc36f1c3aca83b", null);
        System.out.println(JSON.toJSONString(raSubmitBankCardVo));
        System.out.println(JSON.toJSONString(dcSubmitBankCardVo));
    }


}
