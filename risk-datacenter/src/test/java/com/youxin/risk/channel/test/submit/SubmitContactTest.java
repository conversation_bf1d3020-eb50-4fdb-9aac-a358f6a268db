package com.youxin.risk.channel.test.submit;

import com.alibaba.fastjson.JSON;
import com.youxin.risk.commons.dao.datacenter.DcSubmitContactInfoMapper;
import com.youxin.risk.commons.model.datacenter.DcSubmitContactInfo;
import com.youxin.risk.ra.service.impl.SubmitContactInfoServiceImpl;
import com.youxin.risk.ra.vo.RaSubmitContactDetailsVo;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;


@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration("classpath:spring/spring-config.xml")
public class SubmitContactTest {

    @Autowired
    private DcSubmitContactInfoMapper dcSubmitContactInfoMapper;

    @Autowired
    public SubmitContactInfoServiceImpl raSubmitContactInfoService;

    @Test
    public void testSubmitContact() {
        List<DcSubmitContactInfo> twoContactsByUserKey = dcSubmitContactInfoMapper.getTwoContactsByUserKey("bdfa81ebe7ac70f51ab68ed89904b920");
        System.out.println(JSON.toJSONString(twoContactsByUserKey));
    }

    @Test
    public void testSubmitContact2() {
        List<RaSubmitContactDetailsVo> submitContactListByUserApply = raSubmitContactInfoService
                .getDcSubmitContactListByUserKey("bdfa81ebe7ac70f51ab68ed89904b920");
    }
}
