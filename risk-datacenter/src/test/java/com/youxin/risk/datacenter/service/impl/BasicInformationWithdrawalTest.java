package com.youxin.risk.datacenter.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.weicai.caesar.CaesarUtil;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.datacenter.mapper.DcSubmitAmountCreditCardMapper;
import com.youxin.risk.datacenter.mapper.DcSubmitAmountEducationMapper;
import com.youxin.risk.datacenter.mapper.DcSubmitAmountHouseMapper;
import com.youxin.risk.datacenter.mapper.DcSubmitAmountJobMapper;
import com.youxin.risk.datacenter.mapper.DcSubmitAmountMarriageMapper;
import com.youxin.risk.datacenter.mapper.DcSubmitAmountVehicleMapper;
import com.youxin.risk.datacenter.model.DcSubmitAmountCreditCard;
import com.youxin.risk.datacenter.model.DcSubmitAmountEducation;
import com.youxin.risk.datacenter.model.DcSubmitAmountHouse;
import com.youxin.risk.datacenter.model.DcSubmitAmountJob;
import com.youxin.risk.datacenter.model.DcSubmitAmountMarriage;
import com.youxin.risk.datacenter.model.DcSubmitAmountVehicle;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Date;

/**
 * @description:
 * @author: juxiang
 * @create: 2021-09-27 14:19
 **/
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration("classpath:spring/spring-config.xml")
public class BasicInformationWithdrawalTest {
    @Autowired
    DcSubmitAmountCreditCardMapper dcSubmitAmountCreditCardMapper;
    @Autowired
    DcSubmitAmountHouseMapper dcSubmitAmountHouseMapper;

    @Autowired
    DcSubmitAmountJobMapper dcSubmitAmountJobMapper;

    @Autowired
    DcSubmitAmountVehicleMapper dcSubmitAmountVehicleMapper;

    @Autowired
    DcSubmitAmountMarriageMapper marriageMapper;
    @Autowired
    DcSubmitAmountEducationMapper dcSubmitAmountEducationMapper;

    @Test
    public void makeData(){
        for (int i=0;i<5;i++){
            DcSubmitAmountCreditCard dcSubmitAmountCreditCard=new DcSubmitAmountCreditCard();
            dcSubmitAmountCreditCard.setUserName(CaesarUtil.encode("张三"));
            dcSubmitAmountCreditCard.setIdcardNumber(CaesarUtil.encode("185965324564"));
            dcSubmitAmountCreditCard.setCardNo(CaesarUtil.encode("370785796652134585"));
            dcSubmitAmountCreditCard.setReservedMobile(CaesarUtil.encode("17667198653"));
            dcSubmitAmountCreditCard.setUserKey("testUser");
            dcSubmitAmountCreditCard.setOperationLogId(156498432454L);
            dcSubmitAmountCreditCard.setCreateTime(new Date());
            dcSubmitAmountCreditCard.setUpdateTime(new Date());
            dcSubmitAmountCreditCardMapper.insert(dcSubmitAmountCreditCard);
            DcSubmitAmountHouse dcSubmitAmountHouse=new DcSubmitAmountHouse();
            dcSubmitAmountHouse.setLivingAddress("北京市XX区XX街道XX小区X栋X楼X0X室");
            dcSubmitAmountHouse.setLivingCity("北京市");
            dcSubmitAmountHouse.setLivingType("居民楼");
            dcSubmitAmountHouse.setUserKey("testUser");
            dcSubmitAmountHouse.setOperationLogId(156498432454L);
            dcSubmitAmountHouse.setCreateTime(new Date());
            dcSubmitAmountHouse.setUpdateTime(new Date());
            dcSubmitAmountHouseMapper.insert(dcSubmitAmountHouse);
            DcSubmitAmountJob dcSubmitAmountJob=new DcSubmitAmountJob();
            dcSubmitAmountJob.setIndustry("制造业");
            dcSubmitAmountJob.setCompanyPosition("beijignshi");
            dcSubmitAmountJob.setSalary("10000000");
            dcSubmitAmountJob.setWorkYear("5");
            dcSubmitAmountJob.setCompanyName("XXXX有限公司");
            dcSubmitAmountJob.setCompanyPhone("*********");
            dcSubmitAmountJob.setCompanyAddress("XXXXX街道XXXXX区");
            dcSubmitAmountJob.setLevel("5level");
            dcSubmitAmountJob.setUserKey("testUser");
            dcSubmitAmountJob.setOperationLogId(156498432454L);
            dcSubmitAmountJob.setCreateTime(new Date());
            dcSubmitAmountJob.setUpdateTime(new Date());
            dcSubmitAmountJobMapper.insert(dcSubmitAmountJob);
            DcSubmitAmountVehicle dcSubmitAmountVehicle=new DcSubmitAmountVehicle();
            dcSubmitAmountVehicle.setBrand("fengtian");
            dcSubmitAmountVehicle.setAmount("178900");
            dcSubmitAmountVehicle.setOwner("zhangsan");
            dcSubmitAmountVehicle.setNumber("453699");
            dcSubmitAmountVehicle.setLoanMonth("5");
            dcSubmitAmountVehicle.setUserKey("testUser");
            dcSubmitAmountVehicle.setOperationLogId(156498432454L);
            dcSubmitAmountVehicle.setCreateTime(new Date());
            dcSubmitAmountVehicle.setUpdateTime(new Date());
            dcSubmitAmountVehicleMapper.insert(dcSubmitAmountVehicle);
            DcSubmitAmountMarriage dcSubmitAmountMarriage=new DcSubmitAmountMarriage();
            dcSubmitAmountMarriage.setMaritalStatus("已婚");
            dcSubmitAmountMarriage.setUserKey("testUser");
            dcSubmitAmountMarriage.setOperationLogId(156498432454L);
            dcSubmitAmountMarriage.setCreateTime(new Date());
            dcSubmitAmountMarriage.setUpdateTime(new Date());
            marriageMapper.insert(dcSubmitAmountMarriage);
            DcSubmitAmountEducation education=new DcSubmitAmountEducation();
            education.setUserKey("testUser");
            education.setGraduateYear("1996");
            education.setHighestEducationLevel("博士");
            education.setHighestDegree("博士");
            education.setUniversity("清华大学");
            education.setOperationLogId(156498432454L);
            education.setCreateTime(new Date());
            education.setUpdateTime(new Date());
            dcSubmitAmountEducationMapper.insert(education);
        }
    }
}
