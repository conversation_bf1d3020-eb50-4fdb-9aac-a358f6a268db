package com.youxin.risk.datacenter.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.dao.datacenter.DcSubmitPlistShardingMapper;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.model.datacenter.DcSubmitPlist;
import com.youxin.risk.commons.service.engine.EventService;
import com.youxin.risk.commons.service.verify.VerifyResultService;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.StringUtils;
import com.youxin.risk.commons.vo.EventVo;
import com.youxin.risk.datacenter.service.search.DataQueryService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * <p>
 * 获取用户最近一次特定事件审核结果
 */
@Service
public class LastPointEventVerifyResultServiceImpl implements DataQueryService<Map<String, Object>> {

    Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    DcSubmitPlistShardingMapper dcSubmitPlistShardingMapper;

    @Autowired
    EventService eventService;

    @Autowired
    VerifyResultService verifyResultService;


    @Override
    public String getServiceCode() {
        return "getLastPointEventVerifyResultService";
    }

    /**
     * 从  MongoDB 表查询自有数据
     *
     * @param param
     * @return
     */
    @Override
    public Map<String, Object> queryDcData(JSONObject param) {
        String userKey = param.getString("userKey");
        String sourceSystem = param.getString("sourceSystem");
        String sourceEventCode = param.getString("eventCode");

//        List<EventVo> templateEventVoList = eventService.getEventsByUserKey(userKey, sourceSystem, sourceEventCode, TEMPLATE_SOURCE);
        List<EventVo> templateEventVoList = verifyResultService.getEventsByUserKey(userKey, sourceSystem, sourceEventCode);
        LoggerProxy.info("LastPointEventVerifyResultServiceImpl_templateEventVoList",logger,"userKey={}, eventCode={} size={}",userKey,sourceEventCode,templateEventVoList.size());
        if (CollectionUtils.isNotEmpty(templateEventVoList)){
            Map<String, Object> verifyResult = getVerifyResult(sourceEventCode, sourceSystem, templateEventVoList);
            if(verifyResult != null){
                return verifyResult;
            }
        }
        return null;
//        List<EventVo> eventVoList = eventService.getEventsByUserKey(userKey, sourceSystem, sourceEventCode,SHARDING_TEMPLATE_SOURCE);
//        LoggerProxy.info("lastVerifyResult", logger, "userKey={}, eventCode={},size = {}", userKey, sourceEventCode,eventVoList.size());
//        if (CollectionUtils.isEmpty(eventVoList)) {
//            LoggerProxy.warn("lastVerifyResultIsEmpty", logger, "userKey={}, eventCode={}", userKey, sourceEventCode);
//            return null;
//        }
//
//        Map<String, Object> verifyResult = getVerifyResult(sourceEventCode, sourceSystem, eventVoList);
//        return verifyResult !=null?verifyResult:null;
    }

    private Map<String, Object> getVerifyResult(String sourceEventCode,String sourceSystem,List<EventVo> eventVoList){
        if (!CollectionUtils.isEmpty(eventVoList)){
            for (EventVo eventVo : eventVoList) {
                Event event = eventVo.getEvent();
                String eventCode = event.getEventCode();
                if (sourceEventCode.equals(eventCode)) {
                    Map<String, Object> verifyResult = event.getVerifyResult();
                    if (verifyResult == null) {
                        continue;
                    }

                    Map<String, Object> result = verifyResult;

                    String originResult = (String) verifyResult.get("originResult");
                    if (StringUtils.isNotEmpty(originResult)) {
                        result = JSONObject.parseObject(originResult);
                    }
                    if (!"REN_REN_DAI".equals(sourceSystem)) {
                        result.put("eventCreateTime", eventVo.getCreateTime());
                    }
                    return result;
                }
            }
        }
        return null;
    }
}
