package com.youxin.risk.datacenter.service.search;

import com.youxin.risk.commons.service.verify.VerifyResultService;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.datacenter.client.GatewayClient;
import com.youxin.risk.datacenter.pojo.VerifyDataSubscribeVo;
import com.youxin.risk.datacenter.service.search.annotation.DcServiceCode;
import com.youxin.risk.datacenter.service.subscribe.SubscribeServiceHandler;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @description: 最新审核结果
 * @author: juxiang
 * @create: 2023-11-21 18:40
 **/
@Service
public class VerifyDataService implements DcQueryService<Map<String,Object>>{
    public static final String LATEST_VERIFY_RESULT="LATEST_VERIFY_RESULT";
    public static final String EVENT_EXITING="EVENT_EXITING";
    public static final String USER_QUOTA="USER_QUOTA";
    Logger logger = LoggerFactory.getLogger(getClass());
    @Autowired
    VerifyResultService verifyResultService;
    @Autowired
    GatewayClient gatewayClient;
    @Autowired
    private SubscribeServiceHandler subscribeServiceHandler;

    @DcServiceCode(name = LATEST_VERIFY_RESULT)
    public Map<String,Object> getLatestVerifyResultByLoanKey(Map<String, Object> params){
        String loanKey = String.valueOf(params.get("loanKey"));
        if(!this.isEventStatusIsFinal(loanKey)){
            this.subscribeIfNeed(params,LATEST_VERIFY_RESULT,MapUtils.EMPTY_MAP);
            return MapUtils.EMPTY_MAP;
        }
        Map<String, Object> result = verifyResultService.getVerifyResultByLoanKeySimple(loanKey);
        this.subscribeIfNeed(params,LATEST_VERIFY_RESULT,result);
        return result;
    }
    @DcServiceCode(name = EVENT_EXITING)
    public Map<String,Object> eventExitingByLoanKey(Map<String, Object> params){
        String loanKey = String.valueOf(params.get("loanKey"));
        Map<String,Object> result = gatewayClient.eventExitingByLoanKey(loanKey);
        this.subscribeIfNeed(params,EVENT_EXITING,result);
        return result;
    }

    @DcServiceCode(name = USER_QUOTA)
    public Map<String,Object> userQuotaByLoanKey(Map<String, Object> params){
        String loanKey = String.valueOf(params.get("loanKey"));
        if(!this.isEventStatusIsFinal(loanKey)){
            this.subscribeIfNeed(params,USER_QUOTA,MapUtils.EMPTY_MAP);
            return MapUtils.EMPTY_MAP;
        }
        Map<String,Object> result = verifyResultService.queryQuotaDataByLoanKey(loanKey);
        this.subscribeIfNeed(params,USER_QUOTA,result);
        return result;
    }

    private void subscribeIfNeed(Map<String, Object> params, String dataType, Map<String, Object> result) {
        if(MapUtils.isEmpty(result)){
            // 落表并记录
            String loanKey = String.valueOf(params.get("loanKey"));
            VerifyDataSubscribeVo verifyDataSubscribeVo=new VerifyDataSubscribeVo();
            verifyDataSubscribeVo.setDataType(dataType);
            verifyDataSubscribeVo.setLoanKey(String.valueOf(params.get("loanKey")));
            verifyDataSubscribeVo.setRequestId(String.valueOf(params.get("requestId")));
            verifyDataSubscribeVo.setUserKey(String.valueOf(params.get("userKey")));
            verifyDataSubscribeVo.setChannel(String.valueOf(params.get("channel")));
            subscribeServiceHandler.getSubscribeService(dataType).subscribe(verifyDataSubscribeVo);
            LoggerProxy.info(logger,"can not find {} by this loanKey:{}",dataType,loanKey);
        }
    }

    /**
     * 策略节点在每个步骤都会更新数据，为了防止获取的不是最新数据，所以请求下gateway查看是否已经回调
     * @param loanKey
     * @return
     */
    private boolean isEventStatusIsFinal(String loanKey){
        return gatewayClient.eventStatusIsFinalByLoanKey(loanKey);
    }

}
