package com.youxin.risk.datacenter.service.impl;

import com.youxin.risk.commons.dao.datacenter.DcSubmitFundMapper;
import com.youxin.risk.commons.model.datacenter.DcSubmitFund;
import com.youxin.risk.datacenter.service.SubmitFundService;
import org.apache.commons.lang3.NotImplementedException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 资方信息
 *
 * <AUTHOR>
 * @Date 2022-10-24 14:52
 */
@Service
public class SubmitFundServiceImpl extends AbstractBatchQueryService<DcSubmitFund> implements SubmitFundService {

    @Autowired
    private DcSubmitFundMapper dcSubmitFundMapper;

    @Override
    public DcSubmitFund getItem(DcSubmitFund item) {
        throw new NotImplementedException("SubmitFundServiceImpl.getItem");
    }

    @Override
    public Long insertItem(DcSubmitFund item) {
        dcSubmitFundMapper.insert(item);
        return item.getId();
    }

    @Override
    public DcSubmitFund getByOperationId(Long operationId) {
        return dcSubmitFundMapper.getByOperationId(operationId);
    }

    @Override
    public DcSubmitFund getByUserKey(String userKey, String fund) {
        return dcSubmitFundMapper.getByUserKey(userKey, fund);
    }
}
