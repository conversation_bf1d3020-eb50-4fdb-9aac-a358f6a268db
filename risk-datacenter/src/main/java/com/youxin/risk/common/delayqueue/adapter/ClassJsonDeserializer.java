package com.youxin.risk.common.delayqueue.adapter;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.youxin.risk.common.delayqueue.DelayMessageHandler;

public class ClassJsonDeserializer  extends JsonDeserializer<Class<? extends DelayMessageHandler>> {
	@Override
	public Class<? extends DelayMessageHandler> deserialize(JsonParser p, DeserializationContext context) {
		try {
			if (p.getText()!=null && !p.getText().isEmpty()) {
				return (Class) Class.forName(p.getText());
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		return null;
	}
}
