package com.youxin.risk.verify.vo;


import com.youxin.risk.verify.annotion.ParameterNullable;
import com.youxin.risk.verify.annotion.XmlEntity;
import com.youxin.risk.verify.annotion.XmlNode;

import java.util.Date;

@XmlEntity(name= "feature")
public class VerifyLineRepayMentVo extends VerifyLineCommonVo {

	@XmlNode(name = "repay_amount")
    private Double repayAmount;

	@XmlNode(name = "repay_time")
    private Date repayTime;
    
	@XmlNode(name = "last_line_management")
    private VerifyUserLineManagementVo lastLineManagement;

    @XmlNode(name = "cur_bill_repay_plan")
    private String curBillRepayPlan;
    
	@XmlNode(name = "his_bill_repay_plan")
    private String hisBillRepayPlan;
	
	@XmlNode(name = "self_data")
	private String selfData;
	
    @XmlNode(name = "user_key")
    private String whiteUserKey;

    @XmlNode(name = "mobile")
    private String registerPhone;

	@XmlNode(name = "third_party_data")
	private String thirdPartyData;

	@ParameterNullable
	public String getThirdPartyData() {
		return thirdPartyData;
	}

	public void setThirdPartyData(String thirdPartyData) {
		this.thirdPartyData = thirdPartyData;
	}

	@ParameterNullable
	public String getWhiteUserKey() {
		return whiteUserKey;
	}

	public void setWhiteUserKey(String whiteUserKey) {
		this.whiteUserKey = whiteUserKey;
	}

	@ParameterNullable
	public String getRegisterPhone() {
		return registerPhone;
	}

	public void setRegisterPhone(String registerPhone) {
		this.registerPhone = registerPhone;
	}

	@ParameterNullable
	public String getSelfData() {
		return selfData;
	}

	public void setSelfData(String selfData) {
		this.selfData = selfData;
	}

	@ParameterNullable
	public VerifyUserLineManagementVo getLastLineManagement() {
		return lastLineManagement;
	}

	public void setLastLineManagement(VerifyUserLineManagementVo lastLineManagement) {
		this.lastLineManagement = lastLineManagement;
	}

	@ParameterNullable
	public String getCurBillRepayPlan() {
		return curBillRepayPlan;
	}
	
	public void setCurBillRepayPlan(String curBillRepayPlan) {
		this.curBillRepayPlan = curBillRepayPlan;
	}
	
	@ParameterNullable
	public String getHisBillRepayPlan() {
		return hisBillRepayPlan;
	}
	
	public void setHisBillRepayPlan(String hisBillRepayPlan) {
		this.hisBillRepayPlan = hisBillRepayPlan;
	}
	
	
	
	public Double getRepayAmount() {
		return repayAmount;
	}

	public void setRepayAmount(Double repayAmount) {
		this.repayAmount = repayAmount;
	}

	public Date getRepayTime() {
		return repayTime;
	}

	public void setRepayTime(Date repayTime) {
		this.repayTime = repayTime;
	}

}
