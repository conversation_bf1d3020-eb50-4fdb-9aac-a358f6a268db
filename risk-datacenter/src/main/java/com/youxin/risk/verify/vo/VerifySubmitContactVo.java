package com.youxin.risk.verify.vo;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.List;

public class VerifySubmitContactVo extends VerifyCommonVo {
    private static final long serialVersionUID = -6005720005160031809L;
    private List<VerifySubmitContactDetailsVo> contactList;

    private List<VerifySubmitContactDetailsVo> addContactList;

    // 尝试将两个联系人填写为同一人  0无 1有
    private String isRepeat;

    public String getIsRepeat() {
        return isRepeat;
    }

    public void setIsRepeat(String isRepeat) {
        this.isRepeat = isRepeat;
    }

    public List<VerifySubmitContactDetailsVo> getContactList() {
        return contactList;
    }

    public void setContactList(List<VerifySubmitContactDetailsVo> contactList) {
        this.contactList = contactList;
    }

    public List<VerifySubmitContactDetailsVo> getAddContactList() {
        return addContactList;
    }

    public void setAddContactList(List<VerifySubmitContactDetailsVo> addContactList) {
        this.addContactList = addContactList;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this,
                ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
