package com.youxin.risk.verify.service.impl;

import com.youxin.risk.commons.utils.JsonUtils;
import com.youxin.risk.verify.mapper.VerifyTransactionMapper;
import com.youxin.risk.verify.model.VerifyTransaction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * 抽取数据服务
 *
 * <AUTHOR>
 * @version 创建时间：2018年7月19日 下午6:44:30
 */
@Service
public class VerifyTransactionDataService {

    private static final Logger LOG = LoggerFactory.getLogger(VerifyTransactionDataService.class);

    @Autowired
    private VerifyTransactionMapper verifyTransactionMapper;

    public Boolean update(VerifyTransaction trans) {
        int retryCount = 0;
        while(retryCount < 3) {
            try {
                this.verifyTransactionMapper.update(trans);
                return true;
            }catch(Exception e) {
                if(retryCount == 2) {
                    LOG.error("update transaction error,data={}", JsonUtils.toJson(trans),e);
                }else {
                    LOG.warn("update transaction error,data={}",JsonUtils.toJson(trans),e);
                }

            }
            retryCount++;
        }
        return false;
    }


    public VerifyTransaction get(Integer id) {
        return this.verifyTransactionMapper.get(id);
    }


    public VerifyTransaction findByUserKey(String userKey) {
        return this.verifyTransactionMapper.findByUserKey(userKey);
    }



}
