package com.youxin.risk.verify.service;


import com.youxin.risk.commons.model.verify.VerifyUserSystem;
import com.youxin.risk.verify.vo.VerifyUserSystemVo;

/**
 * 用户体系服务接口
 *
 * <AUTHOR>
 * @version 创建时间：2017年8月30日-下午4:21:46
 */
public interface VerifyUserSystemService {



    /**
     * 获取用户等级信息
     *
     * @param userKey
     * @return
     */
    VerifyUserSystemVo getUserLevelByUserKey(String userKey);

    VerifyUserSystem addUserLevelByVerify(String userKey, String userLevel, Double userPoint, String reason, boolean b);


    /**
     * 获取用户等级信息
     *
     * @param userKey
     * @return
     */
    VerifyUserSystemVo getUserLevel(String userKey);

    VerifyUserSystem findById(Integer id);

}
