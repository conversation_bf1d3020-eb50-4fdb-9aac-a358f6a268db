package com.youxin.risk.verify.delayqueue;

import com.youxin.risk.common.delayqueue.SubmitDelayMessage;
import com.youxin.risk.commons.utils.EnvUtil;
import org.redisson.api.RBlockingQueue;
import org.redisson.api.RDelayedQueue;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.util.Random;
import java.util.concurrent.TimeUnit;

public class RedisDelayQueue implements InitializingBean {

    @Value("${submit.delay.queue.prefix}")
    private String delayQueueNamePrefix;

    private int delayQueueNamePosfix = new Random().nextInt(1000);

    @Autowired
    private RedissonClient redissonClient;

    private RBlockingQueue<SubmitDelayMessage> blockingQueue;
    private RDelayedQueue<SubmitDelayMessage> delayedQueue;

    @Override
    public void afterPropertiesSet() throws Exception {
        /** 延时队列区分队列名 **/
        String suffix = EnvUtil.isBatch()?"_batch":"";
        String queueName = delayQueueNamePrefix + delayQueueNamePosfix + suffix;
        blockingQueue = redissonClient.getBlockingQueue(queueName);
        delayedQueue = redissonClient.getDelayedQueue(blockingQueue);
    }

    public void offer(SubmitDelayMessage e, long delay, TimeUnit timeUnit) {
        delayedQueue.offer(e, delay, timeUnit);
    }

    public SubmitDelayMessage take() throws InterruptedException {
        return blockingQueue.take();
    }
}
