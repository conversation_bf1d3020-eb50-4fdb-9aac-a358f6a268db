package com.youxin.risk.verify.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.dao.verify.VerifyResultHitMapper;
import com.youxin.risk.commons.model.verify.VerifyResultHit;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.StringUtils;
import com.youxin.risk.datacenter.pojo.JsonResultVo;
import com.youxin.risk.ra.utils.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class VerifyResultHitServiceImpl {

    private static final Logger logger = LoggerFactory.getLogger(VerifyResultHitServiceImpl.class);

    @Autowired
    private VerifyResultHitMapper verifyResultHitMapper;

    /**
     * 查询 apr触碰率
     *
     * @return
     */
    public JsonResultVo queryAprHitRate(JSONObject params) {
        try {
            handleQueryParam(params);
            List<Map<String, Object>> hitRateList = verifyResultHitMapper.queryAprHitRate(params);
            JsonResultVo resultVo = JsonResultVo.success();
            resultVo.addData("hitRate", hitRateList);
            return resultVo;
        } catch (Exception e) {
            LoggerProxy.error("queryAprHitRateError", logger, "param={}", params.toString(), e);
            return JsonResultVo.error();
        }
    }

    /**
     * 查询 irr触碰率
     *
     * @return
     */
    public JsonResultVo queryIrrHitRate(JSONObject params) {
        try {
            handleQueryParam(params);
//            List<Map<String, Object>> hitRateList = verifyResultHitMapper.queryIrrHitRate(params);
            List<VerifyResultHit> verifyResultHits = verifyResultHitMapper.queryIrrHitRateNew(params);
            Map<String, List<VerifyResultHit>> createHourGroup = verifyResultHits.stream().collect(Collectors.groupingBy(VerifyResultHit::getCreateHour));
            List<Map<String,Object>> hitRateList = new ArrayList<>();
            for (Map.Entry<String, List<VerifyResultHit>> data : createHourGroup.entrySet()){
                Map<String,Object> map = new HashMap<>();
                map.put("create_hour",data.getKey());
                /**按sql的逻辑汇总 定义所有变量**/
                Set<Integer> loanIdSet = new HashSet<>();
                Integer passTotal = 0;
                Integer B001 = 0;
                Integer D001 = 0;
                Integer D002 = 0;
                Integer D003 = 0;
                Integer D004 = 0;
                Integer D005 = 0;
                Integer D006 = 0;
                Integer D007 = 0;
                Integer D008 = 0;
                Integer D009 = 0;
                Integer D010 = 0;
                Integer D011 = 0;
                Integer D012 = 0;
                Integer D013 = 0;
                Integer D014 = 0;
                Integer D015 = 0;
                Integer D016 = 0;
                Integer D017 = 0;
                Integer D018 = 0;
                Integer D019 = 0;
                Integer D020 = 0;
                Integer D021 = 0;
                Integer D022 = 0;
                Integer D023 = 0;
                Integer D024 = 0;
                Integer D025 = 0;
                Integer D026 = 0;
                Integer D027 = 0;
                Integer D028 = 0;
                Integer D029 = 0;
                Integer D030 = 0;
                Integer D031 = 0;
                Integer D032 = 0;
                Integer D033 = 0;
                Integer D034 = 0;
                Integer D035 = 0;
                Integer D036 = 0;
                Integer D037 = 0;
                Integer D038 = 0;
                Integer D039 = 0;
                Integer D040 = 0;
                Integer D041 = 0;
                Integer D042 = 0;
                Integer D043 = 0;
                Integer D044 = 0;
                Integer D045 = 0;
                Integer D046 = 0;
                Integer D047 = 0;
                Integer D048 = 0;
                Integer D049 = 0;
                Integer D050 = 0;
                Integer D051 = 0;
                Integer D052 = 0;
                Integer D053 = 0;
                Integer D054 = 0;
                Integer D055 = 0;
                Integer D056 = 0;
                Integer D057 = 0;
                Integer D058 = 0;
                Integer D059 = 0;
                Integer D060 = 0;
                Integer D061 = 0;
                Integer D062 = 0;
                Integer D063 = 0;
                Integer D064 = 0;
                Integer D065 = 0;
                Integer D066 = 0;
                Integer D067 = 0;
                Integer D068 = 0;
                Integer D069 = 0;
                Integer D070 = 0;
                Integer D071 = 0;
                Integer D072 = 0;
                Integer D073 = 0;
                Integer D074 = 0;
                Integer D075 = 0;
                Integer D076 = 0;
                Integer D077 = 0;
                Integer D078 = 0;
                Integer D079 = 0;
                Integer D080 = 0;
                Integer D081 = 0;
                Integer D082 = 0;
                Integer D083 = 0;
                Integer D084 = 0;
                Integer D085 = 0;
                Integer D086 = 0;
                Integer D087 = 0;
                Integer D088 = 0;
                Integer D089 = 0;
                Integer D090 = 0;
                Integer D091 = 0;
                Integer D092 = 0;
                Integer D093 = 0;
                Integer D094 = 0;
                Integer D095 = 0;
                Integer D096 = 0;
                Integer D097 = 0;
                Integer D098 = 0;
                Integer D099 = 0;
                /** 遍历 List<VerifyResultHit> **/
                for (VerifyResultHit verifyResultHit: data.getValue()){
                    String reasonCode = verifyResultHit.getReasonCode();
                    Integer loanId = verifyResultHit.getLoanId();
                    Boolean isFinalPassed = verifyResultHit.getIsFinalPassed();
                    if (StringUtils.isBlank(reasonCode)){
                        continue;
                    }
                    if (reasonCode.contains("B001")){
                        B001++;
                    }
                    if (reasonCode.contains("D001")){
                        D001++;
                    }
                    if (reasonCode.contains("D002")){
                        D002++;
                    }
                    if (reasonCode.contains("D003")){
                        D003++;
                    }
                    if (reasonCode.contains("D004")){
                        D004++;
                    }
                    if (reasonCode.contains("D005")){
                        D005++;
                    }
                    if (reasonCode.contains("D006")){
                        D006++;
                    }
                    if (reasonCode.contains("D007")){
                        D007++;
                    }
                    if (reasonCode.contains("D008")){
                        D008++;
                    }
                    if (reasonCode.contains("D009")){
                        D009++;
                    }
                    if (reasonCode.contains("D010")){
                        D010++;
                    }
                    if (reasonCode.contains("D011")){
                        D011++;
                    }
                    if (reasonCode.contains("D012")){
                        D012++;
                    }
                    if (reasonCode.contains("D013")){
                        D013++;
                    }
                    if (reasonCode.contains("D014")){
                        D014++;
                    }
                    if (reasonCode.contains("D015")){
                        D015++;
                    }
                    if (reasonCode.contains("D016")){
                        D016++;
                    }
                    if (reasonCode.contains("D017")){
                        D017++;
                    }
                    if (reasonCode.contains("D018")){
                        D018++;
                    }
                    if (reasonCode.contains("D019")){
                        D019++;
                    }
                    if (reasonCode.contains("D020")){
                        D020++;
                    }
                    if (reasonCode.contains("D021")){
                        D021++;
                    }
                    if (reasonCode.contains("D022")){
                        D022++;
                    }
                    if (reasonCode.contains("D023")){
                        D023++;
                    }
                    if (reasonCode.contains("D024")){
                        D024++;
                    }
                    if (reasonCode.contains("D025")){
                        D025++;
                    }
                    if (reasonCode.contains("D026")){
                        D026++;
                    }
                    if (reasonCode.contains("D027")){
                        D027++;
                    }
                    if (reasonCode.contains("D028")){
                        D028++;
                    }
                    if (reasonCode.contains("D029")){
                        D029++;
                    }
                    if (reasonCode.contains("D030")){
                        D030++;
                    }
                    if (reasonCode.contains("D031")){
                        D031++;
                    }
                    if (reasonCode.contains("D032")){
                        D032++;
                    }
                    if (reasonCode.contains("D033")){
                        D033++;
                    }
                    if (reasonCode.contains("D034")){
                        D034++;
                    }
                    if (reasonCode.contains("D035")){
                        D035++;
                    }
                    if (reasonCode.contains("D036")){
                        D036++;
                    }
                    if (reasonCode.contains("D037")){
                        D037++;
                    }
                    if (reasonCode.contains("D038")){
                        D038++;
                    }
                    if (reasonCode.contains("D039")){
                        D039++;
                    }
                    if (reasonCode.contains("D040")){
                        D040++;
                    }
                    if (reasonCode.contains("D041")){
                        D041++;
                    }
                    if (reasonCode.contains("D042")){
                        D042++;
                    }
                    if (reasonCode.contains("D043")){
                        D043++;
                    }
                    if (reasonCode.contains("D044")){
                        D044++;
                    }
                    if (reasonCode.contains("D045")){
                        D045++;
                    }
                    if (reasonCode.contains("D046")){
                        D046++;
                    }
                    if (reasonCode.contains("D047")){
                        D047++;
                    }
                    if (reasonCode.contains("D048")){
                        D048++;
                    }
                    if (reasonCode.contains("D049")){
                        D049++;
                    }
                    if (reasonCode.contains("D050")){
                        D050++;
                    }


                    if (reasonCode.contains("D051")){
                        D051++;
                    }
                    if (reasonCode.contains("D052")){
                        D052++;
                    }
                    if (reasonCode.contains("D053")){
                        D053++;
                    }
                    if (reasonCode.contains("D054")){
                        D054++;
                    }
                    if (reasonCode.contains("D055")){
                        D055++;
                    }
                    if (reasonCode.contains("D056")){
                        D056++;
                    }
                    if (reasonCode.contains("D057")){
                        D057++;
                    }
                    if (reasonCode.contains("D058")){
                        D058++;
                    }
                    if (reasonCode.contains("D059")){
                        D059++;
                    }
                    if (reasonCode.contains("D060")){
                        D060++;
                    }
                    if (reasonCode.contains("D061")){
                        D061++;
                    }
                    if (reasonCode.contains("D062")){
                        D062++;
                    }
                    if (reasonCode.contains("D063")){
                        D063++;
                    }
                    if (reasonCode.contains("D064")){
                        D064++;
                    }
                    if (reasonCode.contains("D065")){
                        D065++;
                    }
                    if (reasonCode.contains("D066")){
                        D066++;
                    }
                    if (reasonCode.contains("D067")){
                        D067++;
                    }
                    if (reasonCode.contains("D068")){
                        D068++;
                    }
                    if (reasonCode.contains("D069")){
                        D069++;
                    }
                    if (reasonCode.contains("D070")){
                        D070++;
                    }
                    if (reasonCode.contains("D071")){
                        D071++;
                    }
                    if (reasonCode.contains("D072")){
                        D072++;
                    }
                    if (reasonCode.contains("D073")){
                        D073++;
                    }
                    if (reasonCode.contains("D074")){
                        D074++;
                    }
                    if (reasonCode.contains("D075")){
                        D075++;
                    }
                    if (reasonCode.contains("D076")){
                        D076++;
                    }
                    if (reasonCode.contains("D077")){
                        D077++;
                    }
                    if (reasonCode.contains("D078")){
                        D078++;
                    }
                    if (reasonCode.contains("D079")){
                        D079++;
                    }
                    if (reasonCode.contains("D080")){
                        D080++;
                    }
                    if (reasonCode.contains("D081")){
                        D081++;
                    }
                    if (reasonCode.contains("D082")){
                        D082++;
                    }
                    if (reasonCode.contains("D083")){
                        D083++;
                    }
                    if (reasonCode.contains("D084")){
                        D084++;
                    }
                    if (reasonCode.contains("D085")){
                        D085++;
                    }
                    if (reasonCode.contains("D086")){
                        D086++;
                    }
                    if (reasonCode.contains("D087")){
                        D087++;
                    }
                    if (reasonCode.contains("D088")){
                        D088++;
                    }
                    if (reasonCode.contains("D089")){
                        D089++;
                    }
                    if (reasonCode.contains("D090")){
                        D090++;
                    }
                    if (reasonCode.contains("D091")){
                        D091++;
                    }
                    if (reasonCode.contains("D092")){
                        D092++;
                    }
                    if (reasonCode.contains("D093")){
                        D093++;
                    }
                    if (reasonCode.contains("D094")){
                        D094++;
                    }
                    if (reasonCode.contains("D095")){
                        D095++;
                    }
                    if (reasonCode.contains("D096")){
                        D096++;
                    }
                    if (reasonCode.contains("D097")){
                        D097++;
                    }
                    if (reasonCode.contains("D098")){
                        D098++;
                    }
                    if (reasonCode.contains("D099")){
                        D099++;
                    }
                    loanIdSet.add(loanId);
                    if (isFinalPassed){
                        passTotal++;
                    }
                }
                map.put("cnt_loan_id",loanIdSet.size());
                map.put("passTotal",passTotal);
                map.put("B001",B001);
                map.put("D001",D001);
                map.put("D002",D002);
                map.put("D003",D003);
                map.put("D004",D004);
                map.put("D005",D005);
                map.put("D006",D006);
                map.put("D007",D007);
                map.put("D008",D008);
                map.put("D009",D009);
                map.put("D010",D010);
                map.put("D011",D011);
                map.put("D012",D012);
                map.put("D013",D013);
                map.put("D014",D014);
                map.put("D015",D015);
                map.put("D016",D016);
                map.put("D017",D017);
                map.put("D018",D018);
                map.put("D019",D019);
                map.put("D020",D020);
                map.put("D021",D021);
                map.put("D022",D022);
                map.put("D023",D023);
                map.put("D024",D024);
                map.put("D025",D025);
                map.put("D026",D026);
                map.put("D027",D027);
                map.put("D028",D028);
                map.put("D029",D029);
                map.put("D030",D030);
                map.put("D031",D031);
                map.put("D032",D032);
                map.put("D033",D033);
                map.put("D034",D034);
                map.put("D035",D035);
                map.put("D036",D036);
                map.put("D037",D037);
                map.put("D038",D038);
                map.put("D039",D039);
                map.put("D040",D040);
                map.put("D041",D041);
                map.put("D042",D042);
                map.put("D043",D043);
                map.put("D044",D044);
                map.put("D045",D045);
                map.put("D046",D046);
                map.put("D047",D047);
                map.put("D048",D048);
                map.put("D049",D049);
                map.put("D050",D050);
                map.put("D051",D051);
                map.put("D052",D052);
                map.put("D053",D053);
                map.put("D054",D054);
                map.put("D055",D055);
                map.put("D056",D056);
                map.put("D057",D057);
                map.put("D058",D058);
                map.put("D059",D059);
                map.put("D060",D060);
                map.put("D061",D061);
                map.put("D062",D062);
                map.put("D063",D063);
                map.put("D064",D064);
                map.put("D065",D065);
                map.put("D066",D066);
                map.put("D067",D067);
                map.put("D068",D068);
                map.put("D069",D069);
                map.put("D070",D070);
                map.put("D071",D071);
                map.put("D072",D072);
                map.put("D073",D073);
                map.put("D074",D074);
                map.put("D075",D075);
                map.put("D076",D076);
                map.put("D077",D077);
                map.put("D078",D078);
                map.put("D079",D079);
                map.put("D080",D080);
                map.put("D081",D081);
                map.put("D082",D082);
                map.put("D083",D083);
                map.put("D084",D084);
                map.put("D085",D085);
                map.put("D086",D086);
                map.put("D087",D087);
                map.put("D088",D088);
                map.put("D089",D089);
                map.put("D090",D090);
                map.put("D091",D091);
                map.put("D092",D092);
                map.put("D093",D093);
                map.put("D094",D094);
                map.put("D095",D095);
                map.put("D096",D096);
                map.put("D097",D097);
                map.put("D098",D098);
                map.put("D099",D099);
                hitRateList.add(map);
            }
            JsonResultVo resultVo = JsonResultVo.success();
            resultVo.addData("hitRate", hitRateList);
            return resultVo;
        } catch (Exception e) {
            LoggerProxy.error("queryIrrHitRateError", logger, "param={}", params.toString(), e);
            return JsonResultVo.error();
        }
    }


    /**
     * 处理查询 触碰率的参数
     *
     * @param params
     */
    private void handleQueryParam(JSONObject params) {
        String endTime = (String) params.get("endTime");
        String startTime ;
        // 只查询前一个小时的数据
        // endTime为 10:18，则 查询 9:00-10:00 的数据
        LocalDateTime endLocalDateTime;
        // 若 endTime为空，置为当前时间
        if (StringUtils.isEmpty(endTime)) {
            endLocalDateTime = LocalDateTime.now();
        }
        // 如果不为空，则时间截取到小时
        else {
            endLocalDateTime = DateUtils.getLocalDateTimeByStr(endTime);
        }
        endTime = DateUtils.getCurrentLongHourTimeStr(endLocalDateTime);
        // 开始时间为 endTime的小时数减一
        startTime = DateUtils.getCurrentLongHourTimeStr(endLocalDateTime.minusHours(1));

        params.put("startTime", startTime);
        params.put("endTime", endTime);
        // 新用户标签默认为 new_coming
        if (!params.containsKey("newTag")) {
            params.put("newTag", "new_coming");
        }
    }
}
