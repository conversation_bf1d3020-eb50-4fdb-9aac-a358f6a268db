package com.youxin.risk.verify.schedule;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledThreadPoolExecutor;

public class VerifyScheduledThreadPool {
    private static ScheduledThreadPoolExecutor scheduledThreadPool = null;
    private static final int scheduledThreadPoolSize = 200;

    private VerifyScheduledThreadPool() {
    }

    public static ScheduledThreadPoolExecutor getScheduledThreadPool() {
        if (scheduledThreadPool == null) {
            synchronized (ScheduledThreadPoolExecutor.class) {
                if (scheduledThreadPool == null) {
                    scheduledThreadPool = (ScheduledThreadPoolExecutor) Executors
                        .newScheduledThreadPool(scheduledThreadPoolSize);
                }
            }
        }
        return scheduledThreadPool;
    }
}
