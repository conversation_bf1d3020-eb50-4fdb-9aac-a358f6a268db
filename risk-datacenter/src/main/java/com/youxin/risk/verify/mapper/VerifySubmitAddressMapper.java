package com.youxin.risk.verify.mapper;

import com.youxin.risk.verify.model.VerifySubmitAddress;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface VerifySubmitAddressMapper  {


    int insert(VerifySubmitAddress verifySubmitAddress);

    @Deprecated
    VerifySubmitAddress findLastSubmitInfoByUserKey(@Param("userKey")String userKey);

    // 已经兼容优先从dc获取
    VerifySubmitAddress findLastSubmitInfoByUserKeyNoId(@Param("userKey")String userKey);
}
