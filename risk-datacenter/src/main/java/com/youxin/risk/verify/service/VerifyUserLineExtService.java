package com.youxin.risk.verify.service;

import com.youxin.risk.verify.vo.PageInfo;
import com.youxin.risk.verify.vo.VerifyAmountHistoryQryVo;
import com.youxin.risk.verify.vo.VerifyUserLineExtVo;

/**
 * 额度中心2.0查询用户历史额度变化接口
 *
 * <AUTHOR>
 * @since 2023/1/11 10:31
 */
public interface VerifyUserLineExtService {

    /**
     * 查询用户历史额度变化
     *
     * @param qryVo VerifyAmountHistoryQryVo
     * @return VerifyUserLineExtVo
     */
    PageInfo<VerifyUserLineExtVo> queryAmountHistory(VerifyAmountHistoryQryVo qryVo);
}
