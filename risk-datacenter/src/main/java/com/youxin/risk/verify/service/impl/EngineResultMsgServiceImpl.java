package com.youxin.risk.verify.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.model.verify.VerifyUserSystem;
import com.youxin.risk.commons.utils.StringUtils;
import com.youxin.risk.verify.service.EngineResultMsgService;
import com.youxin.risk.verify.service.VerifyUserSystemService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class EngineResultMsgServiceImpl implements EngineResultMsgService {
    private static final Logger log = LoggerFactory.getLogger(EngineResultMsgServiceImpl.class);

    @Autowired
    private VerifyUserSystemService verifyUserSystemService;

    @Override
    public VerifyUserSystem insertVerifyUserSystem(JSONObject verifyResultJson, JSONObject params) {
        String userLevel = verifyResultJson.getString("userLevel");
        log.info("saveVerifyUserSystem userKey={}, loanKey={},userLevel={}", params.getString("userKey"), params.getString("loanKey"), userLevel);
        if (StringUtils.isNotBlank(userLevel)) {
            try {
                VerifyUserSystem verifyUserSystem = verifyUserSystemService.addUserLevelByVerify(params.getString("userKey"),userLevel, verifyResultJson.getDouble("userPoint"), verifyResultJson.getString("reason"), false);
                log.info("userKey={},loanKey={} add user level success", params.getString("userKey"), params.getString("loanKey"));
                return verifyUserSystem;
            } catch (Exception e) {
                log.error("userKey={},loanKey={} add user level error", params.getString("userKey"), params.getString("loanKey"), e);
            }
        }
        return null;
    }
}
