package com.youxin.risk.ra.mapper;

import com.youxin.risk.ra.model.SubmitAddress;
import org.apache.ibatis.annotations.Param;

public interface SubmitAddressMapper extends BaseMapper<SubmitAddress> {

    // 已经兼容优先从dc获取
    SubmitAddress findLastSubmitByUserKey(@Param("userKey")String userKey, @Param("sourceSystem")String sourceSystem);

    @Deprecated
    SubmitAddress findAddressByUserApply(@Param("userKey")String userKey, @Param("applyId")Integer applyId);


}
