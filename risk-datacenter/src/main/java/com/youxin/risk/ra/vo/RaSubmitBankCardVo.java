package com.youxin.risk.ra.vo;

import com.youxin.risk.commons.vo.datavo.SubmitCommonVo;
import com.youxin.risk.ra.annotation.ParameterEnum;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

public class RaSubmitBankCardVo extends SubmitCommonVo {
	   private String bankcardNo;
	    private String bankName;
	    private String bankAddress;
	    private String reservedMobile;
	    private Boolean validation;

	    public String getBankcardNo() {
	        return this.bankcardNo;
	    }

	    public void setBankcardNo(String bankcardNo) {
	        this.bankcardNo = bankcardNo;
	    }

	    public String getBankName() {
	        return this.bankName;
	    }

	    public void setBankName(String bankName) {
	        this.bankName = bankName;
	    }

	    @ParameterEnum
	    public String getBankAddress() {
	        return this.bankAddress;
	    }

	    public void setBankAddress(String bankAddress) {
	        this.bankAddress = bankAddress;
	    }

	    public String getReservedMobile() {
	        return this.reservedMobile;
	    }

	    public void setReservedMobile(String reservedMobile) {
	        this.reservedMobile = reservedMobile;
	    }

	    public Boolean getValidation() {
	        return this.validation;
	    }

	    public void setValidation(Boolean validation) {
	        this.validation = validation;
	    }

	    @Override
	    public String toString() {
	        return ToStringBuilder.reflectionToString(this,
	            ToStringStyle.SHORT_PREFIX_STYLE);
	    }

}
