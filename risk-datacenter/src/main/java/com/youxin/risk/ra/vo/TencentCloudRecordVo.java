/**
 * Copyright(c) 2011-2018 by YouCredit Inc.
 * All Rights Reserved
 */
package com.youxin.risk.ra.vo;

import com.youxin.risk.ra.mongo.vo.AbstractRecordVo;

/**
 * <AUTHOR>
 * @version 创建时间：2018年3月13日-下午12:20:58
 */
public class TencentCloudRecordVo extends AbstractRecordVo {

	private TencentCloudRecordDataVo antiFraud;

	public TencentCloudRecordDataVo getAntiFraud() {
		return this.antiFraud;
	}

	public void setAntiFraud(TencentCloudRecordDataVo antiFraud) {
		this.antiFraud = antiFraud;
	}

}
