/**
 * Copyright(c) 2011-2017 by YouCredit Inc.
 * All Rights Reserved
 */
package com.youxin.risk.ra.service;

import com.youxin.risk.ra.model.DataRequestTask;
import com.youxin.risk.ra.mongo.dao.AlipayDao;
import com.youxin.risk.ra.mongo.vo.AlipayVo;
import com.youxin.risk.ra.service.impl.AbstractBaseDataService;
import com.youxin.risk.ra.vo.DataFetchVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 创建时间：2020-07-21
 */
@Service
public class AlipayService extends AbstractBaseDataService<AlipayVo> {


	@Autowired
	private AlipayDao alipayDao;
	@Override
	public void saveData(AlipayVo recordVo, Integer taskId) {
		recordVo.setCreatedTime(new Date());
		this.alipayDao.saveRecord(recordVo, taskId);
	}

	@Override
	public AlipayVo getDataFromDB(DataRequestTask dataTask) {
		return null;
	}

	@Override
	public AlipayVo getDataFromPlatform(DataFetchVo vo) {
		return null;
	}
}
