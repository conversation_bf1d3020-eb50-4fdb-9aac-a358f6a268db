package com.youxin.risk.ra.service.impl;

import com.youxin.risk.ra.enums.DataStatus;
import com.youxin.risk.ra.enums.QueryType;
import com.youxin.risk.ra.enums.StatType;
import com.youxin.risk.ra.mapper.DataStatisticsMapper;
import com.youxin.risk.ra.model.DataStatistics;
import com.youxin.risk.ra.service.DataStatisticsService;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Service
public class DataStatisticsServiceImpl implements DataStatisticsService {

	@Autowired
	private DataStatisticsMapper dataStatisticsMapper;

	@Override
	public DataStatistics getDataStatistics(String sourceSystem, QueryType queryKey, String queryValue, StatType statType) {
		return dataStatisticsMapper.findDataStatisticsByQueryKey(sourceSystem, queryKey, queryValue, statType);
	}

	@Override
	public DataStatistics createDataStatistics(String sourceSystem, QueryType queryKey, String queryValue, StatType statType) {
		DataStatistics dataStatistics = new DataStatistics();
		dataStatistics.setSourceSystem(sourceSystem);
		dataStatistics.setQueryKey(queryKey);
		dataStatistics.setQueryValue(queryValue);
		dataStatistics.setStatType(statType);
		dataStatistics.setExpiry(DateUtils.addDays(new Date(), 7));
		dataStatistics.setFetchExpiry(DateUtils.addSeconds(new Date(), 300));
		dataStatistics.setStatus(DataStatus.PREPARING);
        dataStatistics.setVersion(0);
		dataStatisticsMapper.insert(dataStatistics);
		return dataStatistics;
	}

	@Override
	public DataStatistics updateDataStatistics(DataStatistics dataStatistics) {
		if (dataStatistics != null && dataStatistics.getId() != null) {
			dataStatisticsMapper.update(dataStatistics);
			return dataStatistics;
		}
		throw new RuntimeException("dataStatistics为空或者id为空");
	}
	
}
