package com.youxin.risk.ra.mapper;

import com.youxin.risk.ra.model.RaXwBankRequest;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface XwBankRequestMapper extends BaseMapper<RaXwBankRequest> {

    List<RaXwBankRequest> findAllLastSubmitInfoByUserKey(@Param("userKey") String userKey);

    int updateBankCardById(RaXwBankRequest raXwBankRequest);
}
