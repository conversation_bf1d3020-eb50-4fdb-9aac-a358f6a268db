package com.youxin.risk.ra.kafka.impl;

import com.youxin.risk.ra.kafka.IConsumer;
import com.youxin.risk.ra.kafka.IMessageHandler;
import com.youxin.risk.ra.kafka.commom.BeanSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public abstract class AbstractTopicConsumer<T> implements IConsumer<T> {

	private static final Logger logger = LoggerFactory
			.getLogger(AbstractTopicConsumer.class);

	protected final BeanSerializer<T> serializer;

	protected volatile boolean isRunning = true;

	public AbstractTopicConsumer(BeanSerializer<T> serializer) {
		this.serializer = serializer;
	}

	protected abstract void doConsume(IMessageHandler<T> handler);

	@Override
	public void consume(IMessageHandler<T> handler) {
		while (isRunning) {
			doConsume(handler);
		}
		logger.info("consumer is stopped");
	}

	@Override
	public void stopConsumer() {
		isRunning = false;
	}

	@Override
	public boolean isConsumerRunning() {
		return isRunning;
	}

}
