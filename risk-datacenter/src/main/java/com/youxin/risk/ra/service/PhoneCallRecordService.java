/**
 * Copyright(c) 2011-2017 by YouCredit Inc.
 * All Rights Reserved
 */
package com.youxin.risk.ra.service;

import com.youxin.risk.ra.model.DataRequestTask;
import com.youxin.risk.ra.mongo.dao.PhoneCallRecordDao;
import com.youxin.risk.ra.mongo.vo.PhoneCallRecordVo;
import com.youxin.risk.ra.service.impl.AbstractBaseDataService;
import com.youxin.risk.ra.vo.DataFetchVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 创建时间：2017年12月16日-下午3:39:34
 */
@Service
public class PhoneCallRecordService extends AbstractBaseDataService<PhoneCallRecordVo> {

	@Autowired
	private DataService dataService;

	@Autowired
	private PhoneCallRecordDao callRecordDao;


	@Override
	public void saveData(PhoneCallRecordVo recordVo, Integer dataTask) {
		recordVo.setCreatedTime(new Date());
		this.callRecordDao.saveRecord(recordVo, dataTask);
	}


	@Override
	public PhoneCallRecordVo getDataFromPlatform(DataFetchVo vo) {
		return this.dataService.getPhoneRecordData(vo);
	}

	@Override
	public PhoneCallRecordVo getDataFromDB(DataRequestTask dataTask) {
		return null;
	}
}
