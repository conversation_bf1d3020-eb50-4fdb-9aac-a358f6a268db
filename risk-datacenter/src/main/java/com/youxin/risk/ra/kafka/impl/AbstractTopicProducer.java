package com.youxin.risk.ra.kafka.impl;

import com.youxin.risk.ra.kafka.IProducer;
import com.youxin.risk.ra.kafka.commom.BeanSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public abstract class AbstractTopicProducer<T> implements IProducer<T> {

	private static final Logger logger = LoggerFactory
			.getLogger(AbstractTopicProducer.class);

	protected final BeanSerializer<T> serializer;

	public AbstractTopicProducer(BeanSerializer<T> serializer) {
		this.serializer = serializer;
	}

	protected abstract boolean doSend(T t);

	@Override
	public boolean send(T t) {
		if (null == t) {
			logger.warn("msg is null");
			return false;
		}
		return doSend(t);
	}

}
