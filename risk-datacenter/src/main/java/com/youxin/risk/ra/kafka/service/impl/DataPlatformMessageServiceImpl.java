package com.youxin.risk.ra.kafka.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.youxin.risk.commons.apollo.ApolloClientAdapter;
import com.youxin.risk.commons.apollo.ApolloNamespaceEnum;
import com.youxin.risk.commons.utils.JsonUtils;
import com.youxin.risk.metrics.MetricsAPI;
import com.youxin.risk.ra.kafka.IConsumer;
import com.youxin.risk.ra.kafka.IProducer;
import com.youxin.risk.ra.kafka.enums.DpKafkaMessageType;
import com.youxin.risk.ra.kafka.enums.KafkaMessageStatus;
import com.youxin.risk.ra.kafka.enums.KafkaRecordType;
import com.youxin.risk.ra.kafka.enums.MetricsPointEnum;
import com.youxin.risk.ra.kafka.service.BaseKafkaService;
import com.youxin.risk.ra.kafka.service.KafkaMessageLogServive;
import com.youxin.risk.ra.kafka.vo.DataPlatformMessageVo;
import com.youxin.risk.ra.kafka.vo.KafkaMessageLogVo;
import com.youxin.risk.ra.model.DataRequestTask;
import com.youxin.risk.ra.mongo.vo.*;
import com.youxin.risk.ra.service.*;
import com.youxin.risk.ra.service.impl.DataRequestTaskServiceWapper;
import com.youxin.risk.ra.utils.StringUtils;
import com.youxin.risk.ra.vo.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

public class DataPlatformMessageServiceImpl extends BaseKafkaService<DataPlatformMessageVo> {

    @Autowired
    private DataRequestTaskService dataRequestTaskService;

    @Autowired
    private DataRequestTaskServiceWapper dataRequestTaskServiceWapper;

    @Autowired
    private CallHistoryService callHistoryService;

    @Autowired
    private PhoneBookService phoneBookService;

    @Autowired
    private Rong360InfoService rong360InfoService;

    @Autowired
    private KafkaMessageLogServive kafkaMessageLogServive;


    @Autowired
    private DataKeXinService kexinService;

    @Autowired
    private ShortMessageService shortMessageService;

    @Autowired
    private DfxkZhimaService dfxkZhimaService;

    @Autowired
    private AlipayService alipayService;

    @Autowired
    private CreditCardBillService creditCardBillService;

    @Autowired
    private DataXiaoweiService xiaoweiService;

    @Autowired
    private TencentClountService tencentClountService;

    @Autowired
    private HujinService hujinService;

    @Autowired
    private DianhuabangService dianhuabangService;


    @Autowired
    private CalendarInfoService calendarInfoService;

    @Autowired
    private TianjiFenqifenService tianjiFenqifenService;

    @Autowired
    private NfcsReportService nfcsReportService;


    @Autowired
    private XinyanService xinyanService;


    @Override
    protected Logger getLogger() {
        return LoggerFactory.getLogger(DataPlatformMessageServiceImpl.class);
    }

    @Override
    protected void setProducer(IProducer<DataPlatformMessageVo> producer) {
    }

    @Override
    protected void setConsumer(IConsumer<DataPlatformMessageVo> consumer) {
        this.consumer = consumer;
    }

    /**
     * 适用于构造新的消费端
     * @param consumer
     */
    public void setConsumerNew(IConsumer<DataPlatformMessageVo> consumer) {
        this.consumer = consumer;
    }

    @Override
    protected void handleMessage(DataPlatformMessageVo message, String key, String topic) {
        //logger.info("收到kafka消息[{}]", message.serializeToJson());
        String jobId = message.getJobID();
        String recordType = message.getRecordType();
        //this.logger.info("receive kafka recordType={},jobid={}",recordType,jobId);
        KafkaRecordType kafkaRecordType = null;
        //只接收指定类型的消息
        try {
            kafkaRecordType = Enum.valueOf(KafkaRecordType.class, message.getRecordType());
            String skipRecords = ApolloClientAdapter.getStringConfig(ApolloNamespaceEnum.DC_INSIDE_SPACE, "skipRecords", "PHONE_BOOK,CALL_HISTORY,CALENDAR_INFO,SMS_REPORT");
            if (Objects.nonNull(kafkaRecordType) && StringUtils.isNotBlank(skipRecords) && skipRecords.contains(kafkaRecordType.name())) {
                return;
            }
            if (KafkaRecordType.NIFA_QUERY.equals(kafkaRecordType) || KafkaRecordType.XWBANK_APPROVAL.equals(kafkaRecordType)
                    || KafkaRecordType.XINYAN_BLACK_RECORD.equals(kafkaRecordType) || KafkaRecordType.QCLOUD_ANTIFRAUD.equals(kafkaRecordType)
                    || KafkaRecordType.XINYAN_RADAR_RECORD.equals(kafkaRecordType)) {
                return;
            }
        } catch (Exception e) {
            this.logger.debug("忽略不处理的消息类型 {}", recordType);
            return;
        }

        logger.info("handleKafKaMessage, recordType={}, jobId={},message={}", kafkaRecordType, jobId,
                message.getData());

        if (jobId != null && recordType != null) {
            DataRequestTask dataRequestTask = null;
            try {
                if (StringUtils.isEmpty(message.getRecordType())
                        || kafkaRecordType == KafkaRecordType.SHUMEI_REPORT
                        || kafkaRecordType == KafkaRecordType.PCAC_RISK_INFO) {
                    logger.info("Is it true that it is in use?-h-i");
                    dataRequestTask = this.dataRequestTaskService.getDataRequestTaskByJobId(jobId);
                } else {
                    logger.info("Is it true that it is in use?-f-i");
                    dataRequestTask = this.dataRequestTaskService
                            .getDataRequestTaskByJobIdAndType(jobId, message.getRecordType());
                }
            } catch (Exception e) {
                //logger.error("hanleMessage:获取task失败");
                // logger.error("handleMessage:设置task状态失败", e);
            }
            // task为空，可能是消息于http返回前到达，把消息留存下来
            KafkaMessageLogVo msgVo = new KafkaMessageLogVo();
            msgVo.setKey(key);
            msgVo.setTopic(topic);
            msgVo.setRecordType(recordType);

            if (dataRequestTask == null) {
                this.logger.warn("hanleMessage:jobID[{}]对应的task不存在", jobId);
                if ("SUCCESSFUL".equalsIgnoreCase(message.getType())) {
                    msgVo.setStatus(KafkaMessageStatus.UNDEAL);
                    if (!org.apache.commons.lang3.StringUtils.isBlank(message
                            .getData())) {
                        // 设置SUBTYPE eg:ALITAOBAO
                        if (kafkaRecordType == KafkaRecordType.ALITAOBAO) {
                            message.setSubType(this.getSubtype(kafkaRecordType,
                                    message.getData()));
                        }
                    }

                } else {
                    msgVo.setStatus(KafkaMessageStatus.ABANDON);
                }
            } else {
                if (DpKafkaMessageType.SUCCESSFUL.name().equalsIgnoreCase(message.getType())) {

                    // 只有走到这里的消息才我好分期需要处理的
                    Map<String, String> tags = new HashMap<>();
                    tags.put("recordType", String.valueOf(kafkaRecordType.name()));
                    MetricsAPI.point(MetricsPointEnum.ra_recv_dp_kafka_message.name(), tags);

                    Date taskCreateTime = dataRequestTask.getCreateTime();
                    Date now = new Date();
                    long seconds = (now.getTime() - taskCreateTime.getTime()) / 1000;

                    this.logger.info(
                            "RA-System Calculate Operation Time,memberValue=remoteThirdPartyData,senderFlag=notfound,step=notfound,callType=async,thirdPartyType={},duration={},userKey={},loanKey={},sourceSystem={},jobid={}",
                            kafkaRecordType, seconds, dataRequestTask.getUserKey(), dataRequestTask.getLoanKey(),
                            dataRequestTask.getSourceSystem(), jobId);

                    //对于data_request_task存在并且message中有data信息直接进行数据处理，对于无data数据需要调用数据平台接口
                    if (org.apache.commons.lang3.StringUtils.isBlank(message.getData()) || KafkaRecordType.RONG_RECORD.equals(kafkaRecordType)) {
                        try {
                            logger.info(
                                    "handleKafKaMessage,kafkaRecordType={},message={},userKey={},loanKey={},jobid={}",
                                    kafkaRecordType, message.getData(), dataRequestTask.getUserKey(),
                                    dataRequestTask.getLoanKey(), jobId);
                            this.dataRequestTaskService.processDataRequestTaskWithJobId(dataRequestTask.getId());
                        } catch (Exception e) {
                            this.logger.error("获取[{}]出错", dataRequestTask.getTaskType().name(), e);
                        }
                    } else {//直接处理数据
                        boolean isFetched = this.handleMessageData(kafkaRecordType, message.getData(), dataRequestTask);
                        if (isFetched) {//更新data_request_task status
                            logger.info("Is it true that it is in use?-a-i");
                            this.dataRequestTaskService.fetch(dataRequestTask.getId());
                        }
                    }


                    msgVo.setStatus(KafkaMessageStatus.DEAL);
                    msgVo.setDealTime(new Date());

                    try {
                        //所有异步任务是否完成
                        this.dataRequestTaskServiceWapper.checkReportDataTask(dataRequestTask.getSourceSystem(), dataRequestTask.getUserKey());
                    } catch (Exception e) {
                        this.logger.error("checkReportDataTask error,jobId={}", message.getJobID(), e);
                    }

                } else {
                    //logger.error("crawl fail");
                    msgVo.setStatus(KafkaMessageStatus.ABANDON);
                    // 暂时不把任务置为失败，以防数据平台先发爬取失败消息后再发成功的消息
                    // dataRequestTaskService.fail(dataRequestTask.getId());
                }
            }
            // 保存kafka消息
            message.setData(null);//不保存data数据
            msgVo.setMessage(message.serializeToJson());
            this.kafkaMessageLogServive.saveKafkaMessage(msgVo);
        } else {
            this.logger.error("jobID为空");
        }
    }


    public boolean handleMessageData(KafkaRecordType kafkaRecordType, String data, DataRequestTask dataRequestTask) {
        //this.logger.info("处理的Kafka data：{}",data);
        try {
            switch (kafkaRecordType) {
                case CALL_HISTORY:
                    MobileCallHistoryVo mobileCallHistoryVo = JsonUtils.toObject(data, MobileCallHistoryVo.class);
                    if (mobileCallHistoryVo == null) {
                        this.logger.error("kafka返回详单信息格式错误 ");
                        return false;
                    }

                    this.callHistoryService.saveCallHistory(mobileCallHistoryVo, dataRequestTask.getSourceSystem(), dataRequestTask.getUserKey(), dataRequestTask.getId());
                    return true;
                case PHONE_BOOK:
                    PhoneBookRecordVo phoneBookRecordVo = JSON.parseObject(data, new TypeReference<PhoneBookRecordVo>() {
                    });

                    if (phoneBookRecordVo == null) {
                        this.logger.error("kafka返回通讯录信息格式错误");
                        return false;
                    }
                    this.phoneBookService.savePhoneBook(phoneBookRecordVo, dataRequestTask.getSourceSystem(), dataRequestTask.getUserKey(), dataRequestTask.getApplyId());
                    return true;
                case RONG_RECORD:
                    RongRecordVo rongRecordVo = JSON.parseObject(data, new TypeReference<RongRecordVo>() {
                    });

                    if (rongRecordVo == null) {
                        this.logger.error("kafka返回RongRecord信息格式错误");
                        return false;
                    }

                    this.rong360InfoService.saveRongRecord(rongRecordVo,
                            dataRequestTask.getSourceSystem(), dataRequestTask.getUserKey(), dataRequestTask.getApplyId(), dataRequestTask.getId());
                    return true;
                case RONG_REPORT:
                    RongReportVo rongReportVo = JSON.parseObject(data,
                            new TypeReference<RongReportVo>() {
                            });

                    if (rongReportVo == null) {
                        this.logger.error("kafka返回RongReport信息格式错误");
                        return false;
                    }
                    this.rong360InfoService.saveRongReport(rongReportVo,
                            dataRequestTask.getSourceSystem(), dataRequestTask.getUserKey(), dataRequestTask.getApplyId(), dataRequestTask.getId());
                    return true;
                case CREDITX_FRAUD_RECORDS:
                case CREDITX_SCORE_RECORDS:
                    CreditxRecordVo fraudVo = JSON.parseObject(data,
                            new TypeReference<CreditxRecordVo>() {
                            });
                    if (fraudVo == null) {
                        this.logger.error("kafka返回CREDITX_FRAUD_RECORDS格式错误");
                        return false;
                    }
                    this.kexinService.saveData(dataRequestTask, fraudVo);
                    return true;
                case PCAC_RISK_INFO:
                    XiaoweiRecordVo xiaoweiRecordVo = JSON.parseObject(data,
                            new TypeReference<XiaoweiRecordVo>() {
                            });
                    if (xiaoweiRecordVo == null) {
                        this.logger.error("kafka返回PCAC_RISK_INFO格式错误");
                        return false;
                    }
                    this.xiaoweiService.saveData(dataRequestTask, xiaoweiRecordVo);
                    return true;
                case QCLOUD_ANTIFRAUD:
                    TencentCloudRecordVo tencentCloudRecordVo = JSON.parseObject(data,
                            new TypeReference<TencentCloudRecordVo>() {
                            });
                    if (tencentCloudRecordVo == null) {
                        this.logger.error("kafka返回QCLOUD_ANTIFRAUD格式错误");
                        return false;
                    }
                    this.tencentClountService.saveData(dataRequestTask, tencentCloudRecordVo);
                    return true;
                case NIFA_QUERY:
                    HujinRecordVo hujinRecordVo = JSON.parseObject(data,
                            new TypeReference<HujinRecordVo>() {
                            });
                    if (hujinRecordVo == null) {
                        this.logger.error("kafka返回NIFA_QUERY格式错误");
                        return false;
                    }
                    this.hujinService.saveData(dataRequestTask, hujinRecordVo);
                    return true;
                case DHB_CUISHOU:
                    DianhuabangRecordVo dianhuabangRecordVo = JSON.parseObject(data,
                            new TypeReference<DianhuabangRecordVo>() {
                            });
                    if (dianhuabangRecordVo == null) {
                        this.logger.error("kafka返回DHB_CUISHOU格式错误");
                        return false;
                    }
                    this.dianhuabangService.saveData(dataRequestTask, dianhuabangRecordVo);
                    return true;
                case PHONE_CALLRECORD:
                    return true;
                case SMS_REPORT:
                    MessageBodyVo messageVo = JSON.parseObject(data,
                            new TypeReference<MessageBodyVo>() {
                            });
                    if (messageVo == null) {
                        this.logger.error("kafka返回SMS格式错误");
                        return false;
                    }
                    this.shortMessageService.saveData(messageVo, dataRequestTask.getId());
                    return true;
                case DFXK_ZHIMA_RECORD:
                    DfxkZhimaRecordVo dfxkZhimaRecordVo = JSON.parseObject(data,
                            new TypeReference<DfxkZhimaRecordVo>() {
                            });
                    if (dfxkZhimaRecordVo == null || dfxkZhimaRecordVo.getData() == null) {
                        this.logger.error("kafka返回DfxkZhima格式错误");
                        return false;
                    }
                    this.dfxkZhimaService.saveData(dfxkZhimaRecordVo.getData(), dataRequestTask);
                    return true;
                case ALITAOBAO:
                    AliMessageVo taobaoVo = JSON.parseObject(data,
                            new TypeReference<AliMessageVo>() {
                            });
                    if (taobaoVo == null) {
                        this.logger.error("kafka返回Taobao格式错误");
                        return false;
                    }

                    return true;
                case ALIPAY:
                    AlipayVo alipayVo = JSON.parseObject(data,
                            new TypeReference<AlipayVo>() {
                            });
                    if (alipayVo == null) {
                        this.logger.error("kafka返回Alipay格式错误");
                        return false;
                    }
                    this.alipayService.saveData(alipayVo, dataRequestTask.getId());
                    return true;
                case CALENDAR_INFO:
                    CalendarRecordVo calendarRecordVo = JSON.parseObject(data,
                            new TypeReference<CalendarRecordVo>() {
                            });
                    if (calendarRecordVo == null) {
                        this.logger.error("kafka返回CALENDAR_INFO格式错误");
                        return false;
                    }
                    this.calendarInfoService.saveData(calendarRecordVo, dataRequestTask.getId());
                    return true;
                case EMAIL_ACCOUNT:
                    CreditCardBillRecordVo creditCardBillRecordVo = JSON.parseObject(data,
                            new TypeReference<CreditCardBillRecordVo>() {
                            });
                    if (creditCardBillRecordVo == null) {
                        this.logger.error("kafka返回信用卡账单格式错误");
                        return false;
                    }
                    this.creditCardBillService.saveData(creditCardBillRecordVo, dataRequestTask.getId());
                    return true;
                case TIANJI_BASIC_SCORE:
                    // 将天机alipay存入mongo
                    TianjiFenqifenRecordVo tianjiFenqifenRecordVo = JSON.parseObject(data,
                            new TypeReference<TianjiFenqifenRecordVo>() {
                            });
                    if (tianjiFenqifenRecordVo == null) {
                        this.logger.error("kafka返回TIANJI_INFO格式错误");
                        return false;
                    }
                    this.tianjiFenqifenService.saveData(dataRequestTask, tianjiFenqifenRecordVo);
                    return true;
                case NFCS_REPORT:
                    NfcsReportRecordVo nfcsRecordVo = JSON.parseObject(data, new TypeReference<NfcsReportRecordVo>() {
                    });
                    if (nfcsRecordVo == null || nfcsRecordVo.getData() == null) {
                        this.logger.error("kafka返回nfcs格式错误");
                        return false;
                    }
                    this.nfcsReportService.saveData(dataRequestTask, nfcsRecordVo);
                    return true;
                case XINYAN_BLACK_RECORD:
                case XINYAN_WHITE_RECORD:
                case XINYAN_RADAR_RECORD:

                    XinyanRecordVo xinyanRecordVo = JSON.parseObject(data, new TypeReference<XinyanRecordVo>() {
                    });
                    if (xinyanRecordVo == null) {
                        this.logger.error("kafka返回XINYAN_RADAR_RECORD格式错误");
                        return false;
                    }
                    this.xinyanService.saveData(dataRequestTask, xinyanRecordVo);
                    return true;
                default:
                    this.logger.warn("系统目前没有对接数据平台的接口:[{}]", kafkaRecordType.name());
                    break;
            }
        } catch (Exception e) {
            this.logger.error("保存Kafka消息数据出错", e);
        }

        return false;
    }

    private String getSubtype(KafkaRecordType recordType, String data) {

        if (recordType == KafkaRecordType.ALITAOBAO) {
            AliMessageVo taobaoVo = JSON.parseObject(data,
                    new TypeReference<AliMessageVo>() {
                    });
            if (taobaoVo == null) {
                this.logger.error("kafka返回Taobao格式错误");
                return null;
            }
            return taobaoVo.subType();
        }


        return null;
    }

    @Override
    protected boolean handleByMultiThread() {
        return false;
    }

    public void start() {
        this.logger.info("starting consumer");
        this.beginConsumer();
    }
}
