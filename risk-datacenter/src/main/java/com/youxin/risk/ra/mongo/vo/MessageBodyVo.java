/**
 * Copyright(c) 2011-2017 by YouCredit Inc.
 * All Rights Reserved
 */
package com.youxin.risk.ra.mongo.vo;

import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;
import java.util.List;

/**
 * 短信
 *
 * <AUTHOR>
 * @version 创建时间：2017年11月8日-下午3:51:30
 */
@Document(collection="SMS")
public class MessageBodyVo extends  AbstractRecordVo{

	private List<MessageDetailVo> calls;

	//@Indexed(expireAfterSeconds=432000)//5 days
	private Date createdTime;


	public List<MessageDetailVo> getCalls() {
		return this.calls;
	}

	public void setCalls(List<MessageDetailVo> calls) {
		this.calls = calls;
	}

	public Date getCreatedTime() {
		return this.createdTime;
	}

	public void setCreatedTime(Date createdTime) {
		this.createdTime = createdTime;
	}

}
