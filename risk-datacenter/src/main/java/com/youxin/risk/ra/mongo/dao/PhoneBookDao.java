/**
 * Copyright(c) 2011-2017 by YouCredit Inc.
 * All Rights Reserved
 */
package com.youxin.risk.ra.mongo.dao;

import com.youxin.risk.ra.mongo.vo.PhoneBookRecordVo;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @version 创建时间：2017年9月28日-下午3:28:34
 */
@Repository
public class PhoneBookDao extends MongoBaseDaoImpl<PhoneBookRecordVo>{

	public PhoneBookRecordVo getByApply(Integer applyId){
		Query query = new Query();
		query.addCriteria(Criteria.where("applyId").is(applyId));
        query.with(new Sort(Direction.DESC, "_id")).limit(1);
		return this.findOne(query);
	}

}
