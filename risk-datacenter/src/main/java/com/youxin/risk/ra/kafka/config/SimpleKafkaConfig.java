package com.youxin.risk.ra.kafka.config;

public class SimpleKafkaConfig implements KafkaConfig {
	private String topicName;
	private String bootstrapServers;
	private String consumerGroupId;

	public SimpleKafkaConfig(String topicName, String bootstrapServers,
			String consumerGroupId) {
		this.topicName = topicName;
		this.bootstrapServers = bootstrapServers;
		this.consumerGroupId = consumerGroupId;
	}

	public String getTopicName() {
		return topicName;
	}

	public void setTopicName(String topicName) {
		this.topicName = topicName;
	}

	public String getBootstrapServers() {
		return bootstrapServers;
	}

	public void setBootstrapServers(String bootstrapServers) {
		this.bootstrapServers = bootstrapServers;
	}

	public String getConsumerGroupId() {
		return consumerGroupId;
	}

	public void setConsumerGroupId(String consumerGroupId) {
		this.consumerGroupId = consumerGroupId;
	}
}
