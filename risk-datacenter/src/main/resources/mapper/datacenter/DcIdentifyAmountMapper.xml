<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.datacenter.mapper.DcIdentifyAmountMapper" >
  <resultMap id="BaseResultMap" type="com.youxin.risk.datacenter.model.DcIdentifyAmount" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="user_key" property="userKey" jdbcType="VARCHAR" />
    <result column="operation_type" property="operationType" jdbcType="VARCHAR" />
    <result column="request_id" property="requestId" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, user_key, operation_type,request_id, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from dc_identify_amount
    where id = #{id,jdbcType=INTEGER}
  </select>


  <select id="queryTxxyRecordInFifteenDays" resultType="java.lang.Integer"><![CDATA[
      select
      id
      from dc_identify_amount
      where user_key = #{userKey,jdbcType=VARCHAR}
      and create_time > #{dateTime,jdbcType=VARCHAR}
      and operation_type = #{certificationItem,jdbcType=VARCHAR}
      limit 1
      ]]>
  </select>

  <select id="queryOtherRecordInFifteenDays" resultType="java.lang.Integer"><![CDATA[
      select
      id
      from dc_identify_amount
      where user_key = #{userKey,jdbcType=VARCHAR}
      and create_time > #{dateTime,jdbcType=VARCHAR}
      and operation_type != 'AMOUNT_TIANXIAXINYONG'
      and operation_type != 'AMOUNT_TIANXIAXINYONG_EDUCATION'
      limit 1
      ]]>
  </select>

  <insert id="insert" parameterType="com.youxin.risk.datacenter.model.DcIdentifyAmount" >
    insert into dc_identify_amount (user_key, operation_type,request_id,
      create_time, update_time)
    values (#{userKey,jdbcType=VARCHAR}, #{operationType,jdbcType=VARCHAR},#{requestId,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>

</mapper>