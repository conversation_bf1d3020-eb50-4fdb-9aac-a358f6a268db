<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean id="redissonClientFactory" name="redissonClientFactory"
          class="com.youxin.risk.common.delayqueue.redisson.RedissonClientFactory">
        <constructor-arg name="nodes" value="${redis.cluster.nodes}"/>
        <constructor-arg name="poolSize" value="${redisson.maxPoolSize}"/>
        <constructor-arg name="connectionTimeout" value="${redis.cluster.connectionTimeout}"/>
        <constructor-arg name="scanInterval" value="10000"/>
        <constructor-arg name="idleConnectionTimeout" value="600000"/>
        <constructor-arg name="minimumIdleSize" value="${redisson.minPoolSize}"/>
        <constructor-arg name="retryInterval" value="2000"/>
        <constructor-arg name="password" value="${redis.cluster.password}"/>
    </bean>

    <bean id="redisDelayQueue" class="com.youxin.risk.verify.delayqueue.RedisDelayQueue" />
    <bean id="redissonClient" factory-bean="redissonClientFactory" factory-method="create"/>

    <bean id="routeDelayListener" class="com.youxin.risk.common.delayqueue.redisson.RouteDelayListener">
        <constructor-arg name="concurrency" value="5"/>
    </bean>

    <bean id="submitDelayService" class="com.youxin.risk.verify.delayqueue.SubmitDelayService"/>


    <!-- 上传 提额相关数据的 延迟补偿队列-->
    <bean id="submitAmountRedisDelayQueue" class="com.youxin.risk.datacenter.delayqueue.SubmitAmountRedisDelayQueue" />

    <!-- 上传 提额相关数据的 延迟补偿service-->
    <bean id="submitAmountDelayService" class="com.youxin.risk.datacenter.delayqueue.SubmitAmountDelayService"/>

</beans>