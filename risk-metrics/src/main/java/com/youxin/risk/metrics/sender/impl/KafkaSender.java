package com.youxin.risk.metrics.sender.impl;

import com.youxin.risk.kafka.constants.AppName;
import com.youxin.risk.kafka.sender.KafkaDynamicSender;
import com.youxin.risk.metrics.config.ConfigUtils;
import com.youxin.risk.metrics.constants.PropertiesKey;
import com.youxin.risk.metrics.helpers.LoggerUtils;
import com.youxin.risk.metrics.helpers.Utils;
import com.youxin.risk.metrics.helpers.redis.MJedis;
import com.youxin.risk.metrics.model.PointModel;
import com.youxin.risk.metrics.sender.Sender;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.ProducerListener;
import org.springframework.util.concurrent.ListenableFuture;
import org.springframework.util.concurrent.SettableListenableFuture;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

public class KafkaSender implements Sender {

    protected Logger logger = LoggerFactory.getLogger(getClass());

    private int retries;
    private String topic;
    private String bootstrapServers;
    private String mirrorBootstrapServers;
    private boolean autoFlush = false;

    private Map<String, Object> defaultProducerProperties = new HashMap<>();


    protected KafkaTemplate kafkaTemplate;

    protected KafkaTemplate mirrorKafkaTemplate;

    protected KafkaDynamicSender kafkaDynamicSender;

    private static final String CONFIG_KEY_RETRIES = "retries";
    private static final String CONFIG_KEY_REQUEST_TIMEOUT_MS = "request.timeout.ms";
    private static final String CONFIG_KEY_BATCH_SIZE = "batch.size";
    private static final String CONFIG_KEY_BUFFER_MEMORY = "buffer.memory";
    private static final String CONFIG_KEY_COMPRESSION_TYPE = "compression.type";
    private static final String CONFIG_KEY_KEY_SERIALIZER = "key.serializer";
    private static final String CONFIG_KEY_VALUE_SERIALIZER = "value.serializer";
    private static final String CONFIG_KEY_VALUE_BOOTSTRAP_SERVERS = "bootstrap.servers";
    private static final String CONFIG_KEY_SIZE = "max.request.size";

    protected static final int CONFIG_DEFAULT_RETRIES = 10;
    protected static final int CONFIG_DEFAULT_REQUEST_TIMEOUT_MS = 3000;
    protected static final int CONFIG_DEFAULT_BATCH_SIZE = 262144;
    protected static final int CONFIG_DEFAULT_BUFFER_MEMORY = 16777216;
    protected static final String CONFIG_DEFAULT_COMPRESSION_TYPE = "lz4";
    protected static final String CONFIG_DEFAULE_SERIALIZER = "org.apache.kafka.common.serialization.StringSerializer";
    private static final int CONFIG_DEFAULT_SIZE = 16777216;


    public KafkaSender() {

        this.retries = ConfigUtils.getInteger(PropertiesKey.REMOTE_QUEUE_RETRIES);

        this.topic = ConfigUtils.getString(PropertiesKey.POINT_KAFKA_TOPIC);
        this.bootstrapServers = ConfigUtils.getString(PropertiesKey.POINT_KAFKA_HOSTS);
        this.mirrorBootstrapServers = ConfigUtils.getString(PropertiesKey.POINT_KAFKA_MIRROR_HOSTS);

        defaultProducerProperties.put(CONFIG_KEY_RETRIES, CONFIG_DEFAULT_RETRIES);
        defaultProducerProperties.put(CONFIG_KEY_REQUEST_TIMEOUT_MS, CONFIG_DEFAULT_REQUEST_TIMEOUT_MS);
        defaultProducerProperties.put(CONFIG_KEY_BATCH_SIZE, CONFIG_DEFAULT_BATCH_SIZE);
        defaultProducerProperties.put(ProducerConfig.LINGER_MS_CONFIG, 1000);
        defaultProducerProperties.put(CONFIG_KEY_BUFFER_MEMORY, CONFIG_DEFAULT_BUFFER_MEMORY);
        defaultProducerProperties.put(CONFIG_KEY_COMPRESSION_TYPE, CONFIG_DEFAULT_COMPRESSION_TYPE);
        defaultProducerProperties.put(CONFIG_KEY_KEY_SERIALIZER, CONFIG_DEFAULE_SERIALIZER);
        defaultProducerProperties.put(CONFIG_KEY_VALUE_SERIALIZER, CONFIG_DEFAULE_SERIALIZER);
        defaultProducerProperties.put(CONFIG_KEY_SIZE, CONFIG_DEFAULT_SIZE);

        defaultProducerProperties.put(CONFIG_KEY_VALUE_BOOTSTRAP_SERVERS, bootstrapServers);

        DefaultKafkaProducerFactory factory = new DefaultKafkaProducerFactory(defaultProducerProperties);
        kafkaTemplate = new KafkaTemplate(factory, autoFlush);
        kafkaTemplate.setDefaultTopic(topic);
        kafkaTemplate.setProducerListener(new KafkaSenderListener());

        defaultProducerProperties.put(CONFIG_KEY_VALUE_BOOTSTRAP_SERVERS, mirrorBootstrapServers);
        factory = new DefaultKafkaProducerFactory(defaultProducerProperties);
        mirrorKafkaTemplate = new KafkaTemplate(factory, autoFlush);
        mirrorKafkaTemplate.setDefaultTopic(topic);

        kafkaDynamicSender = new KafkaDynamicSender(kafkaTemplate, mirrorKafkaTemplate, AppName.risk_metrics);
    }


    @Override
    public void pull(List<PointModel> points) {

        if (null == points || points.isEmpty()) {
            return;
        }
        String data;
        int time;
        for (int i = 0, size = points.size(); i < size; i++) {
            data = points.get(i).toJson();
            kafkaTemplate.send(kafkaTemplate.getDefaultTopic(), data);
//            Object writeRes;
//            time = 0;
//            do {
//                time++;
//                try {
//                    if (!(future instanceof SettableListenableFuture)) {
//                        logger.error("notSupportFutureType future=" + future);
//                        return;
//                    }
//                    SettableListenableFuture rFuture = (SettableListenableFuture) future;
//                    writeRes = rFuture.get(CONFIG_DEFAULT_REQUEST_TIMEOUT_MS, TimeUnit.MILLISECONDS);
//                    if (writeRes instanceof Exception) {
//                        throw (Exception) writeRes;
//                    }
//                    break;
//                } catch (Exception e) {
//                    if (time < retries) {
//                        Utils.threadSleep(50);
//                    } else {
//                        LoggerUtils.report("pull to redis failed, errmsg=" + e.getMessage());
//                    }
//                }
//            } while (time <= retries);
        }


    }
}