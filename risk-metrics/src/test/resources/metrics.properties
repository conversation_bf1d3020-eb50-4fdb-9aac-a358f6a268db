#关闭，一般测试环境设置为true
metrics.stop=false
# 命名空间(对应数据库)，长度不能大于32，只支持数字、字母、下划线，必输项
metrics.namespace=renrendai_antifraud_risk
# redis,kafka，必输项
metrics.remote.queue=redis


#redis是否cluster，必输项
#metrics.remote.redis.is.cluster=0
#127.0.0.1:8379，必输项
#metrics.remote.queue.server=172.16.2.143:6379

#redis是否cluster，必输项
metrics.remote.redis.is.cluster=1
#必输项
metrics.remote.queue.server=172.16.2.40:7000,172.16.2.40:7001,172.16.2.40:7002,\
  172.16.2.40:7100,172.16.2.40:7101,172.16.2.40:7102

#自定义消息推送器，优先级高于metrics.remote.queue
metrics.pull.sender.type=


#远端队列最大长度（对redis的保护，当大于该长度不再pull）
#metrics.remote.queue.redis.max.size=200000