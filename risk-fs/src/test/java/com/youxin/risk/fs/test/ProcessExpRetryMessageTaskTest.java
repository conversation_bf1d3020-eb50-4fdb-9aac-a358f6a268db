package com.youxin.risk.fs.test;

import com.youxin.risk.fs.task.ProcessExpRetryMessageTask;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration("classpath:spring/spring-config.xml")
public class ProcessExpRetryMessageTaskTest {

    @Autowired
    ProcessExpRetryMessageTask processExpRetryMessageTask;

    @Test
    public void test() {
        processExpRetryMessageTask.execJobHandler(null);
    }
}