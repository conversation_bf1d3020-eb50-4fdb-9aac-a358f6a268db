package com.youxin.risk.fs.config;

import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.elasticsearch.client.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Configuration
public class IndexConfiguration {

    @Bean
    public RestHighLevelClient initTransportClient(@Value("${es.server.cluster.name}") String clusterName,
                                                   @Value("${es.server.cluster.server}") String serverList){
        CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
        credentialsProvider.setCredentials(AuthScope.ANY,
                new UsernamePasswordCredentials("risk", "LXRity5Vt2HD1Q0vhK2S"));

        String[] serverArray = serverList.split(",");
        HttpHost[] httpHosts = new HttpHost[serverArray.length];
        for (int i = 0; i < serverArray.length; i++) {
            String [] serverHostPort = serverArray[i].split(":");
            String serverHost = serverHostPort[0];
            int serverPort = Integer.parseInt(serverHostPort[1]);
            HttpHost hh = new HttpHost(serverHost, serverPort, "http");
            httpHosts[i] = hh;
        }

        RestClientBuilder restClient = RestClient.builder(httpHosts)
                .setHttpClientConfigCallback(x -> x.setDefaultCredentialsProvider(credentialsProvider));

        return new RestHighLevelClient(restClient);
    }

//    public static void main(String[] args) throws Exception{
//        final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
//        credentialsProvider.setCredentials(AuthScope.ANY,
//                new UsernamePasswordCredentials("risk", "LXRity5Vt2HD1Q0vhK2S"));
//        RestClientBuilder restClient = RestClient.builder(new HttpHost("elk-prod.weicai.com.cn", 80, "http"))
//                .setHttpClientConfigCallback(x -> x.setDefaultCredentialsProvider(credentialsProvider));
//        RestHighLevelClient client = new RestHighLevelClient(restClient);
//        // 创建GetIndexRequest请求
//        GetIndexRequest getIndexRequest = new GetIndexRequest("esdatasubmit_2024-08-27");
//        // 使用IndicesClient检查索引是否存在
//        boolean exists = client.indices().exists(getIndexRequest, RequestOptions.DEFAULT);
//        System.out.println(exists);
//        // 创建删除索引请求
//        DeleteIndexRequest deleteIndexRequest = new DeleteIndexRequest("esstrategyresult_2024-08-27");
//        // 使用RestHighLevelClient删除索引
//        AcknowledgedResponse acknowledgedResponse = client.indices().delete(deleteIndexRequest, RequestOptions.DEFAULT);
//        if (acknowledgedResponse.isAcknowledged()) {
//            System.out.println("索引已成功删除");
//        } else {
//            System.out.println("索引删除失败");
//        }
//        Thread.sleep(1000);
//        // 创建索引
//        CreateIndexRequest request = new CreateIndexRequest("esstrategyresult_2024-08-28");
//
//        XContentBuilder properties = XContentFactory.jsonBuilder().startObject().startObject("properties");
//        /** 获得索引mapping **/
//        Map<String, Map<String,Object>> mapping = IndexConstants.ES_DATA_SUBMIT_MAPPING.get(IndexConstants.ES_STRATEGY_RESULT);
//        /** 数据转换 **/
//        for(String field: mapping.keySet()){
//            Map<String, Object> fieldType = mapping.get(field);
//            properties.startObject(field);
//            for(Map.Entry<String,Object> entry :fieldType.entrySet()){
//                properties.field(entry.getKey(),entry.getValue());
//            }
//            properties.endObject();
//        }
//        //关闭json构造
//        properties.endObject().endObject();
//        // 可以添加设置和映射，例如：
//        request.settings(Settings.builder()
//                .put("index.number_of_shards", 6)
//                .put("index.number_of_replicas", 0)
//        );
//        //添加type和mapping并创建索引
//        request.mapping("dataVo", properties);
//
//        // 使用RestHighLevelClient创建索引
//        CreateIndexResponse createIndexResponse = client.indices().create(request, RequestOptions.DEFAULT);
//        // 检查是否创建成功
//        if (createIndexResponse.isAcknowledged()) {
//            System.out.println("索引已成功创建");
//        } else {
//            System.out.println("索引创建失败");
//        }
//        client.close();
//    }
}
