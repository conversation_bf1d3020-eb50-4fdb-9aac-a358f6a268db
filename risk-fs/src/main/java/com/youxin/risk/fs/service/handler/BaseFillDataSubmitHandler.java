package com.youxin.risk.fs.service.handler;

import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.apollo.ApolloClientAdapter;
import com.youxin.risk.commons.apollo.ApolloNamespaceEnum;
import com.youxin.risk.commons.constants.EngineDataVoPathEnum;
import com.youxin.risk.commons.constants.EngineTransformTypeEnum;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.fs.utils.Transformer;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


/**
 * @ClassName BaseFillDataSubmitHandler
 * @Description
 * <AUTHOR>
 * @Date 2022/1/11 11:04 上午
 **/
@Service
public class BaseFillDataSubmitHandler implements FillDataSubmitHandler{

    private final static Logger LOG = LoggerFactory.getLogger(BaseFillDataSubmitHandler.class);

    @Resource
    private Transformer transformer;

    public BaseFillDataSubmitHandler() {
    }

    /**
     * vulcanData
     * @param thirdPartyData
     * @param vulcan
     * @param key:用于取对应的jobId
     */
    @Override
    public void setVulcanData(String thirdPartyData, JSONObject vulcan, String key) {
        JSONObject jsonObject = HandlerUtil.getSimpleData(vulcan,key);
        String dataVoPath = jsonObject.getString("dataVoPath");
        String thirdType = jsonObject.getString("thirdType");
        /** 数仓的数据需要加工 **/
        String vulcanThirdPartyData = thirdPartVulcanData(thirdPartyData);
//        if (ApolloClientAdapter.getBooleanConfig(ApolloNamespaceEnum.FS_SPACE, "log.detail", false))
//            LoggerProxy.info("setVulcanData",LOG,"key={} vulcanThirdPartyData={}",key,vulcanThirdPartyData);
        setData(vulcan,dataVoPath,thirdType,vulcanThirdPartyData);
    }

    /**
     * 需要子类来复写
     * @param thirdPartyData
     * @return
     */
    @Override
    public String thirdPartVulcanData(String thirdPartyData) {
        return null;
    }


    /**
     * 设置数据
     * @param json
     * @param dataVoPath
     * @param thirdType
     * @param data
     */
    public void setData(JSONObject json,String dataVoPath, String thirdType, Object data){
        EngineDataVoPathEnum saveDataType = StringUtils.isBlank(dataVoPath) ? EngineDataVoPathEnum.THIRD_PARTY : EngineDataVoPathEnum.valueOf(dataVoPath);
        if(saveDataType == EngineDataVoPathEnum.CUSTOM) {
            LoggerProxy.error("saveCustom", LOG, "请自定义数据存储方法, thirdType={}", thirdType);
            throw new IllegalStateException("请请自定义数据存储方法");
        }
        switch (saveDataType) {
            case ROOT:
                json.put(thirdType,data);
                break;
            case THIRD_PARTY:
                json.getJSONObject("thirdPartyData").put(thirdType,data);
                break;
            default:
                json.getJSONObject("thirdPartyData").put(thirdType,data);
        }
    }

    public Object transform(String dataCode, String transformType, String data){
        EngineTransformTypeEnum transformTypeEnum = EngineTransformTypeEnum.valueOf(transformType);
        if(EngineTransformTypeEnum.CUSTOM == transformTypeEnum) {
            LoggerProxy.error("transformCustom", LOG, "请实现自定义转换方法, dataCode={}", dataCode);
            throw new IllegalStateException("请自定义转换数据方法");
        }
        return transformer.transform(transformTypeEnum, data);
    }
}
