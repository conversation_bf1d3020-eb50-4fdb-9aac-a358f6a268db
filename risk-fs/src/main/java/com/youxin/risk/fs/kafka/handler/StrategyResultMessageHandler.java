package com.youxin.risk.fs.kafka.handler;

import com.youxin.risk.commons.apollo.ApolloClientAdapter;
import com.youxin.risk.commons.apollo.ApolloNamespaceEnum;
import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.kafkav2.KafkaContext;
import com.youxin.risk.commons.kafkav2.handler.impl.BaseKafKaMessageHandler;
import com.youxin.risk.commons.tools.redis.RetryableJedis;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.vo.StrategyVo;
import com.youxin.risk.fs.service.DistinctEventFeatureService;
import com.youxin.risk.fs.service.StrategyResultService;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

import static com.youxin.risk.commons.apollo.ApolloNamespaceEnum.FS_SPACE;

/**
 * 消费fs发来的策略结果并保存至hbase
 * <AUTHOR>
 */
public class StrategyResultMessageHandler extends BaseKafKaMessageHandler {

    @Resource
    private StrategyResultService strategyResultService;
    @Resource
    private DistinctEventFeatureService distinctEventFeatureService;

    @Resource
    private RetryableJedis retryableJedis;

    @Override
    protected void handler0(KafkaContext context) {
        StrategyVo strategyVo;
        try {
            strategyVo = context.getMessageObject(StrategyVo.class);
            if(this.currentLimiting(strategyVo)){
                LoggerProxy.info("strategyResultMessageHandler#limit",logger,"consumer limit,userKey={}, loanKey={}, step={},eventCode={}",
                        strategyVo.getUserKey(), strategyVo.getLoanKey(), strategyVo.getStep(),strategyVo.getEventCode());
                context.setRetCode(RetCodeEnum.SUCCESS);
                return;
            };
            LoggerProxy.info(logger, "receive strategy result, userKey={}, loanKey={}, step={}", strategyVo.getUserKey(), strategyVo.getLoanKey(), strategyVo.getStep());
        } catch (Exception e) {
            LoggerProxy.error("parseMessageError", logger, "parse message to strategyVo error, message=" + context.getMessage(), e);
            context.setTerminated(true);
            return;
        }

        try {
            strategyResultService.saveStrategyResultToHbase(strategyVo);

            // 移除跨事件保存逻辑，跨事件结果目前存特征数据查询对应hbase获取
            // distinctEventFeatureService.saveDistinctEventFeatureResultToHbase(strategyVo);
            LoggerProxy.info(logger, "save strategy result to hbase success, userKey={}, loanKey={}, step={}", strategyVo.getUserKey(), strategyVo.getLoanKey(), strategyVo.getStep());
        } catch (Exception e) {
            LoggerProxy.error(logger, "save strategy result to hbase error, userKey={}, loanKey={}, step={}",
                    strategyVo.getUserKey(), strategyVo.getLoanKey(), strategyVo.getStep(), e);
        } finally {
            context.setRetCode(RetCodeEnum.SUCCESS);
        }
    }

    public boolean currentLimiting(StrategyVo strategyVo) {
        try {
            String eventCode = strategyVo.getEventCode();
            Map<String, Integer> mapConfig = ApolloClientAdapter.getMapConfig(ApolloNamespaceEnum.GW_SPACE, "batch.event.limit.map", Integer.class);
            List<String> eventCodeList = ApolloClientAdapter.getListConfig(FS_SPACE, "saveBatchToHbase.eventCode", String.class);
            if(eventCodeList.contains(eventCode) || !mapConfig.containsKey(eventCode)){
                return false;
            }
            String redisKey = "STRATEGY—SUBMIT" + eventCode + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmm"));
            long v = retryableJedis.incrOne(redisKey, 60);
            if(v > 10){
                return true;
            }
            return false;
        }catch (Exception e){
            LoggerProxy.error("strategyResultMessageHandler#currentLimiting",logger,"current limit error:",e);
        }
        return false;
    }
}
