package com.youxin.risk.fs.hbase;

import com.youxin.risk.fs.hbase.utils.HbaseUtil;
import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.hbase.HConstants;
import org.apache.hadoop.hbase.client.Get;
import org.apache.hadoop.hbase.client.Put;
import org.apache.hadoop.hbase.client.Result;
import org.apache.hadoop.hbase.client.Scan;
import org.apache.hadoop.hbase.util.Bytes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.annotation.Transient;

import java.util.Date;

/**
 * 特征结果保存
 */
public class DistinctFeatureResult implements HbaseTable {
    private static Logger log = LoggerFactory.getLogger(DistinctEventFeatureResult.class);
    private String rowKey;
    private String userKey;
    private String loanKey;
    private String event;
    private String step;
    /** 特征 **/
    private String xml;
    private Date createTime;

    /**
     * 列族
     */
    @Transient
    public String family = "f";

    @Transient
    public byte[] familyBytes = Bytes.toBytes(family);

    @Override
    public String getTableName() {
        return "feature_result";
    }

    public DistinctFeatureResult(){

    }
    /**
     * rowKey的生成
     * @param userKey
     * @param event
     * @param step
     */
    public DistinctFeatureResult(String userKey, String event, String step){
        this.rowKey = userKey+"_"+event+"_"+step;
    }

    @Override
    public Put buildPut() {
        byte[] rowkey = Bytes.toBytes(this.rowKey);
        Put put = new Put(rowkey);
        if (!StringUtils.isBlank(this.getUserKey())){
            put.addColumn(familyBytes,Bytes.toBytes("userKey"), Bytes.toBytes(this.getUserKey()));
        }
        if(!StringUtils.isBlank(this.getLoanKey())) {
            put.addColumn(familyBytes,Bytes.toBytes("loanKey"), Bytes.toBytes(this.getLoanKey()));
        }
        if (!StringUtils.isBlank(this.getEvent())) {
            put.addColumn(familyBytes,Bytes.toBytes("event"), Bytes.toBytes(this.getEvent()));
        }
        if (!StringUtils.isBlank(this.getStep())) {
            put.addColumn(familyBytes,Bytes.toBytes("step"), Bytes.toBytes(this.getStep()));
        }
        if (!StringUtils.isBlank(this.getXml())){
            put.addColumn(familyBytes,Bytes.toBytes("xml"), Bytes.toBytes(this.getXml()));
        }
        if(this.getCreateTime() != null) {
            put.addColumn(familyBytes,Bytes.toBytes("createTime"), Bytes.toBytes(this.getCreateTime().getTime()));
        }
        return put;
    }

    @Override
    public void readValue(Result value) {
        this.setUserKey(HbaseUtil.getValue(value.getValue(familyBytes, Bytes.toBytes("userKey"))));
        this.setLoanKey(HbaseUtil.getValue(value.getValue(familyBytes, Bytes.toBytes("loanKey"))));
        this.setEvent(HbaseUtil.getValue(value.getValue(familyBytes, Bytes.toBytes("event"))));
        this.setStep(HbaseUtil.getValue(value.getValue(familyBytes, Bytes.toBytes("step"))));
        this.setXml(HbaseUtil.getValue(value.getValue(familyBytes, Bytes.toBytes("xml"))));
        this.setCreateTime(HbaseUtil.getValueDate(value.getValue(familyBytes, Bytes.toBytes("createTime"))));
    }

    @Override
    public HbaseTable create() {
        return null;
    }

    @Override
    public void readSimpleValue(Result value) {

    }

    @Override
    public Get buildGet(String rowkey) {
        byte[] rowKey = Bytes.toBytes(rowkey);
        Get get = new Get(rowKey);
        get.setPriority(HConstants.HIGH_QOS);
        get.setLoadColumnFamiliesOnDemand(true);
        try {
            get.setMaxVersions(1);
        }catch (Exception e) {
            log.info("get.setMaxVersions(1) error",e);
        }
        get.addFamily(familyBytes);
        return get;
    }

    @Override
    public Scan buildScan(String rowkeyPrefix) {
        return null;
    }

    public String getUserKey() {
        return userKey;
    }

    public void setUserKey(String userKey) {
        this.userKey = userKey;
    }

    public String getLoanKey() {
        return loanKey;
    }

    public void setLoanKey(String loanKey) {
        this.loanKey = loanKey;
    }

    public String getEvent() {
        return event;
    }

    public void setEvent(String event) {
        this.event = event;
    }

    public String getStep() {
        return step;
    }

    public void setStep(String step) {
        this.step = step;
    }

    public String getXml() {
        return xml;
    }

    public void setXml(String xml) {
        this.xml = xml;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public void setRowKey(String rowKey) {
        this.rowKey = rowKey;
    }

    public String getRowKey(){
        return rowKey;
    }
}
