package com.youxin.risk.fs.experiment.feature.collector.impl;

import com.youxin.risk.commons.apollo.ApolloClientAdapter;
import com.youxin.risk.fs.experiment.feature.collector.BaseFeatureSampleCollector;
import com.youxin.risk.fs.experiment.feature.collector.SampleCollectionContext;
import com.youxin.risk.fs.experiment.feature.collector.sample.BigRangeFeatureSample;
import com.youxin.risk.fs.experiment.feature.dao.BigRangeFeatureSampleMongoDao;
import com.youxin.risk.fs.vo.PreDataSubmitVo;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RRateLimiter;
import org.redisson.api.RateIntervalUnit;
import org.redisson.api.RateType;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

import static com.youxin.risk.commons.apollo.ApolloNamespaceEnum.FS_SPACE;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class BigRangeFeatureSampleCollector extends BaseFeatureSampleCollector<BigRangeFeatureSample> {
    private static final String REDIS_KEY_PREFIX = "time-window-collect-strategy_";

    private Long rate;
    private Long rateInterval;

    @Resource
    private RedissonClient redissonClient;
    @Resource
    private BigRangeFeatureSampleMongoDao bigRangeFeatureSampleMongoDao;

    public BigRangeFeatureSampleCollector() {
        this.rate = ApolloClientAdapter.getLongConfig(FS_SPACE, "feature.sample.rate", 1L);
        this.rateInterval = ApolloClientAdapter.getLongConfig(FS_SPACE, "feature.sample.rateInterval", 5L);
    }

    @Override
    public void prepare(SampleCollectionContext context) {
        context.setSampleData(BigRangeFeatureSample.buildFrom(context.getRequestVo()));
    }

    @Override
    public void doCollect(SampleCollectionContext context) {
        prepare(context);
        RRateLimiter rateLimiter = redissonClient.getRateLimiter(REDIS_KEY_PREFIX + context.getSampleKey());
        rateLimiter.trySetRate(RateType.OVERALL, rate, rateInterval, RateIntervalUnit.MINUTES);
        if (rateLimiter.tryAcquire(5, TimeUnit.SECONDS)) {
            PreDataSubmitVo requestVo = context.getRequestVo();
            log.info("beforeSubmitPool, featureName={}, step={}, pythonVersion={}", requestVo.getFeatureName(), context.getRequestVo().getStep(), requestVo.getPythonVersion());
            bigRangeFeatureSampleMongoDao.insert(context.getSampleData());
        }
    }

    @Override
    public String toString() {
        return "BigRangeFeatureSampleCollector{" +
                "rate=" + rate +
                ", rateInterval=" + rateInterval +
                '}';
    }
}
