package com.youxin.risk.fs.tools;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.youxin.risk.commons.apollo.ApolloClientAdapter;
import com.youxin.risk.commons.kafkav2.sender.KafkaSyncSender;
import com.youxin.risk.commons.vo.StrategyVo;
import com.youxin.risk.fs.hbase.HbaseService;
import com.youxin.risk.fs.hbase.HbaseTable;
import com.youxin.risk.fs.hbase.StrategyResultHB;
import com.youxin.risk.fs.service.impl.StrategyServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.youxin.risk.commons.apollo.ApolloNamespaceEnum.FS_SPACE;

/**
 * 给数仓补偿策略结果
 *
 * <AUTHOR>
 *
 * @see com.youxin.risk.fs.service.impl.StrategyServiceImpl#strategyResultJsonSender
 */
@Slf4j
@Service
public class StrategyResultCompensationTool {
    @Autowired
    private HbaseService hbaseService;
    @Autowired
    private StrategyServiceImpl strategyServiceImpl;
    @Resource
    private RowKeyProviders rowKeyProviders;
//    @Resource(name = "strategyResultJsonMirrorSender")
//    private KafkaSyncSender strategyResultJsonMirrorSender;

    @XxlJob("reSendStrategyMessageToVulan")
    public ReturnT<String> execute(String params) {
        if (!isEnable()) {
            return ReturnT.SUCCESS;
        }
        if (StringUtils.isNotBlank(params)) {
            JSONObject jsonObject = JSON.parseObject(params);
            RowKeyProviders.RowKeyProvider rowKeyProvider = rowKeyProviders.getRowKeyProvider(jsonObject);
            if (rowKeyProvider == null) {
                log.warn("getRowKeyProviderIsNull, params={}", params);
                return ReturnT.FAIL;
            }
            List<String> rowKeys = rowKeyProvider.get();
            getDataAndSendMessage(rowKeys);
        }
        return ReturnT.SUCCESS;
    }

    private void getDataAndSendMessage(List<String> rowKeys) {
        if (CollectionUtils.isNotEmpty(rowKeys)) {
            log.info("resendStrategyMessageToVulan, need resend rowKeys={}", String.join(",", rowKeys));
            rowKeys.forEach(this::sendMessage);
        } else {
            log.info("resendStrategyMessageToVulanSuccess, getDataIsEmpty");
        }
    }

    private void sendMessage(String rowKey) {
        if (!isEnable()) {
            return;
        }

        try {
            // 默认睡10分钟
            Long sleepTime = ApolloClientAdapter.getLongConfig(FS_SPACE, "thread.sleep.time", 10 * 60L);
            TimeUnit.SECONDS.sleep(sleepTime);
        } catch (InterruptedException e) {
            log.info("resendStrategyMessageInterrupted");
            return;
        }

        StrategyResultHB strategyResult = new StrategyResultHB();
        HbaseTable table = hbaseService.queryByRowkey(rowKey, strategyResult);
        if (table == null) {
            log.info("queryByRowkeyIsNull, rowKey={}", rowKey);
            return;
        }
        StrategyVo strategyVo = transferToStrategyVo(strategyResult);
        reSendMessage(strategyVo);
    }

    private void reSendMessage(StrategyVo strategyVo) {
        try {
//            strategyResultJsonMirrorSender.send(JSON.toJSONString(strategyVo));
            log.info("resendStrategyMessageToVulanSuccess, rowKey={}, loanKey={}, message={}", strategyVo.getRowKey(), strategyVo.getLoanKey(), JSON.toJSONString(strategyVo));
        } catch (Exception e) {
            log.error("resendStrategyMessageToVulanError", e);
        }
    }

    private StrategyVo transferToStrategyVo(StrategyResultHB strategyResult) {
        StrategyVo vo = new StrategyVo();
        vo.setUserKey(strategyResult.getUserKey());
        vo.setLoanKey(strategyResult.getLoanKey());
        vo.setSourceSystem(strategyResult.getSourceSystem());
        vo.setStep(strategyResult.getStep());
        vo.setType(strategyResult.getType());
        vo.setEventCode(strategyResult.getEventCode());
        vo.setSessionId(strategyResult.getSessionId());
        vo.setStrategyCodeId(strategyResult.getStrategyCodeId());
        vo.setResult(strategyResult.getResult());
        vo.setCreateTime(strategyResult.getCreateTime());
        vo.setXml(xmlToJson(vo).getXml());
        return vo;
    }

    private StrategyVo xmlToJson(StrategyVo vo) {
        return strategyServiceImpl.getStrategyVoForJson(vo, "3");
    }

    private boolean isEnable() {
        return ApolloClientAdapter.getBooleanConfig(FS_SPACE, "enable.strategy.send.message", false);
    }
}
