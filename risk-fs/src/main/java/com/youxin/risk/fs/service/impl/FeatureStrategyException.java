package com.youxin.risk.fs.service.impl;

import com.youxin.risk.commons.constants.ErrorReasonEnum;
import lombok.Getter;

@Getter
public class FeatureStrategyException extends Exception {
    private ErrorReasonEnum errorReason;

    public FeatureStrategyException(ErrorReasonEnum errorReason, String errorMsg) {
        this(errorReason, errorMsg, null);
    }
    public FeatureStrategyException(String errorMsg, Exception e) {
        this(ErrorReasonEnum.UNKNOWN, errorMsg, e);

    }

    public FeatureStrategyException(ErrorReasonEnum errorReason, String errorMsg, Exception e) {
        super(errorMsg, e);
        this.errorReason = errorReason;
    }
}
