<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.commons.dao.verify.VerifySubmitMapper" >
    <resultMap id="BaseResultMap" type="com.youxin.risk.commons.model.verify.VerifySubmit">
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="user_key" property="userKey" jdbcType="VARCHAR" />
        <result column="operation_log_id" property="operationLogId" jdbcType="INTEGER" />
        <result column="loan_id" property="loanId" jdbcType="INTEGER" />
        <result column="loan_key" property="loanKey" jdbcType="VARCHAR" />
        <result column="loan_duration" property="loanDuration" jdbcType="INTEGER" />
        <result column="period_no" property="periodNo" jdbcType="INTEGER" />
        <result column="principal_amount" property="principalAmount" jdbcType="DOUBLE" />
        <result column="limit_amount" property="limitAmount" jdbcType="DOUBLE" />
        <result column="latitude" property="latitude" jdbcType="DOUBLE" />
        <result column="longitude" property="longitude" jdbcType="DOUBLE" />
        <result column="report_id" property="reportId" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="version" property="version" jdbcType="INTEGER" />
        <result column="wifi_ssid" property="wifiSSID" jdbcType="VARCHAR" />
        <result column="wifi_level" property="wifiLevel" jdbcType="VARCHAR" />
        <result column="wifi_mac" property="wifiMac" jdbcType="VARCHAR" />
        <result column="battery_level" property="batteryLevel" jdbcType="INTEGER" />
        <result column="battery_plug_type" property="batteryPlugType" jdbcType="VARCHAR" />
        <result column="device_name" property="deviceName" jdbcType="VARCHAR" />
        <result column="low_battery_mode" property="lowBatteryMode" jdbcType="INTEGER" />
        <result column="rong360_need_audit" property="rong360NeedAudit" jdbcType="INTEGER" />
        <result column="strategy_num" property="strategyNumber" jdbcType="VARCHAR" />
        <result column="step" property="step" jdbcType="VARCHAR" />
        <result column="verify_source" property="verifySource" jdbcType="INTEGER" />
    </resultMap>

    <sql id="table_name">
      verify_submit
    </sql>

    <sql id="Task_Column_List">
        id, user_key, operation_log_id, loan_id, loan_key, loan_duration, period_no, principal_amount,
        limit_amount, latitude, longitude, report_id, create_time, update_time, version, wifi_ssid,
        wifi_level, wifi_mac, battery_level, battery_plug_type, device_name, low_battery_mode,
        rong360_need_audit, strategy_num, step, verify_source
    </sql>

    <select id="selectVerifySubmitAfterId" resultMap="BaseResultMap" parameterType="java.util.Map">
        select
        <include refid="Task_Column_List"/>
        from verify_submit where
        <![CDATA[
        id > #{id}
        ]]>
        limit #{limit}
    </select>

    <select id="selectVerifySubmitBeforeId" resultMap="BaseResultMap" parameterType="java.util.Map">
        select
        <include refid="Task_Column_List"/>
        from verify_submit where
        <![CDATA[
        id < #{id}
        ]]>
        order by id desc
        limit #{limit}
    </select>

    <select id="findSubmitByLoanKey" resultMap="BaseResultMap">
        select
        <include refid="Task_Column_List"/>
        from verify_submit
        where loan_key = #{loanKey}
        order by id desc
        limit 1
    </select>
    <select id="findVerifySubmitByLoanKey"  resultMap="BaseResultMap">
        select
        <include refid="Task_Column_List" />
        from verify_submit
        where loan_key = #{loanKey}
        ORDER BY id DESC limit 1;
    </select>



    <insert id="persist" parameterType="com.youxin.risk.commons.model.verify.VerifySubmit">
         insert into verify_submit(        id, user_key, operation_log_id, loan_id, loan_key, loan_duration, period_no, principal_amount,        limit_amount, latitude, longitude, report_id, create_time, update_time, wifi_ssid,        wifi_level, wifi_mac, battery_level, battery_plug_type, device_name, low_battery_mode,        rong360_need_audit, strategy_num, step, verify_source
         )
         values (
        #{id},
        #{userKey},#{operationLogId},#{loanId},#{loanKey},#{loanDuration},
        #{periodNo},#{principalAmount},#{limitAmount},#{latitude},#{longitude},
        #{reportId},#{createTime},#{updateTime},#{wifiSSID},#{wifiLevel},
        #{wifiMac},#{batteryLevel},#{batteryPlugType},#{deviceName},#{lowBatteryMode},
        #{rong360NeedAudit},#{strategyNumber},#{step},#{verifySource} )
    </insert>
    <update id="update" parameterType="com.youxin.risk.commons.model.verify.VerifySubmit">
        update verify_submit
        <trim prefix="SET" suffixOverrides=",">
            <if test="userKey != null"> user_key = #{userKey},</if>
            <if test="operationLogId != null"> operation_log_id = #{operationLogId},</if>
            <if test="loanId != null"> loan_id = #{loanId},</if>
            <if test="loanKey != null"> loan_key = #{loanKey},</if>
            <if test="loanDuration != null"> loan_duration = #{loanDuration},</if>
            <if test="periodNo != null"> period_no = #{periodNo},</if>
            <if test="principalAmount != null"> principal_amount = #{principalAmount},</if>
            <if test="limitAmount != null"> limit_amount = #{limitAmount},</if>
            <if test="latitude != null"> latitude = #{latitude},</if>
            <if test="longitude != null"> longitude = #{longitude},</if>
            <if test="reportId != null"> report_id = #{reportId},</if>
            <if test="createTime != null"> create_time = #{createTime},</if>
            <if test="updateTime != null"> update_time = #{updateTime},</if>
            <if test="wifiSSID != null"> wifi_ssid = #{wifiSSID},</if>
            <if test="wifiLevel != null"> wifi_level = #{wifiLevel},</if>
            <if test="wifiMac != null"> wifi_mac = #{wifiMac},</if>
            <if test="batteryLevel != null"> battery_level = #{batteryLevel},</if>
            <if test="batteryPlugType != null"> battery_plug_type = #{batteryPlugType},</if>
            <if test="deviceName != null"> device_name = #{deviceName},</if>
            <if test="lowBatteryMode != null"> low_battery_mode = #{lowBatteryMode},</if>
            <if test="rong360NeedAudit != null"> rong360_need_audit = #{rong360NeedAudit},</if>
            <if test="strategyNumber != null"> strategy_num = #{strategyNumber},</if>
            <if test="step != null"> step = #{step},</if>
            <if test="verifySource != null"> verify_source = #{verifySource},</if>
        </trim>
        where id = #{id}
    </update>

    <select id="findVerifySubmitByLoanId" resultMap="BaseResultMap">
        select
        <include refid="Task_Column_List"/>
        from verify_submit
        WHERE loan_id =#{loanId} AND user_key =#{userKey} ORDER BY id DESC
        limit 1
    </select>

    <select id="findVerifySubmitByUserKey" resultMap="BaseResultMap">
        select * from <include refid="table_name"/>
        WHERE user_key = #{userKey} ORDER BY id DESC
    </select>
</mapper>