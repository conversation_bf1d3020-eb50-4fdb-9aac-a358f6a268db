<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.commons.dao.gw.GwMiniRequestMapper">

    <resultMap id="base_result_map" type="com.youxin.risk.commons.model.GwRequest">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="sessionId" column="session_id" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="eventCode" column="event_code" jdbcType="VARCHAR"/>
    </resultMap>

    <delete id="deleteBySessionId">
        delete from gw_request_mini where session_id=#{sessionId}
    </delete>

    <insert id="insertIgnore" parameterType="com.youxin.risk.commons.model.GwRequest">
        insert ignore into gw_request_mini
        (session_id,create_time,event_code,query_status) values
        (#{sessionId},now(),#{eventCode},1)
    </insert>

    <select id="selectByIdLimit" resultMap="base_result_map">
        SELECT id, session_id, create_time, event_code FROM gw_request_mini where id>#{id} order by id limit #{limit}
    </select>

    <delete id="deleteBySessionIdList">
        DELETE FROM gw_request_mini
        WHERE session_id IN
        <foreach collection="sessionIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
</mapper>