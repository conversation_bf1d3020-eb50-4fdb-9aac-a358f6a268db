<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.commons.dao.admin.DataSourceExperimentMapper">
    <resultMap id="BaseResultMap" type="com.youxin.risk.commons.model.DataSourceExperiment">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="exp_code" property="expCode" jdbcType="VARCHAR"/>
        <result column="exp_name" property="expName" jdbcType="VARCHAR"/>
        <result column="exp_type" property="expType" jdbcType="VARCHAR"/>
        <result column="source_system" property="sourceSystem" jdbcType="VARCHAR"/>
        <result column="service_code" property="serviceCode" jdbcType="VARCHAR"/>
        <result column="event_code" property="eventCode" jdbcType="VARCHAR"/>
        <result column="step" property="step" jdbcType="VARCHAR"/>
        <result column="exp_flow_percent" property="expFlowPercent" jdbcType="INTEGER"/>
        <result column="begin_time" property="beginTime" jdbcType="TIMESTAMP"/>
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="cmp_field" property="cmpField" jdbcType="VARCHAR"/>
        <result column="cmp_type" property="cmpType" jdbcType="VARCHAR"/>
        <result column="old_service_code" property="oldServiceCode" jdbcType="VARCHAR"/>
        <result column="new_service_code" property="newServiceCode" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,exp_code,exp_name,exp_type,service_code,source_system,event_code,step,exp_flow_percent,begin_time,end_time,status,cmp_field,cmp_type,old_service_code,new_service_code,create_time,update_time
    </sql>

    <select id="selectAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from admin_datasource_experiment
        where end_time &gt;= now()
    </select>

    <select id="selectByUpdateTime" resultMap="BaseResultMap" parameterType="java.util.Date">
        select
        <include refid="Base_Column_List"/>
        from admin_datasource_experiment
        where end_time &gt;= now()
        and update_time >= #{updateTime}
    </select>

    <select id="selectEnable" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from admin_datasource_experiment
        where end_time > now()
        and status = "ENABLE"
    </select>
</mapper>