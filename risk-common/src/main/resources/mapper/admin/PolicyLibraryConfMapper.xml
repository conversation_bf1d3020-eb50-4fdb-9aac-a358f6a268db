<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.commons.dao.admin.PolicyLibraryConfMapper">
    <resultMap id="BaseResultMap" type="com.youxin.risk.commons.model.PolicyLibraryConf">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="policy_code" jdbcType="VARCHAR" property="policyCode" />
        <result column="policy_name" jdbcType="VARCHAR" property="policyName" />
        <result column="policy_delete" jdbcType="TINYINT" property="policyDelete" />
        <result column="library_code" jdbcType="VARCHAR" property="libraryCode" />
        <result column="library_level" jdbcType="TINYINT" property="libraryLevel" />
        <result column="valid_duration" jdbcType="INTEGER" property="validDuration" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    </resultMap>

    <sql id="table_name">
      admin_policy_library_conf
    </sql>

    <sql id="save_Base_Column_List">
      policy_code, policy_name, policy_delete, library_code, library_level, valid_duration,
      create_time, update_time
    </sql>

    <sql id="Base_Column_List">
        id, <include refid="save_Base_Column_List"/>
    </sql>

    <insert id="insertReplace" parameterType="com.youxin.risk.commons.model.PolicyLibraryConf">
        replace into <include refid="table_name"/> (<include refid="save_Base_Column_List"/>) values
        (#{policyCode},#{policyName},#{policyDelete},#{libraryCode},#{libraryLevel},#{validDuration},
        now(),now())
    </insert>

     <select id="selectByPolicyCode" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from <include refid="table_name"/> where policy_code=#{policyCode}
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from <include refid="table_name"/>
        order by id
    </select>

    <select id="selectByUpdateTime" resultMap="BaseResultMap" parameterType="java.util.Date">
        select
        <include refid="Base_Column_List"/>
        from <include refid="table_name"/>
        where update_time >= #{updateTime}
    </select>

    <!--<update id="updateJodId" >
        update <include refid="table_name"/> set dp_jodid=#{dpJobId},update_time = now() where request_agency_id=#{requestAgencyId}
    </update>-->

</mapper>