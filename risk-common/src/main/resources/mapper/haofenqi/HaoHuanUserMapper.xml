<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youxin.risk.commons.dao.haofenqi.HaoHuanUserMapper">
    <!-- Result Map-->
    <resultMap id="BaseResultMap" type="com.youxin.risk.commons.model.haofenqi.HaoHuanUser">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="uid" property="uid" jdbcType="BIGINT"/>
        <result column="system_unique_id" property="systemUniqueId" jdbcType="VARCHAR"/>
        <result column="total_amount" property="totalAmount" javaType="FLOAT"/>
        <result column="created_at" property="createdAt" javaType="INTEGER"/>
        <result column="audit_time" property="auditTime" javaType="INTEGER"/>
    </resultMap>

    <!-- 查询有效用户 -->
    <select id="queryValidUser" resultMap="BaseResultMap">
		SELECT a.id,b.system_unique_id FROM user_level a,user_account b WHERE a.uid=b.uid AND a.account_status IN (1,2) AND b.deleted_at IS NULL AND a.avail_amount>0
        and <![CDATA[ id > #{startId} ]]> order by id limit #{limit}
	</select>

    <select id="queryUserByModValue" resultMap="BaseResultMap">
        select a.id,b.system_unique_id from user_level a,user_account b
        where a.uid=b.uid and a.account_status !=1  and b.deleted_at is null
        and a.uid%5=#{modV} and <![CDATA[ a.id > #{startId} ]]> order by a.id limit #{limit}
    </select>
    <select id="queryUserByModValueDesc" resultMap="BaseResultMap">
        select a.id,b.system_unique_id from user_level a,user_account b
        where a.uid=b.uid and a.account_status !=1  and b.deleted_at is null
        and a.uid%5=#{modV}
        <if test="startId != null">
        and <![CDATA[ a.id < #{startId} ]]>
        </if>
        order by a.id desc limit #{limit}
    </select>

    <select id="getUserCountByMobile" resultType="java.lang.Long">
        select count(1) from user_account  uacc
        join user_audit  uaud on uacc.uid = uaud.uid
        where uacc.account = #{account}
    </select>

    <select id="getUserAccountStatusByUid" resultType="java.util.Map">
        select account_status, updated_at from user_level
        where uid = #{uid}
        order by updated_at desc
        limit 1
    </select>

    <select id="getUserAccountByUserKey" resultType="java.lang.String">
        select uid from user_account where system_unique_id = #{userKey}
    </select>

    <select id="getAuditTimeByuUid" resultType="java.lang.Long">
        select max(audit_time) from user_audit where uid = #{uid}
    </select>

    <select id="getUserAudit" resultMap="BaseResultMap">
        select
            uid,
            total_amount,
            audit_time,
            created_at
        from user_audit
        where uid = #{uid} and audit_status = 2
        order by created_at desc limit 1;
    </select>

    <select id="getUserLevel" resultType="java.lang.String">
        <![CDATA[
        select distinct uid
        from user_level
        where id >= #{startId} and id < #{endId} and created_at < **********
        ]]>
    </select>

    <select id="getUserKeyByUids" resultType="java.lang.String">
        select system_unique_id
        from user_account
        where uid in
        <foreach collection="uids" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

</mapper>
