<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.commons.dao.channel.ChannelRequestModelMapper">
    <resultMap id="BaseResultMap" type="com.youxin.risk.commons.model.ChannelRequestModel">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="request_id" property="requestId" jdbcType="VARCHAR"/>
        <result column="user_key" property="userKey" jdbcType="VARCHAR"/>
        <result column="loan_key" property="loanKey" jdbcType="VARCHAR"/>
        <result column="request_type" property="requestType" jdbcType="VARCHAR"/>
        <result column="request_message" property="requestMessage" jdbcType="VARCHAR"/>
        <result column="response_message" property="responseMessage" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="is_lock" property="isLock"/>
        <result column="err_count" property="errCount"/>
        <result column="expire_time" property="expireTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="source_system" property="sourceSystem" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="table_name">
      channel_request
    </sql>

    <sql id="save_Base_Column_List">
    request_id, user_key, loan_key,request_type,request_message,response_message,remark,status,expire_time,is_lock,err_count,create_time,update_time,source_system
  </sql>

    <sql id="Base_Column_List">
        id, <include refid="save_Base_Column_List"/>
    </sql>

    <insert id="insertIgnore" parameterType="com.youxin.risk.commons.model.ChannelRequestModel">
        insert ignore into <include refid="table_name"/> (<include refid="save_Base_Column_List"/>) values
        (#{requestId},#{userKey},#{loanKey},#{requestType},#{requestMessage},'','','INIT',#{expireTime},0,0,now(),now(),#{sourceSystem})
    </insert>

    <!-- 查询锁超时记录 -->
    <select id="selectLockTimeout" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from <include refid="table_name"/> where
        create_time &gt;= date_add(now(), interval -1 day) and is_lock=1
        and expire_time >= now() and update_time &lt;= date_add(now(), interval -3 minute)
        order by create_time desc limit 0, 10
    </select>

    <!-- 查询待提交的记录 -->
    <select id="selectWaitSubmit" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from <include refid="table_name"/> where
        create_time &gt;= date_add(now(), interval -1 day) and create_time &lt;= date_add(now(), interval -3 minute)
        and is_lock=0 and expire_time >= now() and status in ('INIT')  order by create_time desc limit 0, 10
    </select>

    <!-- 查询待回调的记录 -->
    <select id="selectWaitCallback" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from <include refid="table_name"/> where
        create_time &gt;= date_add(now(), interval -1 day) and create_time &lt;= date_add(now(), interval -3 minute)
        and is_lock=0 and expire_time >= now() and status in ('FETCHED')  order by create_time desc limit 0, 10
    </select>

    <!-- 查询指定渠道待回调的记录 -->
    <select id="selectPointAgencyWaitCallback" resultMap="BaseResultMap">
        SELECT a.id,a.request_id,a.user_key,a.loan_key,a.request_type,a.request_message,a.response_message,
        a.remark,a.status,a.expire_time,a.is_lock,a.err_count,a.create_time,a.update_time
        from channel_request a LEFT JOIN channel_request_agency b
        on a.request_id = b.request_id where
        a.create_time &gt;= date_add(now(), interval -3 hour) and a.create_time &lt;= date_add(now(), interval -20 second)
        and a.status in ('SUBMITTED') and b.agency_code = #{agencyCode}
        and is_lock=0 and expire_time >= now() order by a.id desc limit 0, 10
    </select>

    <!-- 查询失败的记录 -->
    <select id="selectFailed" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from <include refid="table_name"/> where
        create_time &gt;= date_add(now(), interval -1 day) and create_time &lt;= date_add(now(), interval -3 minute)
        and is_lock=0 and expire_time >= now() and status in ('FAILED')  order by create_time desc limit 0, 10
    </select>

    <!-- 查询要删除的记录 查询6个月前的记录 -->
    <select id="selectDelete" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" /> FROM <include refid="table_name" /> WHERE
        create_time &lt;= DATE_ADD(NOW(), INTERVAL - 6 MONTH) ORDER BY create_time DESC LIMIT 0, 500
    </select>

    <!-- 查询卡单未回调的记录 -->
    <select id="selectNotCallback" resultType="java.util.Map">
        SELECT a.user_key userKey,a.loan_key loanKey,a.request_id requestId,
        a.request_type requestType,a.`status`,a.create_time createTime,b.agency_code agencyCode,
        b.dp_jodid jobId
        from channel_request a LEFT JOIN channel_request_agency b on a.request_id = b.request_id
        where a.create_time &gt;= date_add(now(), interval -4 hour)
        and a.create_time &lt;= date_add(now(), interval -2 hour)
        and a.status !='CALLBACKED' order by a.id desc
    </select>

    <!-- 根据requestId查询 -->
    <select id="selectByRequestId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from <include refid="table_name"/> where request_id = #{requestId}
    </select>
    
    <!-- 等待提交到dp的记录加锁 -->
    <update id="lockWaitSubmit" parameterType="java.lang.Long">
        update <include refid="table_name"/> set is_lock=1,update_time = now() where id=#{id} and is_lock=0 and status in ('INIT')
    </update>

    <!-- 失败记录加锁 -->
    <update id="lockFailed" parameterType="java.lang.Long">
        update <include refid="table_name"/> set is_lock=1,update_time = now() where id=#{id} and is_lock=0 and status in ('FAILED')
    </update>

    <!-- 待回调记录加锁 -->
    <update id="lockWaitCallback" parameterType="java.lang.Long">
        update <include refid="table_name"/> set is_lock=1,update_time = now() where id=#{id} and is_lock=0 and status in ('FETCHED')
    </update>

    <!-- 指定渠道待回调记录加锁 -->
    <update id="lockPointAgencyWaitCallback" parameterType="java.lang.Long">
        update <include refid="table_name"/> set is_lock=1,update_time = now() where id=#{id} and is_lock=0 and status in ('SUBMITTED')
    </update>

    <!-- 暂时只init状态才更新为SUBMITTED -->
    <update id="updateStatusToSubmit">
        update <include refid="table_name"/> set status='SUBMITTED',err_count=0,update_time = now() where request_id=#{requestId} and status in ('INIT')
    </update>

    <!-- 状态更新为CALLBACKED -->
    <update id="updateStatusToCallback">
        update <include refid="table_name"/> set status='CALLBACKED',err_count=0,update_time = now() where id=#{id}
    </update>

    <update id="unlock" parameterType="java.lang.Long">
        update <include refid="table_name"/> set is_lock=0,err_count=err_count+1,update_time = now() where id=#{id} and is_lock=1
    </update>

    <update id="updateStatusToFail" parameterType="com.youxin.risk.commons.model.ChannelRequestModel">
        update <include refid="table_name"/> set status='FAILED',remark=#{remark},update_time = now() where request_id = #{requestId}
    </update>

    <update id="updateStatusToFetched" parameterType="com.youxin.risk.commons.model.ChannelRequestModel">
        update <include refid="table_name"/> set status='FETCHED',response_message=#{responseMessage},update_time = now() where request_id = #{requestId}
    </update>

    <update id="updateFundPlatCallbackMsg" parameterType="com.youxin.risk.commons.model.ChannelRequestModel">
        update <include refid="table_name"/> set response_message=#{responseMessage},update_time = now() where request_id = #{requestId}
    </update>

    <delete id="deleteByIds">
        delete from <include refid="table_name"/> where id in
        <foreach collection="items" index="index" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </delete>

    <delete id="setFailedByRequestIds">
        update <include refid="table_name"/> set `status` = 'FAILED', create_time = NOW()  where request_id  in
        <foreach collection="requestIdList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <select id="selectByRequestIdsAndExpireTime" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from <include refid="table_name"/> where
        expire_time &lt;= NOW() and request_id  in
        <foreach collection="requestIdList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <update id="updateExpireTimeByRequestIds">
        update <include refid="table_name"/> set expire_time = date_add(now(), interval 30 day)  where request_id  in
        <foreach collection="requestIdList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

</mapper>