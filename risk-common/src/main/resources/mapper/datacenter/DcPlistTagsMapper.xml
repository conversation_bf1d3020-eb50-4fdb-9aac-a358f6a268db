<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youxin.risk.commons.dao.datacenter.DcPlistTagsMapper">

    <resultMap id="BaseResultMap" type="com.youxin.risk.commons.model.datacenter.DcPlistTags">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result property="appName" column="app_name" jdbcType="VARCHAR"/>
        <result property="appType" column="app_type" jdbcType="VARCHAR"/>
        <result property="exactness" column="exactness" jdbcType="VARCHAR"/>
        <result property="webSearchLoanTag" column="web_search_loan_tag" jdbcType="VARCHAR"/>
        <result property="webSearchGambleTag" column="web_search_gamble_tag" jdbcType="VARCHAR"/>
        <result property="rank" column="rank" jdbcType="VARCHAR"/>
        <result property="rnk" column="rnk" jdbcType="VARCHAR"/>
        <result property="tier1App" column="tier1_app" jdbcType="VARCHAR"/>
        <result property="tier2App" column="tier2_app" jdbcType="VARCHAR"/>
        <result property="tier3App" column="tier3_app" jdbcType="VARCHAR"/>
        <result property="tier4App" column="tier4_app" jdbcType="VARCHAR"/>
        <result property="xfjrApp" column="xfjr_app" jdbcType="VARCHAR"/>
        <result property="bankLoan" column="bank_loan" jdbcType="VARCHAR"/>
        <result property="bankCreditCard" column="bank_credit_card" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="table_name">
        risk_dc_plist_tags
    </sql>

    <sql id="Base_Column_List">
        id,app_name,app_type,exactness,web_search_loan_tag,web_search_gamble_tag,rank,rnk,tier1_app,tier2_app,tier3_app,
        tier4_app,xfjr_app,bank_loan,bank_credit_card
    </sql>

    <select id="selectAll" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from <include refid="table_name"/>
    </select>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into
        <include refid="table_name"/>
        (
        app_name,app_type,exactness,web_search_loan_tag,web_search_gamble_tag,rank,rnk,tier1_app,tier2_app,tier3_app,
        tier4_app,xfjr_app,bank_loan,bank_credit_card
        )
        values
        <foreach collection="list" item="item" index="index" separator="," close=";">
            (
            #{item.appName},
            #{item.appType},
            #{item.exactness},
            #{item.webSearchLoanTag},
            #{item.webSearchGambleTag},
            #{item.rank},
            #{item.rnk},
            #{item.tier1App},
            #{item.tier2App},
            #{item.tier3App},
            #{item.tier4App},
            #{item.xfjrApp},
            #{item.bankLoan},
            #{item.bankCreditCard}
            )
        </foreach>
    </insert>

</mapper>
