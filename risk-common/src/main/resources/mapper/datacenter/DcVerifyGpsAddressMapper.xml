<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.commons.dao.datacenter.DcVerifyGpsAddressMapper">
    <resultMap id="BaseResultMap" type="com.youxin.risk.commons.model.datacenter.DcVerifyGpsAddress">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="user_key" property="userKey" jdbcType="VARCHAR"/>
        <result column="loan_id" property="loanId" jdbcType="INTEGER"/>
        <result column="operation_log_id" property="operationLogId" jdbcType="INTEGER"/>
        <result column="latitude" property="latitude" jdbcType="DOUBLE"/>
        <result column="longitude" property="longitude" jdbcType="DOUBLE"/>
        <result column="province" property="province" jdbcType="VARCHAR"/>
        <result column="city" property="city" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="dt" property="dt" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>


    <sql id="table_name">
      dc_verify_gps_address
    </sql>

    <insert id="insert" parameterType="com.youxin.risk.commons.model.datacenter.DcVerifyGpsAddress">
        insert into dc_verify_gps_address (loan_id,user_key,operation_log_id,longitude,latitude,
                 province,city,address,create_time,update_time,dt)
        values (#{loanId,jdbcType=INTEGER}, #{userKey,jdbcType=VARCHAR},#{operationLogId,jdbcType=INTEGER},
                #{longitude,jdbcType=DOUBLE}, #{latitude,jdbcType=DOUBLE}, #{province,jdbcType=VARCHAR},
                #{city,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR},#{createTime,jdbcType=TIMESTAMP},
                #{updateTime,jdbcType=TIMESTAMP},#{dt,jdbcType=TIMESTAMP})
    </insert>


    <select id="selectByUserKey" resultMap="BaseResultMap">
        select longitude,latitude,province ,city, address from
        <include refid="table_name"/>
        where user_key = #{userKey} order by id desc limit 1
    </select>
</mapper>