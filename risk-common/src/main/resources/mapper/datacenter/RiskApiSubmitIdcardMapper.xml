<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.commons.dao.datacenter.RiskApiSubmitIdcardMapper" >

    <resultMap id="BaseResultMap" type="com.youxin.risk.commons.model.datacenter.api.RiskApiSubmitIdcard">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="apiSource" column="api_source" jdbcType="VARCHAR"/>
        <result property="sourceSystem" column="source_system" jdbcType="VARCHAR"/>
        <result property="userKey" column="user_key" jdbcType="VARCHAR"/>
        <result property="certType" column="cert_type" jdbcType="VARCHAR"/>
        <result property="idcardNumber" column="idcard_number" jdbcType="VARCHAR" typeHandler="com.youxin.risk.commons.mybatis.ApiMaskHandler"/>
        <result property="idcardName" column="idcard_name" jdbcType="VARCHAR" typeHandler="com.youxin.risk.commons.mybatis.ApiMaskHandler"/>
        <result property="idcardAddress" column="idcard_address" jdbcType="VARCHAR"/>
        <result property="sex" column="sex" jdbcType="VARCHAR"/>
        <result property="idcardNation" column="idcard_nation" jdbcType="VARCHAR"/>
        <result property="idcardIssue" column="idcard_issue" jdbcType="VARCHAR"/>
        <result property="idcardValid" column="idcard_valid" jdbcType="VARCHAR"/>
        <result property="pidPositiveUrl" column="pid_positive_url" jdbcType="VARCHAR"/>
        <result property="pidNegativeUrl" column="pid_negative_url" jdbcType="VARCHAR"/>
        <result property="assayTime" column="assay_time" jdbcType="VARCHAR"/>
        <result property="assayType" column="assay_type" jdbcType="VARCHAR"/>
        <result property="similarPoint" column="similar_point" jdbcType="VARCHAR"/>
        <result property="faceUrl" column="face_url" jdbcType="VARCHAR"/>
        <result property="facePoint" column="face_point" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,api_source,source_system,user_key,cert_type,idcard_number,
        idcard_name,idcard_address,sex,idcard_nation,idcard_issue,idcard_valid,
        pid_positive_url,pid_negative_url,assay_time,assay_type,similar_point,
        face_url,face_point,create_time,update_time
    </sql>

    <sql id="table_name">
        risk_api_submit_idcard
    </sql>

    <insert id="insert" parameterType="com.youxin.risk.commons.model.datacenter.api.RiskApiSubmitIdcard" >
        insert into <include refid="table_name"/> (
            api_source,source_system,user_key,cert_type,idcard_number,
            idcard_name,idcard_address,sex,idcard_nation,idcard_issue,idcard_valid,
            pid_positive_url,pid_negative_url,assay_time,assay_type,similar_point,face_url,face_point
        )
        values (
            #{apiSource,jdbcType=VARCHAR}, #{sourceSystem,jdbcType=VARCHAR},
            #{userKey,jdbcType=VARCHAR}, #{certType,jdbcType=VARCHAR},
            #{idcardNumber,jdbcType=VARCHAR,typeHandler=com.youxin.risk.commons.mybatis.ApiMaskHandler},
            #{idcardName,jdbcType=VARCHAR,typeHandler=com.youxin.risk.commons.mybatis.ApiMaskHandler},
            #{idcardAddress,jdbcType=VARCHAR}, #{sex,jdbcType=VARCHAR}, #{idcardNation,jdbcType=VARCHAR},
            #{idcardIssue,jdbcType=VARCHAR}, #{idcardValid,jdbcType=VARCHAR}, #{pidPositiveUrl,jdbcType=VARCHAR},
            #{pidNegativeUrl,jdbcType=VARCHAR}, #{assayTime,jdbcType=VARCHAR}, #{assayType,jdbcType=VARCHAR},
            #{similarPoint,jdbcType=VARCHAR},#{faceUrl,jdbcType=VARCHAR}, #{facePoint,jdbcType=VARCHAR}
        )
    </insert>

    <update id="updateById">
        update <include refid="table_name"/>
        set
        update_time = now()
        <if test="sourceSystem != null and sourceSystem != '' ">
            ,source_system = #{sourceSystem,jdbcType=VARCHAR}
        </if>
        <if test="certType != null and certType != '' ">
            ,cert_type = #{certType,jdbcType=VARCHAR}
        </if>
        <if test="idcardNumber != null and idcardNumber != '' ">
            ,idcard_number = #{idcardNumber,jdbcType=VARCHAR,typeHandler=com.youxin.risk.commons.mybatis.ApiMaskHandler}
        </if>
        <if test="idcardName != null and idcardName != '' ">
            ,idcard_name = #{idcardName,jdbcType=VARCHAR,typeHandler=com.youxin.risk.commons.mybatis.ApiMaskHandler}
        </if>
        <if test="idcardAddress != null and idcardAddress != '' ">
            ,idcard_address = #{idcardAddress,jdbcType=VARCHAR}
        </if>
        <if test="sex != null and sex != '' ">
            ,sex = #{sex,jdbcType=VARCHAR}
        </if>
        <if test="idcardNation != null and idcardNation != '' ">
            ,idcard_nation = #{idcardNation,jdbcType=VARCHAR}
        </if>
        <if test="idcardIssue != null and idcardIssue != '' ">
            ,idcard_issue = #{idcardIssue,jdbcType=VARCHAR}
        </if>
        <if test="idcardValid != null and idcardValid != '' ">
            ,idcard_valid = #{idcardValid,jdbcType=VARCHAR}
        </if>
        <if test="pidPositiveUrl != null and pidPositiveUrl != '' ">
            ,pid_positive_url = #{pidPositiveUrl,jdbcType=VARCHAR}
        </if>
        <if test="pidNegativeUrl != null and pidNegativeUrl != '' ">
            ,pid_negative_url = #{pidNegativeUrl,jdbcType=VARCHAR}
        </if>
        <if test="assayTime != null and assayTime != '' ">
            ,assay_time = #{assayTime,jdbcType=VARCHAR}
        </if>
        <if test="assayType != null and assayType != '' ">
            ,assay_type = #{assayType,jdbcType=VARCHAR}
        </if>
        <if test="similarPoint != null and similarPoint != '' ">
            ,similar_point = #{similarPoint,jdbcType=VARCHAR}
        </if>
        <if test="faceUrl != null and faceUrl != '' ">
            ,face_url = #{faceUrl,jdbcType=VARCHAR}
        </if>
        <if test="facePoint != null and facePoint != '' ">
            ,face_point = #{facePoint,jdbcType=VARCHAR}
        </if>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="getByUserKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from <include refid="table_name"/>
        where user_key = #{userKey}
        <if test="apiSource != null and apiSource != '' ">
            and api_source = #{apiSource}
        </if>
        order by id DESC limit 0,1
    </select>

    <select id="findByIdNum" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from <include refid="table_name"/>
        where  idcard_number = #{idcardNumber}
    </select>
    <select id="findOneByIdNum" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from <include refid="table_name"/>
        where  idcard_number = #{idcardNumber}
        ORDER BY `update_time`desc limit 1
    </select>

</mapper>