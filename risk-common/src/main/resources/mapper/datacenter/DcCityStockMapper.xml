<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youxin.risk.commons.dao.datacenter.DcCityStockMapper">

    <resultMap id="BaseResultMap" type="com.youxin.risk.commons.model.datacenter.DcCityStock">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result property="type" column="type" jdbcType="TINYINT"/>
        <result property="keyword" column="keyword" jdbcType="VARCHAR"/>
        <result property="meaning" column="meaning" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="TINYINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,type,keyword,
        meaning,status,create_time,
        update_time
    </sql>

    <sql id="table_name">
        dc_city_stock
    </sql>

    <insert id="insert" parameterType="com.youxin.risk.commons.model.datacenter.DcWordStock">
        insert into
        <include refid="table_name"/>
        (
        type,keyword,
        meaning,status
        )
        values
        (
        #{type},
        #{keyword},
        #{meaning},
        #{status}
        )
    </insert>

    <select id="selectAll" resultMap="BaseResultMap">
        select type,keyword,meaning
        from
        <include refid="table_name"/>
        where status = 1
    </select>
</mapper>
