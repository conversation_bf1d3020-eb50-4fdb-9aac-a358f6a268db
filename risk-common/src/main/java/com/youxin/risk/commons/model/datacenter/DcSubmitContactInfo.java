package com.youxin.risk.commons.model.datacenter;

import com.youxin.risk.commons.model.BaseModel;
import com.youxin.risk.commons.model.datacenter.common.VerifyCommonData;

import java.util.Date;

/**
 * verify基础数据推送联系人表
 * 
 * <AUTHOR>
 * 
 * @date 2018-10-11
 */
public class DcSubmitContactInfo extends BaseModel {

    /**
     * operation_log表id
     */
    private long operationLogId;

    /**
     * 用户key
     */
    private String userKey;

    /**
     * 关系
     */
    private String relation;

    /**
     * 联系人姓名
     */
    private String contactName;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 命中规则
     */
    private String hitRule;

    /**
     * 手机号状态及获得节点
     */
    private String statusAndNode;

    /**
     * 重新认证标志
     */
    private Boolean reCertificationFlag;

    public long getOperationLogId() {
        return operationLogId;
    }

    public void setOperationLogId(long operationLogId) {
        this.operationLogId = operationLogId;
    }

    public String getUserKey() {
        return userKey;
    }

    public void setUserKey(String userKey) {
        this.userKey = userKey == null ? null : userKey.trim();
    }

    public String getRelation() {
        return relation;
    }

    public void setRelation(String relation) {
        this.relation = relation == null ? null : relation.trim();
    }

    public String getContactName() {
        return contactName;
    }

    public void setContactName(String contactName) {
        this.contactName = contactName == null ? null : contactName.trim();
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile == null ? null : mobile.trim();
    }

    public String getHitRule() {
        return hitRule;
    }

    public void setHitRule(String hitRule) {
        this.hitRule = hitRule;
    }

    public String getStatusAndNode() {
        return statusAndNode;
    }

    public void setStatusAndNode(String statusAndNode) {
        this.statusAndNode = statusAndNode;
    }

    public Boolean getReCertificationFlag() {
        return reCertificationFlag;
    }

    public void setReCertificationFlag(Boolean reCertificationFlag) {
        this.reCertificationFlag = reCertificationFlag;
    }
}