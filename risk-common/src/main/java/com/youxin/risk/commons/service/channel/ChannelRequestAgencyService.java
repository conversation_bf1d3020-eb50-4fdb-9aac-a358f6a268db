package com.youxin.risk.commons.service.channel;

import com.youxin.risk.commons.dao.channel.ChannelRequestAgencyMapper;
import com.youxin.risk.commons.model.ChannelRequestAgency;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.retry.annotation.Retryable;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

@EnableRetry
public class ChannelRequestAgencyService {

    private static final int MAX_ATTEMPTS = 3;
    private static final long DELAY = 100L;
    private static final double MULTI_PLIER = 1;

    @Resource
    private ChannelRequestAgencyMapper channelRequestAgencyMapper;


    @Retryable(value = {Exception.class}, maxAttempts = MAX_ATTEMPTS, backoff = @Backoff(delay = DELAY, multiplier = MULTI_PLIER))
    public ChannelRequestAgency selectMaxTime(String requestId) {
        return channelRequestAgencyMapper.selectMaxTime(requestId);
    }

    @Retryable(value = {Exception.class}, maxAttempts = MAX_ATTEMPTS, backoff = @Backoff(delay = DELAY, multiplier = MULTI_PLIER))
    public ChannelRequestAgency selectByJobId(String jobId) {
        return this.channelRequestAgencyMapper.selectByJobId(jobId);
    }

    @Retryable(value = {Exception.class}, maxAttempts = MAX_ATTEMPTS, backoff = @Backoff(delay = DELAY, multiplier = MULTI_PLIER))
    public ChannelRequestAgency selectByAgencyCode(String requestId, String agencyCode) {
        return this.channelRequestAgencyMapper.selectByAgencyCode(requestId, agencyCode);
    }

    @Retryable(value = {Exception.class}, maxAttempts = MAX_ATTEMPTS, backoff = @Backoff(delay = DELAY, multiplier = MULTI_PLIER))
    public int insertOrUpdate(ChannelRequestAgency entry) {
        return channelRequestAgencyMapper.insertOrUpdate(entry);
    }

    @Retryable(value = {Exception.class}, maxAttempts = MAX_ATTEMPTS, backoff = @Backoff(delay = DELAY, multiplier = MULTI_PLIER))
    public int updateJodId(String requestAgencyId, String jodId) {
        return channelRequestAgencyMapper.updateJodId(requestAgencyId, jodId);
    }

    @Retryable(value = {Exception.class}, maxAttempts = MAX_ATTEMPTS, backoff = @Backoff(delay = DELAY, multiplier = MULTI_PLIER))
    public List<ChannelRequestAgency> selectDelete() {
        return channelRequestAgencyMapper.selectDelete();
    }

    @Retryable(value = {Exception.class}, maxAttempts = MAX_ATTEMPTS, backoff = @Backoff(delay = DELAY, multiplier = MULTI_PLIER))
    public int deleteByIds(List<ChannelRequestAgency> items) {
        if (CollectionUtils.isEmpty(items)) {
            return 0;
        }
        return channelRequestAgencyMapper.deleteByIds(items);
    }

	@Retryable(value = {Exception.class}, maxAttempts = MAX_ATTEMPTS, backoff = @Backoff(delay = DELAY, multiplier = MULTI_PLIER))
	public List<ChannelRequestAgency> selectWaitCallbackList() {
		return channelRequestAgencyMapper.selectWaitCallbackList();
	}
}
