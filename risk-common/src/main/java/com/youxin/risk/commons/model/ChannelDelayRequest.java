/*
 * Copyright (C) 2019 Baidu, Inc. All Rights Reserved.
 */
package com.youxin.risk.commons.model;

/**
 * ChannelDelayRequest(mongodb)
 */

import java.math.BigInteger;
import java.util.Date;

public class ChannelDelayRequest extends BaseEntity {

    private BigInteger id;

    private String appName;

    private String userKey;

    private String requestId;

    private String data;

    private Boolean notified;

    private Date createTime;

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getUserKey() {
        return userKey;
    }

    public void setUserKey(String userKey) {
        this.userKey = userKey;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public Boolean getNotified() {
        return notified;
    }

    public void setNotified(<PERSON>ole<PERSON> notified) {
        this.notified = notified;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
