package com.youxin.risk.commons.cacheloader.service;

import com.youxin.risk.commons.model.AlertPolicy;
import com.youxin.risk.commons.model.AlertPolicyCondition;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 字典缓存加载Service
 *
 * <AUTHOR>
 */
public interface AlertPolicyService {

    Map<String, Object> selectAllPolicy();


    Map<String, List<AlertPolicyCondition>> selectAllPolicyCondis();

    Map<String, Object> selectByUpdateTime(Date updateTime);

    Map<String, List<AlertPolicyCondition>> selectPolicyCondisByPolicyNames(List<String> policyNames);

}