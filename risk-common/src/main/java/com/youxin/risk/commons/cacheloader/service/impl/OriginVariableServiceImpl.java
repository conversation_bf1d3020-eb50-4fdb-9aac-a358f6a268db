package com.youxin.risk.commons.cacheloader.service.impl;

import com.youxin.risk.commons.cacheloader.service.OriginVariableService;
import com.youxin.risk.commons.dao.admin.OriginVariableMapper;
import com.youxin.risk.commons.model.OriginVariable;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/11/12 16:25
 */
public class OriginVariableServiceImpl implements OriginVariableService {
    @Resource
    private OriginVariableMapper originVariableMapper;

    @Override
    public List<OriginVariable> selectAll() {
        return originVariableMapper.selectAll();
    }

    @Override
    public List<OriginVariable> selectByUpdateTime(Date updateTime) {
        return originVariableMapper.selectByUpdateTime(updateTime);
    }
}
