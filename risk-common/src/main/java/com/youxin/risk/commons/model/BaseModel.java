package com.youxin.risk.commons.model;

import java.util.Date;

import com.youxin.risk.commons.constants.RetCodeEnum;
import com.youxin.risk.commons.exception.RiskRuntimeException;
import org.apache.commons.lang3.time.DateUtils;

/**
 * 数据集model对象基类
 * 
 * <AUTHOR>
 *
 */
public class BaseModel extends BaseEntity implements Comparable<BaseModel> {

    private static final long serialVersionUID = 5357966182394287632L;

    private Long id;

    private Date createTime;

    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 此处只是浅克隆，只有基本数据类型与父对象不同，写其它对象了会影响所有克隆出的所有实例，使用时需要注意
     */
    @Override
    public BaseModel clone() {
        try {
            return (BaseModel) super.clone();
        } catch (Exception e) {
            throw new RiskRuntimeException(RetCodeEnum.FAILED, e);
        }
    }

    /**
     * 下一次重试时间， 基数设置 todo,可以问下乐总他们，指数增加，1、2、4
     * @param delaySeconds 延时时间
     * @param retryCount
     * @return
     */
    protected Date calNextRetryTime(Integer delaySeconds, long retryCount) {
        double seconds = delaySeconds * Math.pow(2, retryCount);
        return DateUtils.addSeconds(new Date(), (int) seconds);
    }

    /**
     * 是否需要自动重试
     * @return
     */
    protected boolean isNeedAutoRetry(Number retryCount) {
        return retryCount.intValue() < 3;
    }

    @Override
    public int compareTo(BaseModel o) {
        return 0;
    }
}