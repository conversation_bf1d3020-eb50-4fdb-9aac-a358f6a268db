/*
 * Copyright (C) 2019 Baidu, Inc. All Rights Reserved.
 */
package com.youxin.risk.commons.constants;

public enum FeatureResult {
    TRUE {
        @Override
        public String toString() {
            return "True";
        }
    },
    FALSE {
        @Override
        public String toString() {
            return "False";
        }
    },
    INVALID {
        @Override
        public String toString() {
            return "Invalid";
        }
    },
    NULL {
        @Override
        public String toString() {
            return "None";
        }
    };
    public abstract String toString();
}
