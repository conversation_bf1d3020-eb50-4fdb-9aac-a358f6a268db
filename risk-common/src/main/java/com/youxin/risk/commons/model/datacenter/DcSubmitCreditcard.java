package com.youxin.risk.commons.model.datacenter;

import com.youxin.risk.commons.model.BaseModel;
import com.youxin.risk.commons.model.datacenter.common.VerifyCommonData;

import java.util.Date;

/**
 * verify基础数据推送信用卡表
 * 
 * <AUTHOR>
 * 
 * @date 2018-10-11
 */
public class DcSubmitCreditcard extends BaseModel {

    /**
     * operation_log表id
     */
    private Long operationLogId;

    /**
     * 用户key
     */
    private String userKey;

    /**
     * 卡号
     */
    private String cardNo;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 银行地址
     */
    private String bankAddress;

    /**
     * 预留手机号
     */
    private String reservedMobile;



    public Long getOperationLogId() {
        return operationLogId;
    }

    public void setOperationLogId(Long operationLogId) {
        this.operationLogId = operationLogId;
    }

    public String getUserKey() {
        return userKey;
    }

    public void setUserKey(String userKey) {
        this.userKey = userKey == null ? null : userKey.trim();
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo == null ? null : cardNo.trim();
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName == null ? null : bankName.trim();
    }

    public String getBankAddress() {
        return bankAddress;
    }

    public void setBankAddress(String bankAddress) {
        this.bankAddress = bankAddress == null ? null : bankAddress.trim();
    }

    public String getReservedMobile() {
        return reservedMobile;
    }

    public void setReservedMobile(String reservedMobile) {
        this.reservedMobile = reservedMobile == null ? null : reservedMobile.trim();
    }

}