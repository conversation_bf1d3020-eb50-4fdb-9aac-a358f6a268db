package com.youxin.risk.commons.health.impl;

import com.youxin.risk.commons.health.Listener;
import com.youxin.risk.commons.utils.GlobalUtil;
import com.youxin.risk.commons.utils.LoggerProxy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class DefaultListener implements Listener {

    private Logger logger = LoggerFactory.getLogger(DefaultListener.class);

    private static Listener instance;

    public static Listener getInstance() {
        if (null != instance) {
            return instance;
        }
        synchronized (DefaultListener.class) {
            if (null != instance) {
                return instance;
            }
            instance = new DefaultListener();
        }
        return instance;
    }

    @Override
    public void notify(boolean state) {
        LoggerProxy.info("stateChange", logger,
                "state={} globalRunning={}", state, GlobalUtil.isRunning());
        if (GlobalUtil.isRunning() != state) {
            GlobalUtil.setRunning(state);
        }
    }
}
