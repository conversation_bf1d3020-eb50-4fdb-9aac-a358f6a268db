package com.youxin.risk.commons.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 字符串工具类
 */
public class StringUtils extends org.apache.commons.lang3.StringUtils {

	private static final Logger LOGGER = LoggerFactory.getLogger(StringUtils.class);

	public static final String SPLIT_STR = "_";

	private static final Pattern pattern = Pattern.compile("\\s*|\t|\r|\n|\f|\\v");

	/**
	 * 判断一个集合是否为null或空集合
	 *
	 * @param c
	 * @return
	 */
	public static boolean isEmpty(Collection c) {
		if (c == null || c.size() == 0) {
			return true;
		}
		return false;
	}

	/**
	 * 判断一个map是否为null或空集合
	 *
	 * @param c
	 * @return
	 */
	public static boolean isEmpty(Map c) {
		if (c == null || c.isEmpty()) {
			return true;
		}
		return false;
	}

	/**
	 * 判断一个数组是否为null或空集合
	 *
	 * @param c
	 * @return
	 */
	public static boolean isEmpty(Object[] c) {
		if (c == null || c.length == 0) {
			return true;
		}
		return false;
	}

	/**
	 * 判断一个整形是否为null或空集合
	 *
	 * @param c
	 * @return
	 */
	public static boolean isEmpty(Integer c) {
		if (c == null) {
			return true;
		}
		return false;
	}

	/**
	 * 判断一个字符串是否为空或者0长串
	 *
	 * @param c
	 * @return
	 */
	public static boolean isEmpty(String c) {
		if (c == null || c.length() == 0) {
			return true;
		}
		return false;
	}

	/**
	 * 判断字符串的长度是否合法
	 *
	 * @param str
	 * @param minLength
	 * @param maxLength
	 * @return
	 */
	public static boolean stringLengthIsValid(String str, int minLength,
			int maxLength) {
		if (StringUtils.isEmpty(str)) {
			return false;
		}
		if (str.length() < minLength || str.length() > maxLength) {
			return false;
		}
		return true;
	}

	/**
	 * 按字符分割成列表
	 *
	 * @param str
	 * @param separatorChars
	 * @return
	 */
	public static List<String> splitToList(String str, String separatorChars) {
		String[] array = org.apache.commons.lang3.StringUtils.split(str, separatorChars);
		return Arrays.asList(array);
	}

	/**
	 * 按字符分割成列表
	 *
	 * @param str
	 * @param separatorChars
	 * @return
	 */
	public static List<Integer> splitToIntegerList(String str,
			String separatorChars) {
		String[] array = org.apache.commons.lang3.StringUtils.split(str, separatorChars);
		List<Integer> numbers = new ArrayList<Integer>();
		for (String s : array) {
			try {
				numbers.add(Integer.parseInt(s));
			} catch (Exception e) {
				e.printStackTrace();
			}
		}

		return numbers;
	}

	/**
	 * @param str
	 *            根据类的全名获取在spring中注入的id（默认约定）
	 * @return 类在spring注入的id名
	 */
	public static String getClassNameInIoc(String str) {
		if (!StringUtils.isEmpty(str)) {
			// 去掉第一个'$'开始的所有字符
			int pos = str.indexOf("$");
			if (pos > 0) {
				str = str.substring(0, pos);
			}
			// 获取最后一个'.'的位置
			pos = str.lastIndexOf(".");
			if (pos > 0) {
				// 截取包名
				String tempName = str.substring(pos + 1);
				if (tempName.length() > 1) {
					// 截取首字母
					String firstChar = tempName.substring(0, 1);
					return firstChar.toLowerCase() + tempName.substring(1);
				}
			}
		}
		return "";
	}

	/**
	 * 获取str中子串sub的个数
	 *
	 * @param str
	 * @param sub
	 * @return
	 */
	public static int countMatches(String str, String sub) {
		if (StringUtils.isEmpty(str) || StringUtils.isEmpty(sub)) {
			return 0;
		}
		int count = 0;
		int idx = 0;
		while ((idx = str.indexOf(sub, idx)) != -1) {
			count++;
			idx += sub.length();
		}
		return count;
	}

	public static String underlineToCamel(String nodeName) {
		if (StringUtils.isEmpty(nodeName)) {
			return null;
		}
		String[] strs = nodeName.split(SPLIT_STR);

		StringBuilder sb = new StringBuilder();
		sb.append(strs[0]);
		if (strs.length > 1) {
			for (int i = 1; i < strs.length; i++) {
				if (isNotEmpty(strs[i])) {
					sb.append(StringUtils.firstCharUpperCase(strs[i]));
				}
			}
		}
		return sb.toString();
	}

	// 只能支持不含数字的，含数字的字段断字有关
	public static String camelToUnderline(String nodeName) {
		if (StringUtils.isEmpty(nodeName)) {
			return null;
		}
		StringBuilder sb = new StringBuilder();
		for (int i = 0; i < nodeName.length(); i++) {
			char c = nodeName.charAt(i);
			if (c >= 'A' && c <= 'Z') {
				sb.append("_");
				sb.append(org.apache.commons.lang3.StringUtils.lowerCase(String.valueOf(c)));
			} else {
				sb.append(c);
			}
		}
		return sb.toString();
	}

	public static String firstCharUpperCase(String s) {
		StringBuilder sb = new StringBuilder();
		sb.append(Character.toUpperCase(s.charAt(0)));
		sb.append(s.substring(1));
		return sb.toString();
	}

	public static String firstCharLowerCase(String s) {
		StringBuilder sb = new StringBuilder();
		sb.append(Character.toLowerCase(s.charAt(0)));
		sb.append(s.substring(1));
		return sb.toString();
	}

	public static String removeNotNumber(String str) {
		if (str == null) {
			return str;
		}
		return str.replaceAll("[^\\d]", "");
	}

	public static boolean isNotEmojiCharacter(char codePoint){
	        return codePoint == 0x0 || codePoint == 0x9 || codePoint == 0xA
	               || codePoint == 0xD || codePoint >= 0x20 && codePoint <= 0xD7FF
	               || codePoint >= 0xE000 && codePoint <= 0xFFFD
	               || codePoint >= 0x10000 && codePoint <= 0x10FFFF;
	}

	public static boolean containsEmoji(String source) {

		if (org.apache.commons.lang3.StringUtils.isBlank(source)) {
			return false;
		}

		int len = source.length();

		for (int i = 0; i < len; i++) {
			char codePoint = source.charAt(i);
			if (!StringUtils.isNotEmojiCharacter(codePoint)) {
				return true;
			}
		}

		return false;
	}

	 /**
	  *
	    * 过滤emoji 或者 其他非文字类型的字符
	    *
	    * @param source
	    * @return
	    */
	public static String filterEmoji(String source) {

		if (!StringUtils.containsEmoji(source)) {
			return source;// 如果不包含，直接返回
		}

		StringBuilder buf = new StringBuilder();

		int len = source.length();

		for (int i = 0; i < len; i++) {
			char codePoint = source.charAt(i);

			if (StringUtils.isNotEmojiCharacter(codePoint)) {
				buf.append(codePoint);
			}

		}

		return buf.toString();
	}

    /**
     * 去除字符串中的特殊字符
     * 
     * @param source
     * @return
     */
    public static String removeIllegalCharacter(String source) {
        if (org.apache.commons.lang3.StringUtils.isBlank(source)) {
            return source;
        }
        return source.replaceAll("[^0-9a-zA-Z\u4e00-\u9fa5.，,。？“”]+", "");
    }

	public static String removeSpaces(String source) {
    	if(isBlank(source)) {
    		return source;
		}
    	return source.replaceAll("\\s+|\u00a0","");
	}

	public static Integer str2Int(String numberStr){
		if (StringUtils.isBlank(numberStr)) {
			return null;
		}
		BigDecimal decimal = new BigDecimal(numberStr);
		return decimal.intValue();
	}

	public static void main(String[] args) {
		String a = "idCardNumber";
		System.out.println(StringUtils.camelToUnderline(a));
	}

	/**
	 *  将字符串中的回车、空格、水平制表符、换行
	 * @param source
	 * @return
	 */
	public static String replaceBlank(String source){
		String target = null;
		try {
			if (source == null) {
				return source;
			}
			source = source.trim();
			if (StringUtils.isBlank(source)) {
				target = "";
			}
			Matcher m = pattern.matcher(source);
			target = m.replaceAll("");
			//这个是特殊的空格ASCII 160的空格
			if (target.contains(" ")) {
				target = target.replaceAll(" ", "");
			}
			//特殊空格ASCII 17
			if (target.contains("\u3000")) {
				target = target.replaceAll("\u3000", "");
			}
			if (target.contains("\u2029")) {
				target = target.replaceAll("\u2029", "");
			}
		}catch (Exception e){
			LoggerProxy.error("replaceBlankError",LOGGER,"sourceString="+source,e);
			return source;
		}
		return target;
	}

	public static int findFirstDiff(String a, String b) {
		if (null == a || null == b) {
			return 0;
		}
		int pos = 0;
		int shorterLength = Math.min(a.length(), b.length());
		for (int i = 0; i < shorterLength; i++) {
			if (a.charAt(i) != b.charAt(i)) {
				return i;
			}
		}
		if (a.length() != b.length()) {
			pos = shorterLength;
		}

		return pos;
	}


	/**
	 * 在source 后面拼接num个ele并返回
	 * @param sourceStr 原始字符串
	 * @param num 数量
	 * @param ele 字符串
	 * @return result
	 */
	public static String appendBlank(String sourceStr, int num,String ele) {
		StringBuilder builder = new StringBuilder(sourceStr);
		while (num != 0) {
			if ((num & 1) == 1) {
				builder.append(ele);
			}
			num = num >> 1;
			ele = doubleString(ele);
		}
		return builder.toString();
	}


	public static String doubleString(String ele) {
		return ele + ele;
	}

	public static String subString(String message, int length) {
		return isNotEmpty(message) && message.length() > length ? message.substring(0, length) : message;
	}

	public static String printStackTraceToString(Throwable t) {
		StringWriter sw = new StringWriter();
		t.printStackTrace(new PrintWriter(sw, true));
		return sw.getBuffer().toString();
	}
}