package com.youxin.risk.commons.model.datacenter.vo;

import com.youxin.risk.commons.model.BaseModel;
import com.youxin.risk.commons.model.datacenter.common.VerifyCommonData;

/**
 * verify基础数据推送社交账号表
 * 
 * <AUTHOR>
 * 
 * @date 2018-10-11
 */
public class SubmitSocialContactVo extends VerifyCommonData {

    /**
     * 用户id
     */
    private String userKey;

    /**
     * qq号
     */
    private String qq;

    /**
     * 微信号
     */
    private String wechat;




    public String getUserKey() {
        return userKey;
    }

    public void setUserKey(String userKey) {
        this.userKey = userKey == null ? null : userKey.trim();
    }

    public String getQq() {
        return qq;
    }

    public void setQq(String qq) {
        this.qq = qq == null ? null : qq.trim();
    }

    public String getWechat() {
        return wechat;
    }

    public void setWechat(String wechat) {
        this.wechat = wechat == null ? null : wechat.trim();
    }



}