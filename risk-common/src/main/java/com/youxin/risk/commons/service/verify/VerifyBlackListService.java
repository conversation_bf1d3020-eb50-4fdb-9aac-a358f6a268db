package com.youxin.risk.commons.service.verify;

import com.youxin.risk.commons.dao.verify.BlackListMapper;
import com.youxin.risk.commons.model.verify.BlackList;
import com.youxin.risk.commons.utils.CommonUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * @Desc 获取黑名单信息
 * @Auth linchongbin
 * @Date 2021/12/14 18:20
 */
public class VerifyBlackListService {

    @Resource
    private BlackListMapper blackListMapper;

    public Map<String, BlackList> getInfoByValue(Map<String, String> params) {
        Map<String, BlackList> results = new HashMap<>();
        params.forEach((indexType, value) -> {
            BlackList info = blackListMapper.getByValueAndIndexType(CommonUtils.getBlackListTableName(value), value, indexType);
            if (info != null) {
                results.put(info.getIndexType(), info);
            }
        });
        return results;
    }
}
