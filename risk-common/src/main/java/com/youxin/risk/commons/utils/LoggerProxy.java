package com.youxin.risk.commons.utils;

import org.slf4j.Logger;

/**
 * LogUtil
 *
 * <AUTHOR>
 */
public final class LoggerProxy {

    public static final String METHOD_NAME_SPLIT = "___ ";

    public static enum LoggerOnOffKey {
        logDebug
    }

    private static final boolean isOnLogDebug() {
        return isOnLogOfKey(LoggerOnOffKey.logDebug.toString());
    }

    private static final boolean isOnLogOfKey(String onOffKey) {
        // return "1".equals(CacheApi.getDictSysConfig(onOffKey));
        return false;
    }

    public static final void infoIfNecessary(Logger logger, String onOffKey, String msg) {
        // Assert.notNull(logger);
        if (isOnLogOfKey(onOffKey)) {
            logger.info(msg);
        }
    }

    public static final void infoIfNecessary(Logger logger, String onOffKey, String format, Object arg) {
        // Assert.notNull(logger);
        if (isOnLogOfKey(onOffKey)) {
            logger.info(format, arg);
        }
    }

    public static final void infoIfNecessary(Logger logger, String onOffKey, String format, Object arg1, Object arg2) {
        // Assert.notNull(logger);
        if (isOnLogOfKey(onOffKey)) {
            logger.info(format, arg1, arg2);
        }
    }

    public static final void infoIfNecessary(Logger logger, String onOffKey, String format, Object[] argArray) {
        // Assert.notNull(logger);
        if (isOnLogOfKey(onOffKey)) {
            logger.info(format, argArray);
        }
    }

    public static final void infoIfNecessary(Logger logger, String onOffKey, String msg, Throwable t) {
        // Assert.notNull(logger);
        if (isOnLogOfKey(onOffKey)) {
            logger.info(msg, t);
        }
    }

    public static final void infoIfNecessary(Logger logger, String msg) {
        // Assert.notNull(logger);
        if (isOnLogDebug()) {
            logger.info(msg);
        }
    }

    public static final void infoIfNecessary(Logger logger, String format, Object arg) {
        // Assert.notNull(logger);
        if (isOnLogDebug()) {
            logger.info(format, arg);
        }
    }

    public static final void infoIfNecessary(Logger logger, String format, Object arg1, Object arg2) {
        // Assert.notNull(logger);
        if (isOnLogDebug()) {
            logger.info(format, arg1, arg2);
        }
    }

    public static final void infoIfNecessary(Logger logger, String format, Object[] argArray) {
        // Assert.notNull(logger);
        if (isOnLogDebug()) {
            logger.info(format, argArray);
        }
    }

    public static final void infoIfNecessary(Logger logger, String msg, Throwable t) {
        // Assert.notNull(logger);
        if (isOnLogDebug()) {
            logger.info(msg, t);
        }
    }

    /**
     * Is the logger instance enabled for the DEBUG level?
     *
     * @return True if this Logger is enabled for the DEBUG level, false otherwise.
     */
    public static final boolean isDebugEnabled(Logger logger) {
        // Assert.notNull(logger);
        return logger.isDebugEnabled();
    }

    /**
     * Log a message at the DEBUG level.
     *
     * @param msg the message string to be logged
     */
    public static final void debug(Logger logger, String msg) {
        // Assert.notNull(logger);
        logger.debug(msg);
    }

    /**
     * Log a message at the DEBUG level according to the specified format and argument.
     *
     * <p>
     * This form avoids superfluous object creation when the logger is disabled for the DEBUG level.
     * </p>
     *
     * @param format the format string
     * @param arg    the argument
     */
    public static final void debug(Logger logger, String format, Object arg) {
        // Assert.notNull(logger);
        logger.debug(format, arg);
    }

    /**
     * Log a message at the DEBUG level according to the specified format and arguments.
     *
     * <p>
     * This form avoids superfluous object creation when the logger is disabled for the DEBUG level.
     * </p>
     *
     * @param format the format string
     * @param arg1   the first argument
     * @param arg2   the second argument
     */
    public static final void debug(Logger logger, String format, Object arg1, Object arg2) {
        // Assert.notNull(logger);
        logger.debug(format, arg1, arg2);
    }

    /**
     * Log a message at the DEBUG level according to the specified format and arguments.
     *
     * <p>
     * This form avoids superfluous object creation when the logger is disabled for the DEBUG level.
     * </p>
     *
     * @param format   the format string
     * @param argArray an array of arguments
     */
    public static final void debug(Logger logger, String format, Object[] argArray) {
        // Assert.notNull(logger);
        logger.debug(format, argArray);
    }

    /**
     * Log an exception (throwable) at the DEBUG level with an accompanying message.
     *
     * @param msg the message accompanying the exception
     * @param t   the exception (throwable) to log
     */
    public static final void debug(Logger logger, String msg, Throwable t) {
        // Assert.notNull(logger);
        logger.debug(msg, t);
    }

    public static void debug(String methodName, Logger logger, String format, Object... args) {
        format = "methodName=" + methodName + METHOD_NAME_SPLIT + format;
        logger.debug(format, args);
    }

    /**
     * Is the logger instance enabled for the INFO level?
     *
     * @return True if this Logger is enabled for the INFO level, false otherwise.
     */
    public static final boolean isInfoEnabled(Logger logger) {
        // Assert.notNull(logger);
        return logger.isInfoEnabled();
    }

    /**
     * Log a message at the INFO level.
     *
     * @param msg the message string to be logged
     */
    public static final void info(Logger logger, String msg) {
        // Assert.notNull(logger);
        logger.info(msg);
    }

    public static final void info(String methodName, Logger logger, String msg) {
        // Assert.notNull(logger);
        msg = "methodName=" + methodName + METHOD_NAME_SPLIT + msg;
        logger.info(msg);
    }

    /**
     * Log a message at the INFO level according to the specified format and argument.
     *
     * <p>
     * This form avoids superfluous object creation when the logger is disabled for the INFO level.
     * </p>
     *
     * @param format the format string
     * @param arg    the argument
     */
    public static final void info(Logger logger, String format, Object arg) {
        // Assert.notNull(logger);
        logger.info(format, arg);
    }

    public static final void info(String methodName, Logger logger, String format, Object arg) {
        // Assert.notNull(logger);
        format = "methodName=" + methodName + METHOD_NAME_SPLIT + format;
        logger.info(format, arg);
    }

    /**
     * Log a message at the INFO level according to the specified format and arguments.
     *
     * <p>
     * This form avoids superfluous object creation when the logger is disabled for the INFO level.
     * </p>
     *
     * @param format the format string
     * @param arg1   the first argument
     * @param arg2   the second argument
     */
    public static final void info(Logger logger, String format, Object arg1, Object arg2) {
        // Assert.notNull(logger);
        logger.info(format, arg1, arg2);
    }

    public static final void info(String methodName, Logger logger, String format, Object arg1, Object arg2) {
        // Assert.notNull(logger);
        format = "methodName=" + methodName + METHOD_NAME_SPLIT + format;
        logger.info(format, arg1, arg2);
    }

    /**
     * Log a message at the INFO level according to the specified format and arguments.
     *
     * <p>
     * This form avoids superfluous object creation when the logger is disabled for the INFO level.
     * </p>
     *
     * @param format   the format string
     * @param argArray an array of arguments
     */
    public static final void info(Logger logger, String format, Object... argArray) {
        // Assert.notNull(logger);
        logger.info(format, argArray);
    }

    public static final void info(String methodName, Logger logger, String format, Object... argArray) {
        // Assert.notNull(logger);
        format = "methodName=" + methodName + METHOD_NAME_SPLIT + format;
        logger.info(format, argArray);
    }

    private static String LOCAL_IP = SystemUtil.getLocalIp();

    public static void threadLog(Logger logger, Object type, String method) {
        boolean isLog = true;
        if (isLog) {
            String typeInfo = type == null ? "NULLTYPE" : type.getClass().toString();
            int hash = type == null ? 0 : type.hashCode();
            long threadId = Thread.currentThread().getId();
            LoggerProxy.info("threadDebug",
                    logger, "IP: {} Thread ID: {} type: {} hash: {} method: {}", LOCAL_IP, threadId, typeInfo, hash, method);
        }
    }

    /**
     * Log an exception (throwable) at the INFO level with an accompanying message.
     *
     * @param msg the message accompanying the exception
     * @param t   the exception (throwable) to log
     */
    public static final void info(Logger logger, String msg, Throwable t) {
        // Assert.notNull(logger);
        logger.info(msg, t);
    }

    public static final void info(String methodName, Logger logger, String msg, Throwable t) {
        // Assert.notNull(logger);
        msg = "methodName=" + methodName + METHOD_NAME_SPLIT + msg;
        logger.info(msg, t);
    }

    /**
     * Is the logger instance enabled for the WARN level?
     *
     * @return True if this Logger is enabled for the WARN level, false otherwise.
     */
    public static final boolean isWarnEnabled(Logger logger) {
        // Assert.notNull(logger);
        return logger.isWarnEnabled();
    }

    /**
     * Log a message at the WARN level.
     *
     * @param msg the message string to be logged
     */
    public static final void warn(Logger logger, String msg) {
        // Assert.notNull(logger);
        logger.warn(msg);
    }

    public static final void warn(String methodName, Logger logger, String msg) {
        // Assert.notNull(logger);
        msg = "methodName=" + methodName + METHOD_NAME_SPLIT + msg;
        logger.warn(msg);
    }

    /**
     * Log a message at the WARN level according to the specified format and argument.
     *
     * <p>
     * This form avoids superfluous object creation when the logger is disabled for the WARN level.
     * </p>
     *
     * @param format the format string
     * @param arg    the argument
     */
    public static final void warn(String methodName, Logger logger, String format, Object arg) {
        // Assert.notNull(logger);
        format = "methodName=" + methodName + METHOD_NAME_SPLIT + format;
        logger.warn(format, arg);
    }

    /**
     * Log a message at the WARN level according to the specified format and arguments.
     *
     * <p>
     * This form avoids superfluous object creation when the logger is disabled for the WARN level.
     * </p>
     *
     * @param format the format string
     * @param arg1   the first argument
     * @param arg2   the second argument
     */
    public static final void warn(Logger logger, String format, Object arg1, Object arg2) {
        // Assert.notNull(logger);
        logger.warn(format, arg1, arg2);
    }

    public static final void warn(String methodName, Logger logger, String format, Object arg1, Object arg2) {
        // Assert.notNull(logger);
        format = "methodName=" + methodName + METHOD_NAME_SPLIT + format;
        logger.warn(format, arg1, arg2);
    }

    /**
     * Log a message at the WARN level according to the specified format and arguments.
     *
     * <p>
     * This form avoids superfluous object creation when the logger is disabled for the WARN level.
     * </p>
     *
     * @param format   the format string
     * @param argArray an array of arguments
     */
    public static final void warn(Logger logger, String format, Object[] argArray) {
        // Assert.notNull(logger);
        logger.warn(format, argArray);
    }

    /**
     * Log a message at the WARN level according to the specified format and arguments. and add method name
     *
     * @param format    the format string
     * @param arguments a list of 3 or more arguments
     */
    public static final void warn(String methodName, Logger logger, String format, Object... arguments) {
        // Assert.notNull(logger);
        format = "methodName=" + methodName + METHOD_NAME_SPLIT + format;
        logger.warn(format, arguments);
    }

    /**
     * Log an exception (throwable) at the WARN level with an accompanying message.
     *
     * @param msg the message accompanying the exception
     * @param t   the exception (throwable) to log
     */
    public static final void warn(Logger logger, String msg, Throwable t) {
        // Assert.notNull(logger);
        logger.warn(msg, t);
    }

    public static final void warn(String methodName, Logger logger, String msg, Throwable t) {
        // Assert.notNull(logger);
        msg = "methodName=" + methodName + METHOD_NAME_SPLIT + msg;
        logger.warn(msg, t);
    }

    /**
     * Is the logger instance enabled for the ERROR level?
     *
     * @return True if this Logger is enabled for the ERROR level, false otherwise.
     */
    public static final boolean isErrorEnabled(Logger logger) {
        // Assert.notNull(logger);
        return logger.isErrorEnabled();
    }

    /**
     * Log a message at the ERROR level.
     *
     * @param msg the message string to be logged
     */
    public static final void error(Logger logger, String msg) {
        // Assert.notNull(logger);
        logger.error(msg);
    }

    /**
     * Log a message at the ERROR level according to the specified format and argument.
     *
     * <p>
     * This form avoids superfluous object creation when the logger is disabled for the ERROR level.
     * </p>
     *
     * @param format the format string
     * @param arg    the argument
     */
    public static final void error(Logger logger, String format, Object arg) {
        logger.error(format, arg);
    }

    /**
     * Log a message at the ERROR level according to the specified format and arguments.
     *
     * <p>
     * This form avoids superfluous object creation when the logger is disabled for the ERROR level.
     * </p>
     *
     * @param format the format string
     * @param arg1   the first argument
     * @param arg2   the second argument
     */
    public static final void error(Logger logger, String format, Object arg1, Object arg2) {
        logger.error(format, arg1, arg2);
    }

    /**
     * Log a message at the ERROR level according to the specified format and arguments.
     *
     * <p>
     * This form avoids superfluous object creation when the logger is disabled for the ERROR level.
     * </p>
     *
     * @param format   the format string
     * @param argArray an array of arguments
     */
    public static final void error(Logger logger, String format, Object... argArray) {
        logger.error(format, argArray);
    }

    /**
     * Log an exception (throwable) at the ERROR level with an accompanying message.
     *
     * @param msg the message accompanying the exception
     * @param t   the exception (throwable) to log
     */
    public static final void error(Logger logger, String msg, Throwable t) {
        logger.error(msg, t);
    }

    /**
     * Log a message at the ERROR level.
     *
     * @param msg the message string to be logged
     */
    public static final void error(String methodName, Logger logger, String msg) {
        // Assert.notNull(logger);
        msg = "methodName=" + methodName + METHOD_NAME_SPLIT + msg;
        logger.error(msg);
    }

    /**
     * Log a message at the ERROR level according to the specified format and argument.
     *
     * <p>
     * This form avoids superfluous object creation when the logger is disabled for the ERROR level.
     * </p>
     *
     * @param format the format string
     * @param arg    the argument
     */
    public static final void error(String methodName, Logger logger, String format, Object arg) {
        // Assert.notNull(logger);
        format = "methodName=" + methodName + METHOD_NAME_SPLIT + format;
        logger.error(format, arg);
    }

    /**
     * Log a message at the ERROR level according to the specified format and arguments.
     *
     * <p>
     * This form avoids superfluous object creation when the logger is disabled for the ERROR level.
     * </p>
     *
     * @param format the format string
     * @param arg1   the first argument
     * @param arg2   the second argument
     */
    public static final void error(String methodName, Logger logger, String format, Object arg1, Object arg2) {
        // Assert.notNull(logger);
        format = "methodName=" + methodName + METHOD_NAME_SPLIT + format;
        logger.error(format, arg1, arg2);
    }

    /**
     * Log a message at the ERROR level according to the specified format and arguments.
     *
     * <p>
     * This form avoids superfluous object creation when the logger is disabled for the ERROR level.
     * </p>
     *
     * @param format   the format string
     * @param argArray an array of arguments
     */
    public static final void error(String methodName, Logger logger, String format, Object... argArray) {
        // Assert.notNull(logger);
        format = "methodName=" + methodName + METHOD_NAME_SPLIT + format;
        logger.error(format, argArray);
    }

    /**
     * Log an exception (throwable) at the ERROR level with an accompanying message.
     *
     * @param msg the message accompanying the exception
     * @param t   the exception (throwable) to log
     */
    public static final void error(String methodName, Logger logger, String msg, Throwable t) {
        // Assert.notNull(logger);
        msg = "methodName=" + methodName + METHOD_NAME_SPLIT + msg;
        logger.error(msg, t);
    }
}