package com.youxin.risk.commons.queue.impl;

import com.youxin.risk.commons.queue.MessageConverter;
import com.youxin.risk.commons.queue.QueueMessageListener;
import com.youxin.risk.commons.tools.redis.RetryableJedis;
import com.youxin.risk.commons.utils.GlobalUtil;
import com.youxin.risk.commons.utils.SystemUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;

/**
 * redis队列消息获取
 *
 * <AUTHOR>
 */
public class RedisQueueMessageListener<T> implements QueueMessageListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(RedisQueueMessageListener.class);

    @Resource
    private RetryableJedis retryableJedis;

    private MessageConverter<T> messageConverter;

    @Override
    public T poll(Enum queueName) {
        // 系统如果非运行状态，不收取消息
        holdUntilRunning();
        // 执行take
        return doTake(queueName);
    }

    protected void holdUntilRunning() {
        // 如果系统已停止运行，则不收取消息
        while (!GlobalUtil.isRunning()) {
            SystemUtil.threadSleep(10000);
        }
    }

    protected T doTake(Enum queueName) {
        T ret = null;
        String message = null;
        try {
            // return operations.rightPop(queueName.toString(), 0, TimeUnit.SECONDS);
            // 由于BDRP平台不支持BRPOP指令，因此使用轮询方式获取消息
            message = retryableJedis.lpop(queueName.toString());
            if (StringUtils.isNotBlank(message)) {
                ret = (messageConverter != null ? messageConverter.convert(message) : (T) message);
            }
        } catch (Exception e) {
            LOGGER.error("pop and convert message exception, message:" + message, e);
        }
        return ret;
    }

    public RetryableJedis getRetryableJedis() {
        return retryableJedis;
    }

    public void setRetryableJedis(RetryableJedis retryableJedis) {
        this.retryableJedis = retryableJedis;
    }

    public MessageConverter<T> getMessageConverter() {
        return messageConverter;
    }

    public void setMessageConverter(MessageConverter<T> messageConverter) {
        this.messageConverter = messageConverter;
    }
}