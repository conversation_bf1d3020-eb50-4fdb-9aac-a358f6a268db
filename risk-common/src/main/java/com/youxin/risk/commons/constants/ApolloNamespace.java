package com.youxin.risk.commons.constants;

/**
 * <AUTHOR>
 */
public interface ApolloNamespace {
    String commonSpace = "risk-common";
    String diSpace = "risk-di";
    String alertSpace = "risk-alert";
    String fsSpace = "risk-fs";
    String ALERT_NAMESPACE = "risk-alert";
    String gwSpace = "risk-gateway";
    String engineSpace = "risk-process-engine";
    String DATACENTER_NAMESPACE = "risk-datacenter";
    String fpSpace = "risk-fp";
    String riskAllSpace = "risk-all";
}
