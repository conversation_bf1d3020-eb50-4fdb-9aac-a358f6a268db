package com.youxin.risk.commons.constants;

import java.util.Optional;

/**
 * 神策event枚举
 * <AUTHOR>
 * @since 2022/3/17 17:27
 */
public enum SensorTaskEnum {
    GET_CODE(1,"get_code"),
    COC_REPAY(2,"coc_repay");
    private Integer code;
    private String desc;

    SensorTaskEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static Optional<SensorTaskEnum> getByCode(Integer code){
        for(SensorTaskEnum taskEnum : values()){
            if(taskEnum.code.equals(code)){
                return Optional.ofNullable(taskEnum);
            }
        }
        return Optional.empty();
    }
}
