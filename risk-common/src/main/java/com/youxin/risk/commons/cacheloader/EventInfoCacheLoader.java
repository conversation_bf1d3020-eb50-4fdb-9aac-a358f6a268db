package com.youxin.risk.commons.cacheloader;

import com.google.common.collect.Maps;
import com.youxin.risk.commons.cache.CacheManager;
import com.youxin.risk.commons.cache.CacheType;
import com.youxin.risk.commons.cacheloader.service.EventInfoService;
import com.youxin.risk.commons.constants.ConfigTableEnum;
import com.youxin.risk.commons.model.EventInfo;
import com.youxin.risk.commons.model.EventInput;
import com.youxin.risk.commons.model.EventOutput;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/11/12 16:47
 */
public class EventInfoCacheLoader extends BaseCacheLoader {
    @Resource
    private EventInfoService eventInfoService;

    @Override
    @Scheduled(fixedDelay = 10000)
    public void load() {
        super.load(ConfigTableEnum.admin_event_info.toString());
    }

    @Override
    protected int loadPart() {
        List<EventInfo> eventInfos = eventInfoService.selectByUpdateTime(getCacheTime());
        if (!CollectionUtils.isEmpty(eventInfos)) {
            for (EventInfo eventInfo : eventInfos) {
                eventInfo.setInputs(eventInfoService.selectInput(eventInfo.getEventCode()));
                eventInfo.setOutputs(eventInfoService.selectOutput(eventInfo.getEventCode()));
                CacheManager.putToCache(CacheType.event_info, eventInfo.getEventCode(), eventInfo);
            }
            return eventInfos.size();
        }
        return 0;
    }

    @Override
    protected void loadAll() {
        List<EventInfo> eventInfos = eventInfoService.selectAll();
        if (CollectionUtils.isEmpty(eventInfos)) {
            return;
        }
        Map<String, List<EventInput>> inputMap = eventInfoService.selectAllInput();
        Map<String, List<EventOutput>> outputMap = eventInfoService.selectAllOutput();
        Map<String, Object> eventInfoMap = Maps.newHashMap();
        for (EventInfo eventInfo : eventInfos) {
            eventInfo.setInputs(inputMap.get(eventInfo.getEventCode()));
            eventInfo.setOutputs(outputMap.get(eventInfo.getEventCode()));
            eventInfoMap.put(eventInfo.getEventCode(), eventInfo);
        }
        CacheManager.setCache(CacheType.event_info, eventInfoMap);
    }
}
