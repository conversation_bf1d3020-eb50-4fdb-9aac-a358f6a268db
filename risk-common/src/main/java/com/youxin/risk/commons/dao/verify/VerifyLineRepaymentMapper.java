package com.youxin.risk.commons.dao.verify;

import com.youxin.risk.commons.model.verify.VerifyLineRepayment;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
public interface VerifyLineRepaymentMapper {

    VerifyLineRepayment findRecordByTransId(@Param("transId") String transId, @Param("userKey")String userKey);

    void insert(VerifyLineRepayment lineRepayment);

    void update(VerifyLineRepayment lineRepayment);

}

