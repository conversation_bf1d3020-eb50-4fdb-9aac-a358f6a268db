package com.youxin.risk.commons.dao.stat;

import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.ibatis.annotations.Param;

import com.youxin.risk.commons.model.StatService;


public interface StatEngineMapper {

    List<StatService> selectServiceInvoke(@Param("serviceCodes")List<String> serviceCodes,@Param("startTime") String startTime,@Param("endTime")String endTime);

    List<StatService> selectNodeCount(@Param("nodeCodes") Set<String> nodeCodes, @Param("startTime") String startTime, @Param("endTime")String endTime);

    String selectEventCode(@Param("nodeCode")String nodeCode,@Param("startTime") String startTime, @Param("endTime")String endTime);

}
