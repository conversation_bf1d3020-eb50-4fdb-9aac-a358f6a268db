package com.youxin.risk.commons.cacheloader;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.youxin.risk.commons.cache.CacheManager;
import com.youxin.risk.commons.cache.CacheType;
import com.youxin.risk.commons.cacheloader.service.ProcessNodeService;
import com.youxin.risk.commons.constants.ConfigTableEnum;
import com.youxin.risk.commons.constants.NodeTypeEnum;
import com.youxin.risk.commons.model.NodeDataConfig;
import com.youxin.risk.commons.model.ProcessNode;
import com.youxin.risk.commons.utils.LoggerProxy;

/**
 * 事件三方调用配置缓存加载类
 */
public class SerialDataConfigCacheLoader extends BaseCacheLoader {
    @Resource
    private ProcessNodeService processNodeService;

    @Override
    @Scheduled(fixedDelay = 10000)
    public void load() {
        super.load(ConfigTableEnum.admin_process_node.toString());
    }

    @Override
    protected int loadPart() {
        List<ProcessNode> processNodes = processNodeService.selectByUpdateTime(getCacheTime());
        if (!CollectionUtils.isEmpty(processNodes)) {
            for (ProcessNode node : processNodes) {
                NodeTypeEnum type = NodeTypeEnum.value(node.getNodeType());
                switch (type) {
                    case SERIAL:
                        CacheManager.putToCache(CacheType.process_data_config, node.getNodeCode(), processNodeService.selectNodeDataConfigs(node.getNodeCode()));
                        break;
                    default:
                        LoggerProxy.warn("nodeTypeError", logger, "node={}", JSONObject.toJSONString(node));
                        break;
                }
            }
            return processNodes.size();
        }
        return 0;
    }

    @Override
    protected void loadAll() {
        Map<String, List<NodeDataConfig>> nodeDataMap = processNodeService.selectAllNodeDataConfig();
        if (CollectionUtils.isEmpty(nodeDataMap)) {
            return;
        }
        Map<String, Object> result = Maps.newHashMap();
        result.putAll(nodeDataMap);
        CacheManager.setCache(CacheType.process_data_config, result);
    }
}
