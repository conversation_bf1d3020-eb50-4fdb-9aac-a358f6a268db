package com.youxin.risk.commons.service.di;

import com.youxin.risk.commons.constants.DiTaskStatusEnum;
import com.youxin.risk.commons.dao.di.DiTaskMapper;
import com.youxin.risk.commons.model.DiTask;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.retry.annotation.Retryable;

import javax.annotation.Resource;
import java.util.List;

@EnableRetry
public class DiTaskService {
    private static final int MAX_ATTEMPTS = 3;
    private static final long DELAY = 100L;
    private static final double MULTI_PLIER = 1;

    @Resource
    private DiTaskMapper diTaskMapper;

    @Retryable(value = {Exception.class}, maxAttempts = MAX_ATTEMPTS, backoff = @Backoff(delay = DELAY, multiplier = MULTI_PLIER))
    public void insert(List<DiTask> list) {
        this.diTaskMapper.multiInsert(list);
    }

    @Retryable(value = {Exception.class}, maxAttempts = MAX_ATTEMPTS, backoff = @Backoff(delay = DELAY, multiplier = MULTI_PLIER))
    public int updateWithStatus(DiTask diTask, DiTaskStatusEnum oldStatus) {
        return this.diTaskMapper.updateWithStatus(diTask, oldStatus.name());
    }

    @Retryable(value = {Exception.class}, maxAttempts = MAX_ATTEMPTS, backoff = @Backoff(delay = DELAY, multiplier = MULTI_PLIER))
    public int updateWithStatusList(DiTask diTask, List<String> acceptOldStatus) {
        return this.diTaskMapper.updateWithStatusList(diTask, acceptOldStatus);
    }

    @Retryable(value = {Exception.class}, maxAttempts = MAX_ATTEMPTS, backoff = @Backoff(delay = DELAY, multiplier = MULTI_PLIER))
    public DiTask getTaksByJobId(String jobId,String taskType) {
        return this.diTaskMapper.getTaksByJobId(jobId, taskType);
    }

    @Retryable(value = {Exception.class}, maxAttempts = MAX_ATTEMPTS, backoff = @Backoff(delay = DELAY, multiplier = MULTI_PLIER))
    public List<DiTask> getErrorTasks(){
        return this.diTaskMapper.getErrorTasks();
    }

    @Retryable(value = {Exception.class}, maxAttempts = MAX_ATTEMPTS, backoff = @Backoff(delay = DELAY, multiplier = MULTI_PLIER))
    public List<DiTask> getUndealTasks(List<String> userInfoTypeList){
        return this.diTaskMapper.getUndealTasks(userInfoTypeList);
    }

    @Retryable(value = {Exception.class}, maxAttempts = MAX_ATTEMPTS, backoff = @Backoff(delay = DELAY, multiplier = MULTI_PLIER))
    public List<DiTask> getUserInfoUndealTasks(List<String> userInfoTypeList){
        return this.diTaskMapper.getUserInfoUndealTasks(userInfoTypeList);
    }

    public int incrementErrCount(DiTask diTask) {
        return this.diTaskMapper.incrementErrCount(diTask);
    }
    public List<DiTask> getTasksByRequestId(String requestId){
        return this.diTaskMapper.getTasksByRequestId(requestId);
    }

    @Retryable(value = {Exception.class}, maxAttempts = MAX_ATTEMPTS, backoff = @Backoff(delay = DELAY, multiplier = MULTI_PLIER))
    public List<DiTask> getExpiredTasks(){
        return this.diTaskMapper.getExpiredTasks();
    }

    public List<Long> selectForDelete() {
        return diTaskMapper.selectForDelete();
    }

    public void delete(List<Long> idList) {
        diTaskMapper.delete(idList);
    }

    @Retryable(value = {Exception.class}, maxAttempts = MAX_ATTEMPTS, backoff = @Backoff(delay = DELAY, multiplier = MULTI_PLIER))
    public List<DiTask> getNonGetLatestUploadDataTask(int limit,int differenceFromCurrentTime) {
        return this.diTaskMapper.getNonGetLatestUploadDataTask(limit,differenceFromCurrentTime);
    }

    @Retryable(value = {Exception.class}, maxAttempts = MAX_ATTEMPTS, backoff = @Backoff(delay = DELAY, multiplier = MULTI_PLIER))
    public int updateStatusWithRequestId(String requestId, String newStatus, String oldStatus) {
        return this.diTaskMapper.updateStatusWithRequestId(requestId, newStatus, oldStatus);
    }

    @Retryable(value = {Exception.class}, maxAttempts = MAX_ATTEMPTS, backoff = @Backoff(delay = DELAY, multiplier = MULTI_PLIER))
    public int updateStatusByRequestId(String requestId, String status) {
        return this.diTaskMapper.updateStatusByRequestId(requestId, status);
    }

    public int updateJobIdById(Long id, String newJobId) {
        return diTaskMapper.updateJobIdById(id, newJobId);
    }
}
