package com.youxin.risk.commons.constants;

import org.apache.commons.lang3.StringUtils;

import java.text.MessageFormat;

/**
 * RetCodeEnum
 *
 * <AUTHOR>
 */
public enum RetCodeEnum {

    SUCCESS("S0000", "成功"),
    NO_DATA("S0001", "未获取到数据"),
    FETCHED_OLD("S0002","获取到旧数据"),

    SUSPEND_SYSVER("W1000", "系统暂停服务"),
    SERVICE_DISABLE("W1001", "服务已停用"),
    SERVICE_OVER("W1002", "服务已超量"),
    SERVICE_SUSPEND("W1003", "服务暂停使用"),
    RATE_LIMIT("W1004", "流量过大"),

    ILLEGAL_ARGUMENT("E1000", "参数非法"),

    EVENT_NOT_FOUND("E1001", "事件未找到"),

    EVENT_STATUS_ERROR("E1002", "状态非法"),

    EVENT_VERIFY_RESULT_NOT_FOUND("E1003", "事件审核结果未找到"),

    UNSAFE("E2000", "未通过安全检查"),

    UNAUTHORIZED("E3000", "未授权"),

    NOT_BIND_CARD("E4000","用户未绑卡"),

    EXISTS_SUBMIT("E4001","进件已存在"),

    SIGN_ERROR("E4002","签名错误"),

    USER_CANCEL("E4003","用户已注销"),

    USER_BANK_DIFF("E4004","用户信息与银行卡信息不一致"),

    FAILED("F1000", "处理失败"),

    HANDLE_TIMEOUT("F1001", "请求处理超时"),

    INTERFACE_EXCEPTION("F1002", "下游接口调用异常"),

    RESPONSE_EXCEPTION("F1003", "下游数据解析异常"),



    PROCESSING("P1000", "请求处理中"),

    WAITING_RETRY("R1000", "等待异步重试"),

    REQUEST_ERROR("R1001", "请求失败")
    ;

    private String value;
    private String retMsg;

    RetCodeEnum(String value, String retMsg) {
        this.value = value;
        this.retMsg = retMsg;
    }

    public String getValue() {
        return AppName.appCode() + value;
    }

    public String toString() {
        return getValue();
    }

    public static boolean isSuccess(RetCodeEnum retCode) {
        if (null == retCode) {
            return false;
        }
        return isSuccess(retCode.getValue());
    }

    public static boolean isSuccess(String retCode) {
        if (StringUtils.isBlank(retCode)) {
            return false;
        }
        return RetCodeEnum.SUCCESS.value.equals(retCode.substring(2))
                || RetCodeEnum.NO_DATA.value.equals(retCode.substring(2));
    }

    public boolean equals(String value) {
        if (StringUtils.isBlank(value)) {
            return false;
        }
        return this.value.equals(value.substring(2));
    }

    public String getRetMsg() {
        return retMsg;
    }

    public String getRetMsg(Object arg) {
        Object[] args = new Object[]{arg};
        return getRetMsg(args);
    }

    public String getRetMsg(Object arg1, Object arg2) {
        Object[] args = new Object[]{arg1, arg2};
        return getRetMsg(args);
    }

    public String getRetMsg(Object[] args) {
        Object[] nonNullArgs = args;
        StringBuilder buffer = new StringBuilder(getRetMsg());
        int size = null == args ? 0 : args.length;
        if (0 < size) {
            buffer.append(": ");
        }
        for (int i = 0; i < size; i++) {
            if (args[i] == null) {
                if (nonNullArgs == args) {
                    nonNullArgs = (Object[]) args.clone();
                }
                nonNullArgs[i] = "null";
            }
            buffer.append(" ").append("{").append(i).append("}");
        }
        return MessageFormat.format(buffer.toString(), nonNullArgs);
    }

    public static RetCodeEnum getRetCodeEnum(String str) {
        for (RetCodeEnum r : RetCodeEnum.values()) {
            if (r.getValue().equals(str) || r.getRetMsg().equals(str)) {
                return r;
            }
        }
        return null;
    }

    // retCode是否可以用来做幂等
    public static boolean isRepeatableCode(RetCodeEnum retCode) {
        if (null == retCode) {
            return false;
        }
        return RetCodeEnum.SUCCESS == retCode || RetCodeEnum.PROCESSING == retCode;
    }
}
