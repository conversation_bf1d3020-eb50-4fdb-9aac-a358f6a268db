package com.youxin.risk.commons.model.creditDriver;

import com.youxin.risk.commons.model.BaseModel;

import java.util.Date;

/**
 * 
 * 
 * <AUTHOR> 
 * @date
 */
public class CdConfig extends BaseModel {


    /**
     * 命名空间
     */
    private String nameSpaces;

    /**
     * 键
     */
    private String key;

    /**
     * 值
     */
    private String value;

    /**
     * 描述
     */
    private String description;



    /**
     * 版本号
     */
    private Integer version;

    /**
     * 操作用户
     */
    private Integer operator;

    public String getNameSpaces() {
        return nameSpaces;
    }

    public void setNameSpaces(String nameSpaces) {
        this.nameSpaces = nameSpaces == null ? null : nameSpaces.trim();
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key == null ? null : key.trim();
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value == null ? null : value.trim();
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Integer getOperator() {
        return operator;
    }

    public void setOperator(Integer operator) {
        this.operator = operator;
    }
}