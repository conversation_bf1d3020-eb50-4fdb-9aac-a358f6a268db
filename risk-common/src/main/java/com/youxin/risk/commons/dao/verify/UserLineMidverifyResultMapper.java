package com.youxin.risk.commons.dao.verify;
import org.apache.ibatis.annotations.Param;

import com.youxin.risk.commons.model.verify.UserLineMidverifyResult;

public interface UserLineMidverifyResultMapper extends  VerifyBaseMapper<UserLineMidverifyResult>{

    UserLineMidverifyResult selectBySourceSystemAndUserKey(@Param("sourceSystem")String sourceSystem,@Param("userKey")String userKey);

    UserLineMidverifyResult queryByUserKey(@Param("sourceSystem")String sourceSystem, @Param("userKey")String userKey);
}