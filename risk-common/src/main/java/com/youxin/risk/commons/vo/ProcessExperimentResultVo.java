package com.youxin.risk.commons.vo;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/1/20
 */
@Document(collection = "ProcessExperimentResult")
public class ProcessExperimentResultVo {
    @Id
    private String id;
    @Indexed(background = true)
    private String sourceSystem;
    @Indexed(background = true)
    private String expCode;
    @Indexed(background = true)
    private String userKey;
    @Indexed(background = true)
    private String loanKey;
    private Long strategyCodeId;
    private String step;
    private String request;
    private String response;
    private String status;

    private String productionResponse;

    private List<ExperimentCompareResultVo> diffResultList;

    private boolean isFinal;

//    @Indexed(expireAfterSeconds = 3600 * 24 * 7)
    private Date createTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getSourceSystem() {
        return sourceSystem;
    }

    public void setSourceSystem(String sourceSystem) {
        this.sourceSystem = sourceSystem;
    }

    public String getExpCode() {
        return expCode;
    }

    public void setExpCode(String expCode) {
        this.expCode = expCode;
    }

    public String getUserKey() {
        return userKey;
    }

    public void setUserKey(String userKey) {
        this.userKey = userKey;
    }

    public String getLoanKey() {
        return loanKey;
    }

    public void setLoanKey(String loanKey) {
        this.loanKey = loanKey;
    }

    public Long getStrategyCodeId() {
        return strategyCodeId;
    }

    public void setStrategyCodeId(Long strategyCodeId) {
        this.strategyCodeId = strategyCodeId;
    }

    public String getStep() {
        return step;
    }

    public void setStep(String step) {
        this.step = step;
    }

    public String getRequest() {
        return request;
    }

    public void setRequest(String request) {
        this.request = request;
    }

    public String getResponse() {
        return response;
    }

    public void setResponse(String response) {
        this.response = response;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public List<ExperimentCompareResultVo> getDiffResultList() {
        return diffResultList;
    }

    public void setDiffResultList(List<ExperimentCompareResultVo> diffResultList) {
        this.diffResultList = diffResultList;
    }

    public String getProductionResponse() {
        return productionResponse;
    }

    public void setProductionResponse(String productionResponse) {
        this.productionResponse = productionResponse;
    }

    public boolean isFinal() {
        return isFinal;
    }

    public void setFinal(boolean aFinal) {
        isFinal = aFinal;
    }
}
