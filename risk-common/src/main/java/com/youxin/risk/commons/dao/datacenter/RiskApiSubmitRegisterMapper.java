package com.youxin.risk.commons.dao.datacenter;

import com.youxin.risk.commons.model.datacenter.api.RiskApiSubmitRegister;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface RiskApiSubmitRegisterMapper {

    Integer insert(RiskApiSubmitRegister record);

    Integer updateById(RiskApiSubmitRegister record);

    RiskApiSubmitRegister getByUserKey(@Param("userKey") String userKey, @Param("apiSource") String apiSource);

    List<RiskApiSubmitRegister> getListByUserKey(@Param("userKey") String userKey, @Param("apiSource") String apiSource);

    RiskApiSubmitRegister getByMobile(@Param("mobile") String mobile);
}