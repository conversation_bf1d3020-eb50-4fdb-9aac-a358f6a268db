package com.youxin.risk.commons.dao.verifySharding;

import com.youxin.risk.commons.model.verify.VerifyUserLineManagement;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface VerifyUserLineManagementShardingMapper {


    VerifyUserLineManagement getByUserKey(@Param("shardingTableName") String shardingTableName, @Param("sourceSystem") String sourceSystem, @Param("userKey") String userKey);

    VerifyUserLineManagement getLastPreloanByUserKey(@Param("shardingTableName") String shardingTableName, @Param("sourceSystem") String sourceSystem, @Param("userKey") String userKey);

    VerifyUserLineManagement getAmountAssignByUserKey(@Param("shardingTableName") String shardingTableName, @Param("sourceSystem") String sourceSystem, @Param("userKey") String userKey);

    List<VerifyUserLineManagement> getLatestAmountByType(@Param("shardingTableName") String shardingTableName, @Param("sourceSystem") String sourceSystem, @Param("userKey") String userKey, @Param("strategyTypes") List strategyTypes);


    List<VerifyUserLineManagement> getListByUserKey(@Param("shardingTableName") String shardingTableName, @Param("sourceSystem") String sourceSystem, @Param("userKey") String userKey);


    /**
     * 将verify_user_line_management 中的is_active为1的设置为0
     *
     * @param userKey userkey
     * @param lineId  lineId
     */
    void updateNoInvalid(@Param("shardingTableName") String shardingTableName, @Param("userKey") String userKey, @Param("lineId") Integer lineId);

    /**
     * 保存VerifyUserLineManagement对象
     *
     * @param verifyUserLineManagement VerifyUserLineManagement 对象
     */
    Long saveUserLineManagemert(VerifyUserLineManagement verifyUserLineManagement);

    Date getFirstCreateTimeByUserKey(@Param("shardingTableName") String shardingTableName, @Param("userKey") String userKey);


    List<Map<String, Object>> getVerifyUserLineManagementMapper(@Param("shardingTableName") String shardingTableName, @Param("userKey") String businessUserKey);

    VerifyUserLineManagement getCheckVerifyUserLineManagement(@Param("shardingTableName") String shardingTableName, @Param("userKey") String businessUserKey);

    Date getLastAssignDate(@Param("shardingTableName") String shardingTableName, @Param("sourceSystem") String sourceSystem, @Param("userKey") String userKey);

    Map<String, Object> getLineDataFromVerifyUserLineManagement(@Param("shardingTableName")String shardingTableName, @Param("userKey")String businessUserKey, @Param("time") Date borrowTime);

    List<VerifyUserLineManagement> queryFirstLineDataByUserKeyList(@Param("shardingTableName")String shardingTableName, @Param("userKeyList")List<String> userKeyList);//查询每个用户的首条记录

    Date getCreateTimeByUserKey(@Param("shardingTableName") String shardingTableName, @Param("sourceSystem") String sourceSystem, @Param("userKey") String userKey);
}
