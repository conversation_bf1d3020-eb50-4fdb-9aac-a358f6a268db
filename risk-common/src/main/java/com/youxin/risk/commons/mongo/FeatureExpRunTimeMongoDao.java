package com.youxin.risk.commons.mongo;

import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.vo.FeatureExpRunTimeVo;
import com.youxin.risk.commons.vo.FeatureExperimentResultVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022-08-17
 */
public class FeatureExpRunTimeMongoDao extends BaseMongoDao{

    private static final Logger LOGGER = LoggerFactory.getLogger(FeatureExpRunTimeMongoDao.class);

    @Autowired
    @Qualifier("riskExpMongoTemplate")
    private MongoTemplate riskExpMongoTemplate;

    public FeatureExpRunTimeMongoDao() {
        collectionName = "FeatureExpRunTimeResult";
    }

    @Override
    public void insert(Object obj) {
        LoggerProxy.info("insertToFeatureExpRunTimeMongoDao", LOGGER, "insert data to mongo, collectionName={}", collectionName);
        long start = System.currentTimeMillis();
        riskExpMongoTemplate.insert(obj, collectionName);
        LOGGER.info("collectionName:{}; insertTime:{} ,insertToMongo time used:{}",collectionName,System.currentTimeMillis(),System.currentTimeMillis()-start);
    }

    public void updateFeatureExpRunTimeMongoDao(FeatureExpRunTimeVo featureExpRunTimeVo){
        try {
            Query query = new Query(Criteria.where("expCode").is(featureExpRunTimeVo.getExpCode()));
            /** 如果eventCode是:haoHuanVerify,存对应的xml文件 **/
            Update update = new Update();
            update.set("total",featureExpRunTimeVo.getTotal());
            update.set("averageRunTime",featureExpRunTimeVo.getAverageRunTime());
            update.set("lessThan500",featureExpRunTimeVo.getLessThan500());
            update.set("lessThan1000",featureExpRunTimeVo.getLessThan1000());
            update.set("lessThan2000",featureExpRunTimeVo.getLessThan2000());
            update.set("lessThan3000",featureExpRunTimeVo.getLessThan3000());
            update.set("lessThan5000",featureExpRunTimeVo.getLessThan5000());
            update.set("lessThan10000",featureExpRunTimeVo.getLessThan10000());
            update.set("moreThan10000",featureExpRunTimeVo.getMoreThan10000());
            update.set("moreThan1024Count",featureExpRunTimeVo.getMoreThan1024Count());
            update.set("failedCount",featureExpRunTimeVo.getFailedCount());
            this.riskExpMongoTemplate.upsert(query,update, collectionName);
        }catch (Exception e){
            LoggerProxy.error("updateFeatureExpRunTimeMongo",LOGGER,e.getMessage(),e);
        }
    }

//    public FeatureExpRunTimeVo findExpRunTimeVoByExpCode(String expCode){
//        Query query = new Query();
//        query.addCriteria(Criteria.where("expCode").is(expCode)).limit(1);
//        return riskExpMongoTemplate.findOne(query, FeatureExpRunTimeVo.class, collectionName);
//    }



}
