package com.youxin.risk.commons.dao.admin;

import com.youxin.risk.commons.model.EventInfo;
import com.youxin.risk.commons.model.EventInput;
import com.youxin.risk.commons.model.EventOutput;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/11/12 16:44
 */
public interface EventInfoMapper {
    List<EventInfo> selectAll();

    List<EventInfo> selectByUpdateTime(Date updateTime);

    List<EventInput> selectAllInput();

    List<EventOutput> selectAllOutput();

    List<EventInput> selectInput(String eventCode);

    List<EventOutput> selectOutput(String eventCode);

    List<EventInfo> selectAllEventName();
}
