package com.youxin.risk.commons.cacheloader;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.youxin.risk.commons.cache.CacheManager;
import com.youxin.risk.commons.cache.CacheType;
import com.youxin.risk.commons.cacheloader.service.StrategyExpNewService;
import com.youxin.risk.commons.constants.ConfigTableEnum;
import com.youxin.risk.commons.model.StrategyExp;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 新决策引擎策略实验记录缓存加载器
 */
public class StrategyExpCacheLoader extends BaseCacheLoader {
    @Resource
    private StrategyExpNewService strategyExpNewService;

    @Override
    @Scheduled(fixedDelay = 10000)
    public void load() {
//        super.load(ConfigTableEnum.admin_strategy_exp.toString());
        //部分场景更新后 没有更新表admin_sys_db_update_time 所以 loadPart 不会被调用
        loadAll();
    }

    @Override
    protected int loadPart() {
        int updateNum = 0;
        List<StrategyExp> list = strategyExpNewService.selectEnable();
        if (CollectionUtils.isEmpty(list)) {
            CacheManager.clearCache(CacheType.strategy_exp);
            return updateNum;
        }
        Map<String, Object> map = Maps.newHashMap();
        for (StrategyExp item : list) {
            if (map.containsKey(item.getOnlineStrategyCode())) {
                List<StrategyExp> valueList = (List<StrategyExp>) map.get(item.getOnlineStrategyCode());
                valueList.add(item);
            } else {
                map.put(item.getOnlineStrategyCode(), Lists.newArrayList(item));
            }
        }
        CacheManager.setCache(CacheType.strategy_exp, map);
        return updateNum;
    }

    @Override
    protected void loadAll() {
        List<StrategyExp> list = strategyExpNewService.selectEnable();
        if (CollectionUtils.isEmpty(list)) {
            CacheManager.clearCache(CacheType.strategy_exp);
            return;
        }
        Map<String, Object> map = Maps.newHashMap();
        for (StrategyExp item : list) {
            if (map.containsKey(item.getOnlineStrategyCode())) {
                List<StrategyExp> valueList = (List<StrategyExp>) map.get(item.getOnlineStrategyCode());
                valueList.add(item);
            } else {
                map.put(item.getOnlineStrategyCode(), Lists.newArrayList(item));
            }
        }
        CacheManager.setCache(CacheType.strategy_exp, map);
    }
}
