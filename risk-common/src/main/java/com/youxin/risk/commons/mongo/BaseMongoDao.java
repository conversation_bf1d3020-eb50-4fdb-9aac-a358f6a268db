package com.youxin.risk.commons.mongo;

import com.youxin.risk.commons.utils.LoggerProxy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.mongodb.core.MongoTemplate;

import java.lang.reflect.Method;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2018/10/18 10:56
 */
public abstract class BaseMongoDao {
    private static final Logger LOGGER = LoggerFactory.getLogger(BaseMongoDao.class);

    public static final int MAX_ATTEMPTS = 3;
    public static final long DELAY = 100L;
    public static final double MULTI_PLIER = 1;

    protected String collectionName;

    @Autowired
    @Qualifier("mongoTemplate")
    protected MongoTemplate template;

    public void insert(Object obj) {
        LoggerProxy.info("insertToMongo", LOGGER, "insert data to mongo, collectionName={}", collectionName);
        long start = System.currentTimeMillis();
        template.insert(obj, collectionName);
        LOGGER.info("collectionName:{}; insertTime:{} ,insertToMongo time used:{}",collectionName,System.currentTimeMillis(),System.currentTimeMillis()-start);
    }
    public MongoTemplate getTemplate() {
        return template;
    }

    public void setTemplate(MongoTemplate template) {
        this.template = template;
    }

    protected static void logCount(Object vo){
        try{
            long days;
            if(vo!=null){
                Method getCreatetime = vo.getClass().getMethod("getCreateTime");
                Date createDate = (Date)getCreatetime.invoke(vo);
                long toDay = System.currentTimeMillis();
                days = (toDay - createDate.getTime())/(24*3600*1000);
                String times = null;
                if(days == 0L){
                    times="当天";
                }
                if(days <= 7L && days > 0L){
                    times="7天之内";
                }
                if(days <= 30L && days > 7L){
                    times="7天以外30天之内";
                }
                if(days <= 60L && days > 30L){
                    times="30天之外60天以内";
                }
                if(days <= 90L && days > 60L){
                    times="60天之外90天以内";
                }
                if(days <= 180L && days > 90L){
                    times="90天以外180天以内";
                }
                if(days > 180L){
                    times="180天以外";
                }
                LoggerProxy.info(LOGGER,"countQueryCollection=risk_event,times={}",times);
            }
        }catch (Exception e){
            LoggerProxy.error("logCount",LOGGER,e.getMessage(),e);
        }
    }

}
