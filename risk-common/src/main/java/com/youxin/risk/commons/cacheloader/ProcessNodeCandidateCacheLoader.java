package com.youxin.risk.commons.cacheloader;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.youxin.risk.commons.cache.CacheManager;
import com.youxin.risk.commons.cache.CacheType;
import com.youxin.risk.commons.cacheloader.service.ProcessNodeCandidateService;
import com.youxin.risk.commons.constants.ConfigTableEnum;
import com.youxin.risk.commons.constants.NodeTypeEnum;
import com.youxin.risk.commons.model.NodeDataCandidate;
import com.youxin.risk.commons.model.NodeFeatureCandidate;
import com.youxin.risk.commons.model.NodeStrategyCandidate;
import com.youxin.risk.commons.model.ProcessNodeCandidate;
import com.youxin.risk.commons.utils.LoggerProxy;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/3/11 10:43
 */
public class ProcessNodeCandidateCacheLoader extends BaseCacheLoader {
    @Resource
    private ProcessNodeCandidateService processNodeCandidateService;

    @Override
    @Scheduled(fixedDelay = 10000)
    public void load() {
        super.load(ConfigTableEnum.admin_process_node_candidate.toString());
    }

    @Override
    protected int loadPart() {
        List<ProcessNodeCandidate> processNodeCandidates = processNodeCandidateService.selectByUpdateTime(getCacheTime());
        if (!CollectionUtils.isEmpty(processNodeCandidates)) {
            for (ProcessNodeCandidate node : processNodeCandidates) {
                NodeTypeEnum type = NodeTypeEnum.value(node.getNodeType());
                String key = node.getProcessDefId() + "_" + node.getNodeCode();
                switch (type) {
                    case DATA:
                        node.setDatas(processNodeCandidateService.selectNodeDatas(node.getProcessDefId(), node.getNodeCode()));
                        CacheManager.putToCache(CacheType.process_node_candidate, key, node);
                        break;
                    case FEATURE:
                        node.setFeatures(processNodeCandidateService.selectNodeFeatures(node.getProcessDefId(), node.getNodeCode()));
                        CacheManager.putToCache(CacheType.process_node_candidate, key, node);
                        break;
                    case STRATEGY:
                        node.setStrategy(processNodeCandidateService.selectNodeStrategy(node.getProcessDefId(), node.getNodeCode()));
                        CacheManager.putToCache(CacheType.process_node_candidate, key, node);
                        break;
                    case CUSTOM:
                        CacheManager.putToCache(CacheType.process_node_candidate, key, node);
                        break;
                    default:
                        LoggerProxy.error("nodeTypeError", logger, "node={}", JSONObject.toJSONString(node));
                        break;
                }
            }
            return processNodeCandidates.size();
        }
        return 0;
    }

    @Override
    protected void loadAll() {
        List<ProcessNodeCandidate> processNodeCandidates = processNodeCandidateService.selectAll();
        if (CollectionUtils.isEmpty(processNodeCandidates)) {
            return;
        }
        Map<String, List<NodeDataCandidate>> nodeDataMap = processNodeCandidateService.selectAllNodeData();
        Map<String, List<NodeFeatureCandidate>> nodeFeatureMap = processNodeCandidateService.selectAllNodeFeature();
        Map<String, NodeStrategyCandidate> nodeStrategyMap = processNodeCandidateService.selectAllNodeStrategy();
        Map<String, Object> processNodeMap = Maps.newHashMap();
        for (ProcessNodeCandidate node : processNodeCandidates) {
            NodeTypeEnum type = NodeTypeEnum.value(node.getNodeType());
            String key = node.getProcessDefId() + "_" + node.getNodeCode();
            switch (type) {
                case DATA:
                    node.setDatas(nodeDataMap.get(key));
                    processNodeMap.put(key, node);
                    break;
                case FEATURE:
                    node.setFeatures(nodeFeatureMap.get(key));
                    processNodeMap.put(key, node);
                    break;
                case STRATEGY:
                    node.setStrategy(nodeStrategyMap.get(key));
                    processNodeMap.put(key, node);
                    break;
                case CUSTOM:
                    processNodeMap.put(key, node);
                    break;
                default:
                    LoggerProxy.error("nodeTypeError", logger, "node={}", JSONObject.toJSONString(node));
                    break;
            }
        }
        CacheManager.setCache(CacheType.process_node_candidate, processNodeMap);
    }
}
