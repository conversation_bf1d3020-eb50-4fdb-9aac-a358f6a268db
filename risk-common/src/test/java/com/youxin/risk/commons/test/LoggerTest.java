package com.youxin.risk.commons.test;

import com.youxin.risk.commons.utils.LoggerProxy;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class LoggerTest {
    private Logger logger = LoggerFactory.getLogger(getClass());

    @Test
    public void test() {
        Exception e = new NullPointerException("haha");
        logger.error("test param1: {} param2: {}", "value1", "value2", e);
        LoggerProxy.error(logger, "test param1: {} param2: {}", "value1", "value2", e);
    }

    @Test
    public void printStackErrorTest() {
        String[] strArray = {"test1","test2"};
        try{
           String str = strArray[2];
        }catch (Exception e){
            System.out.println(e.toString());
        }
    }

    @Test
    public void errorTest(){
        Exception e = new NullPointerException("haha");
        String method = "errorTest";
        LoggerProxy.error(logger,method,e);
    }

    @Test
    public void errorLevelTest(){
        Exception e = new NullPointerException("haha");
        String method = "errorLevelTest";
        LoggerProxy.error(method,logger,"error level log",e);
        LoggerProxy.warn(method,logger,"warn level log",e);
        LoggerProxy.info(method,logger,"info level log",e);
        LoggerProxy.debug(logger,"debug level log",e);
    }
}
