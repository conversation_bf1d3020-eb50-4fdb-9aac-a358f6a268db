package com.youxin.risk.ra.service;

import com.youxin.risk.ra.model.SubmitRegister;
import com.youxin.risk.ra.vo.RaSubmitRegisterVo;

import java.util.List;

public interface SubmitRegisterService {


     RaSubmitRegisterVo getSubmitRegisterVoByUserApply(String sourceSystem, String userKey, Integer applyId);


     RaSubmitRegisterVo getLastSubmitRegisterVoByUserKey(String sourceSystem, String userKey);

     void saveSubmitRegister(SubmitRegister register);

     String getUserKeyByMobileList(String sourceSystem, List<String> mobile);


}
