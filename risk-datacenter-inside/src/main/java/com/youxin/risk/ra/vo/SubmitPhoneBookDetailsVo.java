package com.youxin.risk.ra.vo;

import com.youxin.risk.ra.annotation.ParameterNullable;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

public class SubmitPhoneBookDetailsVo {
	private String name;
	private String mobile;
	private String remark;
	private Date createTime;

	@ParameterNullable
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	@ParameterNullable
	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	@ParameterNullable
	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	@ParameterNullable
	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this,
				ToStringStyle.SHORT_PREFIX_STYLE);
	}
}
