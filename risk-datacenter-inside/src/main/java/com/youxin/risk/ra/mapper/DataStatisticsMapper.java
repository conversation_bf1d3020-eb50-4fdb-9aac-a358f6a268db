package com.youxin.risk.ra.mapper;

import com.youxin.risk.ra.enums.QueryType;
import com.youxin.risk.ra.enums.StatType;
import com.youxin.risk.ra.model.DataStatistics;
import org.apache.ibatis.annotations.Param;

public interface DataStatisticsMapper extends BaseMapper<DataStatistics> {

	 DataStatistics findDataStatisticsByQueryKey(@Param("sourceSystem") String sourceSystem, @Param("queryKey") QueryType queryKey,
													   @Param("queryValue")String queryValue, @Param("statType") StatType statType);


}
