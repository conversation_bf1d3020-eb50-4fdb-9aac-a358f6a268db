package com.youxin.risk.ra.service;

import com.youxin.risk.ra.mongo.vo.MobileCallHistoryVo;

public interface CallHistoryService {

	 MobileCallHistoryVo getCallHistoryVoByUser(String sourceSystem, String userKey, Integer taskId) throws InstantiationException, IllegalAccessException, SecurityException;

	void saveCallHistory(MobileCallHistoryVo mobileCallHistoryVo, String sourceSystem, String userKey, Integer taskId);

}
