package com.youxin.risk.ra.kafka.vo;


import com.youxin.risk.commons.utils.JsonUtils;

public class GraphBlackformMessageVo extends BaseKafkaMessage {
	private String job_id;
	private String code;
	private String system_id;
	private Long timestamp;
	private String data;
	private String application_id;

	public String getJob_id() {
		return job_id;
	}

	public void setJob_id(String job_id) {
		this.job_id = job_id;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getSystem_id() {
		return system_id;
	}

	public void setSystem_id(String system_id) {
		this.system_id = system_id;
	}

	public Long getTimestamp() {
		return timestamp;
	}

	public void setTimestamp(Long timestamp) {
		this.timestamp = timestamp;
	}

	public String getData() {
		return data;
	}

	public void setData(String data) {
		this.data = data;
	}

	public String getApplication_id() {
		return application_id;
	}

	public void setApplication_id(String application_id) {
		this.application_id = application_id;
	}

	@Override
	public void deserialize(String json) {
		GraphBlackformMessageVo msg = JsonUtils.toObject(json, GraphBlackformMessageVo.class);
		this.setJob_id(msg.getJob_id());
		this.setCode(msg.getCode());
		this.setTimestamp(msg.getTimestamp());
		this.setData(msg.getData());
		this.setSystem_id(msg.getSystem_id());
		this.setApplication_id(msg.getApplication_id());
	}

	@Override
	public String getKey() {
		return this.job_id;
	}

}
