package com.youxin.risk.ra.service;

import com.youxin.risk.commons.vo.datavo.SubmitCommonVo;
import com.youxin.risk.ra.enums.ApplyType;
import com.youxin.risk.ra.enums.DataRequestTaskType;
import com.youxin.risk.ra.vo.*;

public interface RaSubmitService {

    void submitRegister(RaSubmitRegisterVo registerVo) throws InstantiationException, IllegalAccessException, SecurityException;

    void submitAddress(RaSubmitAddressVo addressVo) throws InstantiationException, IllegalAccessException, SecurityException;

    void submitJob(RaSubmitJobVo jobVo) throws InstantiationException, IllegalAccessException, SecurityException;

    void submitIdcard(RaSubmitIdcardVo idcardVo) throws InstantiationException, IllegalAccessException, SecurityException;

    void submitContact(RaSubmitContactVo contactVo) throws InstantiationException, IllegalAccessException, SecurityException;

    void submitPhoneBook(RaSubmitPhoneBookVo phoneBookVo) throws InstantiationException, IllegalAccessException, SecurityException;


    void submitBankCard(RaSubmitBankCardVo bankCardVo) throws InstantiationException, IllegalAccessException, SecurityException;


    void submitCommonInfoWithJobId(SubmitCommonVo commonVo, ApplyType type, DataRequestTaskType taskType) throws InstantiationException, IllegalAccessException, SecurityException;

    void submitPlist(RaSubmitPlistVo plistVo) throws InstantiationException, IllegalAccessException, SecurityException;

    void submitLoansInfo(RaSubmitLoansInfoVo loansInfoVo) throws InstantiationException, IllegalAccessException, SecurityException;


}
