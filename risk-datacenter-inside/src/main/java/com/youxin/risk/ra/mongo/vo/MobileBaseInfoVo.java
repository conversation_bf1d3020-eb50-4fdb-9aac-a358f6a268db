package com.youxin.risk.ra.mongo.vo;

import org.springframework.data.mongodb.core.index.Indexed;

import java.util.Date;

public class MobileBaseInfoVo {
	/** 任务id */
	@Indexed
	private Integer taskId;
	/** 来源系统 */
	private String sourceSystem;
	/** 用户ID */
	private String userKey;
	/** 姓名 */
	private String realName;
	/** 入网时间 */
	private Date inNetDate;
	/** 用户级别 */
	private String level;
	/** 邮箱 */
	private String email;
	/** 住址 */
	private String address;
	/** 邮编 */
	private String zipCode;
	/** 手机号 */
	private String mobile;
	/** 星级得分 */
	private String starScore;
	/** 客户星级 */
	private String starLevel;
	/** 用户状态 */
	private String status;
	/** 性别 */
	private String gender;
	/** 证件类型 */
	private String certType;
	/** 证件号 */
	private String certNumber;
	/** 手机运营商 */
	private String carrierCode;
	/** 创建时间 */
	private Date createTime;
	
	private String balance;

	/** 数据来源*/
	private String dataSource;
	
	// 是否调用认证运营商 0走认证 1不走认证
	private String phoneInfoStatus;

	public String getPhoneInfoStatus() {
		return phoneInfoStatus;
	}

	public void setPhoneInfoStatus(String phoneInfoStatus) {
		this.phoneInfoStatus = phoneInfoStatus;
	}

	public Integer getTaskId() {
		return this.taskId;
	}

	public void setTaskId(Integer taskId) {
		this.taskId = taskId;
	}

	public String getSourceSystem() {
		return this.sourceSystem;
	}

	public void setSourceSystem(String sourceSystem) {
		this.sourceSystem = sourceSystem;
	}

	public String getUserKey() {
		return this.userKey;
	}

	public void setUserKey(String userKey) {
		this.userKey = userKey;
	}

	public String getRealName() {
		return this.realName;
	}

	public void setRealName(String realName) {
		this.realName = realName;
	}

	public Date getInNetDate() {
		return this.inNetDate;
	}

	public void setInNetDate(Date inNetDate) {
		this.inNetDate = inNetDate;
	}

	public String getLevel() {
		return this.level;
	}

	public void setLevel(String level) {
		this.level = level;
	}

	public String getEmail() {
		return this.email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getAddress() {
		return this.address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getZipCode() {
		return this.zipCode;
	}

	public void setZipCode(String zipCode) {
		this.zipCode = zipCode;
	}

	public String getMobile() {
		return this.mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getStarScore() {
		return this.starScore;
	}

	public void setStarScore(String starScore) {
		this.starScore = starScore;
	}

	public String getStarLevel() {
		return this.starLevel;
	}

	public void setStarLevel(String starLevel) {
		this.starLevel = starLevel;
	}

	public String getStatus() {
		return this.status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getGender() {
		return this.gender;
	}

	public void setGender(String gender) {
		this.gender = gender;
	}

	public String getCertType() {
		return this.certType;
	}

	public void setCertType(String certType) {
		this.certType = certType;
	}

	public String getCertNumber() {
		return this.certNumber;
	}

	public void setCertNumber(String certNumber) {
		this.certNumber = certNumber;
	}

	public String getCarrierCode() {
		return this.carrierCode;
	}

	public void setCarrierCode(String carrierCode) {
		this.carrierCode = carrierCode;
	}

	public Date getCreateTime() {
		return this.createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getDataSource() {
		return this.dataSource;
	}

	public void setDataSource(String dataSource) {
		this.dataSource = dataSource;
	}

    public String getBalance() {
        return balance;
    }

    public void setBalance(String balance) {
        this.balance = balance;
    }
}
