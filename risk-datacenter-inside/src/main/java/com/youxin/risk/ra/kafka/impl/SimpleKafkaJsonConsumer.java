package com.youxin.risk.ra.kafka.impl;

import com.youxin.risk.ra.kafka.IMessage;
import com.youxin.risk.ra.kafka.IMessageHandler;
import com.youxin.risk.ra.kafka.commom.BeanSerializer;
import com.youxin.risk.ra.kafka.config.DefaultKafkaConfigConstants;
import com.youxin.risk.ra.kafka.config.SimpleKafkaConfig;
import com.youxin.risk.ra.kafka.IMessage;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.Properties;


public class SimpleKafkaJsonConsumer<T extends IMessage<T>> extends AbstractTopicConsumer<T>{
	
	private static final long POLL_TIMEOUT = 1000;
	
	Logger logger = LoggerFactory.getLogger(SimpleKafkaJsonConsumer.class);
	
	KafkaConsumer<String, String> consumer = null;
	
	public SimpleKafkaJsonConsumer(SimpleKafkaConfig config, BeanSerializer<T> serializer) {
		super(serializer);
		Properties props =  createConfigProperties(config.getBootstrapServers(), config.getConsumerGroupId());
		consumer = new KafkaConsumer<String, String>(props);
		consumer.subscribe(Arrays.asList(config.getTopicName()));
	}
	
	private Properties createConfigProperties(String bootStrapServer, String groupId) {
		Properties props = new Properties();
		props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG,
				bootStrapServer == null ? DefaultKafkaConfigConstants.BOOTSTRAP_SERVERS : bootStrapServer);
	    props.put(ConsumerConfig.GROUP_ID_CONFIG, groupId == null ? DefaultKafkaConfigConstants.GROUP_ID : groupId);
	    props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, DefaultKafkaConfigConstants.ENABLE_AUTO_COMMIT);
	    props.put(ConsumerConfig.AUTO_COMMIT_INTERVAL_MS_CONFIG, DefaultKafkaConfigConstants.AUTO_COMMIT_INTERVAL_MS);
	    props.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, DefaultKafkaConfigConstants.SESSION_TIMEOUT_MS);
	    props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, DefaultKafkaConfigConstants.KEY_DESERIALIZER);
	    props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, DefaultKafkaConfigConstants.VALUE_DESERIALIZER);

		return props;
	}

	@Override
	protected void doConsume(IMessageHandler<T> handler) {
		ConsumerRecords<String, String> records = consumer.poll(POLL_TIMEOUT);
		for (ConsumerRecord<String, String> record : records) {
			//logger.info("offset = {}, key = {}, value = {}", record.offset(), record.key(), record.value());
			T t= this.serializer.deserialize(record.value());
			handler.handle(t, record.key(), record.topic());
        }
	}
}
