package com.youxin.risk.ra.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.youxin.risk.commons.dao.datacenter.DcSubmitRegisterMapper;
import com.youxin.risk.commons.model.datacenter.DcSubmitRegister;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.MaskUtils;
import com.youxin.risk.commons.utils.ObjectTransferUtils;
import com.youxin.risk.datacenter.service.impl.HfqUserInfoService;
import com.youxin.risk.ra.enums.RiskProduct;
import com.youxin.risk.ra.mapper.SubmitRegisterMapper;
import com.youxin.risk.ra.model.SubmitRegister;
import com.youxin.risk.ra.service.SubmitRegisterService;
import com.youxin.risk.ra.utils.StringUtils;
import com.youxin.risk.ra.vo.RaSubmitRegisterVo;
import com.youxin.risk.verify.service.VerifySubmitService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


@Service("raSubmitRegisterService")
public class SubmitRegisterServiceImpl implements SubmitRegisterService {

    private static Logger logger = LoggerFactory.getLogger(SubmitRegisterServiceImpl.class);

    @Autowired
    private SubmitRegisterMapper submitRegisterMapper;

    @Autowired
    DcSubmitRegisterMapper dcSubmitRegisterMapper;


    @Resource
    private HfqUserInfoService hfqUserInfoService;

    @Autowired
    @Qualifier("verifySubmitServImpl")
    private VerifySubmitService verifySubmitService;

    @Override
    public RaSubmitRegisterVo getSubmitRegisterVoByUserApply(String sourceSystem,
                                                             String userKey, Integer applyId) {
        RaSubmitRegisterVo raSubmitRegisterVo = null;
        SubmitRegister submitRegister = this.submitRegisterMapper.findByApplyId(applyId);
        if (null == submitRegister) {
            try {
                //调用好分期接口获取用户信息
                JSON userInfo = hfqUserInfoService.getUserInfo(userKey);

                raSubmitRegisterVo = ObjectTransferUtils.transferObject(userInfo, RaSubmitRegisterVo.class);

                LoggerProxy.info("return lost rePoll raSubmitRegister", logger, "userKey={}", userKey);

            } catch (Exception e) {
                LoggerProxy.error("lost raRegister insert error", logger, "userKey={}", userKey, e);
            }
        }
        if (submitRegister != null) {
            try {
                raSubmitRegisterVo = ObjectTransferUtils.transferObject(submitRegister, RaSubmitRegisterVo.class);
            } catch (Exception e) {
                logger.error("transfer model to vo error");
            }
        }
        return raSubmitRegisterVo;
    }


    @Override
    public RaSubmitRegisterVo getLastSubmitRegisterVoByUserKey(String sourceSystem, String userKey) {
        DcSubmitRegister dcRegister = dcSubmitRegisterMapper.getByUserKey(userKey);
        if (dcRegister == null) {
            logger.info("getLastSubmitRegisterVoByUserKey dc not exist, userKey: {}, sourceSystem: {}", userKey, sourceSystem);
            SubmitRegister raRegister = this.submitRegisterMapper.findLastSubmitByUserKey(userKey, sourceSystem);
            try {
                return ObjectTransferUtils.transferObject(raRegister, RaSubmitRegisterVo.class);
            } catch (Exception e) {
                logger.error("raRegister transfer error", e);
                return null;
            }
        }

        try {
            final RaSubmitRegisterVo raSubmitRegisterVo = ObjectTransferUtils.transferObject(dcRegister, RaSubmitRegisterVo.class);
            raSubmitRegisterVo.setSourceSystem(sourceSystem);
            return raSubmitRegisterVo;
        } catch (Exception e) {
            logger.error("dcRegister transfer error", e);
            return null;
        }
    }

    @Override
    public String getUserKeyByMobileList(String sourceSystem, List<String> mobile) {
        if (mobile == null || mobile.isEmpty()) {
            return null;
        }
        // ra_register mobile字段进行脱敏，所以查询时需要兼容加密后手机号
        List<String> allMobiles = Lists.newArrayList();
        for (String mobileStr : mobile) {
            allMobiles.add(MaskUtils.unMaskValue(mobileStr));
            allMobiles.add(MaskUtils.maskValue(mobileStr));
        }
        String userKey = dcSubmitRegisterMapper.getUserKeyByMobileList(allMobiles);
        if (StringUtils.isBlank(userKey)) {
            logger.info("getUserKeyByMobileList dc not exist, mobile: {}, sourceSystem: {}", allMobiles, sourceSystem);
            userKey = submitRegisterMapper.getUserKeyByMobileList(RiskProduct.valueOf(sourceSystem).getBindSourceSystem(), allMobiles);
        }
        return userKey;
    }

    @Override
    public void saveSubmitRegister(SubmitRegister register) {
        this.submitRegisterMapper.insert(register);
    }

}
