package com.youxin.risk.ra.model;


import com.youxin.risk.ra.enums.*;
import com.youxin.risk.ra.timeinterface.CreateTimeSetter;
import com.youxin.risk.ra.timeinterface.UpdateTimeSetter;

import java.util.Date;


public class DataRequestTask implements CreateTimeSetter, UpdateTimeSetter {
	/** 自增id */
	private Integer id;
	/** ra_apply表id*/
	private Integer applyId;
	/** 来源系统 */
	private String sourceSystem;

	/** 任务类型(淘宝、通话详单) */
	private DataRequestTaskType taskType;

	/** 是否强制获取，默认为false */
	private Boolean force;

	/** 用户ID */
	private String userKey;

    private String loanKey;

	/** 用户身份证号 */
	private String idcardNumber;

	/** 数据最早有效时间 */
	private Date expiredTime;

	/** 任务类型，同步/异步 */
	private TaskMode taskMode;

	private DataRequestTaskStatus taskStatus;

	/** 数据平台返回的任务id */
	private String jobID;

	/** 记录创建时间 */
	private Date createTime;

	/** 记录更新时间 */
	private Date updateTime;

	/** 版本号 */
	private Integer version;

	public Integer getId() {
		return this.id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getApplyId() {
		return this.applyId;
	}

	public void setApplyId(Integer applyId) {
		this.applyId = applyId;
	}

	public String getSourceSystem() {
		return this.sourceSystem;
	}

	public void setSourceSystem(String sourceSystem) {
		this.sourceSystem = sourceSystem;
	}

	public DataRequestTaskType getTaskType() {
		return this.taskType;
	}

	public void setTaskType(DataRequestTaskType taskType) {
		this.taskType = taskType;
	}

	public Boolean getForce() {
		return this.force;
	}

	public void setForce(Boolean force) {
		this.force = force;
	}

	public String getUserKey() {
		return this.userKey;
	}

	public void setUserKey(String userKey) {
		this.userKey = userKey;
	}

	public String getIdcardNumber() {
		return this.idcardNumber;
	}

	public void setIdcardNumber(String idcardNumber) {
		this.idcardNumber = idcardNumber;
	}

	public Date getExpiredTime() {
		return this.expiredTime;
	}

	public void setExpiredTime(Date expiredTime) {
		this.expiredTime = expiredTime;
	}

	public TaskMode getTaskMode() {
		return this.taskMode;
	}

	public void setTaskMode(TaskMode taskMode) {
		this.taskMode = taskMode;
	}

	public DataRequestTaskStatus getTaskStatus() {
		return this.taskStatus;
	}

	public void setTaskStatus(DataRequestTaskStatus taskStatus) {
		this.taskStatus = taskStatus;
	}

	public String getJobID() {
		return this.jobID;
	}

	public void setJobID(String jobID) {
		this.jobID = jobID;
	}

	public Date getCreateTime() {
		return this.createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return this.updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public Integer getVersion() {
		return this.version;
	}

	public void setVersion(Integer version) {
		this.version = version;
	}

	@Override
	public void setUpdateTime() {
		this.updateTime = new Date();
	}

	@Override
	public void setCreateTime() {
		this.createTime = new Date();
	}

	public String getLoanKey() {
        return this.loanKey;
    }

    public void setLoanKey(String loanKey) {
        this.loanKey = loanKey;
    }

    public boolean isExpired() {
		int expiredTime = - 1;
		try {
			expiredTime = RiskProduct.getRiskProduct(this.sourceSystem).getMobileRecordExpireTime();
		} catch (Exception e) {
		}
		if (expiredTime == -1) {
			expiredTime = DataRequestConstants.DATA_REQUEST_TASK_EXPIRED_INTERVAL;
		}
		return (new Date().getTime() - this.getCreateTime().getTime()) / 1000 > expiredTime;
	}

	public boolean isExpired(Date pCreateTime){
		int expiredTime = - 1;
		try {
			expiredTime = RiskProduct.getRiskProduct(this.sourceSystem).getMobileRecordExpireTime();
		} catch (Exception e) {
		}
		if (expiredTime == -1) {
			expiredTime = DataRequestConstants.DATA_REQUEST_TASK_EXPIRED_INTERVAL;
		}
		return (new Date().getTime() - pCreateTime.getTime()) / 1000 > expiredTime;
	}
}
