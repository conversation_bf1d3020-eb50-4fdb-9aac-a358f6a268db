package com.youxin.risk.datacenter.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.google.common.collect.Lists;
import com.youxin.risk.commons.apollo.ApolloClientAdapter;
import com.youxin.risk.commons.apollo.ApolloNamespaceEnum;
import com.youxin.risk.commons.dao.accountProxy.AccountProxyMapper;
import com.youxin.risk.commons.utils.DateUtil;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.datacenter.account.*;
import com.youxin.risk.datacenter.constants.UserPayConstants;
import com.youxin.risk.datacenter.dto.LoanRequestDTO;
import com.youxin.risk.datacenter.hbase.RiskUserPayHB;
import com.youxin.risk.datacenter.service.LoanService;
import com.youxin.risk.hbase.HbaseService;
import com.youxin.risk.verify.service.AccountSystemService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class LoanServiceImpl implements LoanService {

    private static final Logger LOGGER = LoggerFactory.getLogger(LoanServiceImpl.class);
    private final String DBTABLE_STATUS_SUCCESS = "2";
    private static final String COLLECTION_USER_PAY = "user_pay";
    private static final String COLLECTION_USER_PAY_NEW = "user_pay_202301";
    private static final String PROXY_SERIAL_NO = "proxySerialNo";

    @Autowired
    private HbaseService hbaseService;
    @Autowired
    private AccountSystemService accountSystemService;

    @Resource
    private AccountProxyMapper accountProxyMapper;
    @Autowired
    @Qualifier("accountMongoTemplate")
    private MongoTemplate accountMongoTemplate;

    private final String[] partitions = new String[]{"0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "a", "b", "c", "d", "e", "f", "g"};
    private static final String USER_PAY_DERATE_ITEMS = "realDerateItems";
    private static final String USER_PAY_DERATE_TYPE = "realDerateType";
    private static final String AMOUNT = "amount";
    private static final String PAY_TYPE = "payType";
    private static final String PAY_LAUNCH_TYPE = "payLaunchType";
    private static final String PAYMENT_CONFIRM_TIME = "paymentConfirmTime";

    @Override
    public String getLoanHistoryInfo(LoanRequestDTO requestDTO) {
        boolean flag = ApolloClientAdapter.getBooleanConfig(ApolloNamespaceEnum.DC_INSIDE_SPACE, "userpay.detail.flag", false);

        String userKey = requestDTO.getPartnerUserId();
        String sourceSystem = requestDTO.getPartner();

        // 手动开启日志打印
        String logFlag = requestDTO.getLogFlag();
        if (flag || "1".equals(logFlag)) {
            flag = true;
            LoggerProxy.info("LoanService", LOGGER, "getLoanHistoryInfo request={},", JSON.toJSONString(requestDTO));
        }

        String result;
        String isDelayCheckFlag = requestDTO.getIsDelayCheckFlag();
        if(StringUtils.isNotEmpty(isDelayCheckFlag) && "1".equals(isDelayCheckFlag)){
            result = accountSystemService.getLoanHistoryNewDelayCheckFromAccountByUserKey(userKey, sourceSystem);
        }else {
            result = accountSystemService.getLoanHistoryNewFromAccountByUserKey(userKey, sourceSystem);
        }

        // 下线 detail 字段的组装，todo 后续上线可以直接return result
        boolean switchFlag = ApolloClientAdapter.getBooleanConfig(ApolloNamespaceEnum.DC_INSIDE_SPACE, "userpay.detail.switch", false);
        if (switchFlag) {
            return result;
        }

        try {
            // 新接口数据
            JSONObject json = JSON.parseObject(result, Feature.OrderedField);
            String code = (String) json.get("status");
            JSONObject data = json.getJSONObject("data");

            // 说明返回数据code有误
            if (!"040001".equals(code) && !"000000".equals(code)) {
                return result;
            }

            if (data == null || data.isEmpty()) {
                return result;
            }

            // 合并hbase数据
            mergeWithHbaseData(userKey, data, flag);

            // 修改返回结果
            result = json.toJSONString();
        } catch (Exception e) {
            LoggerProxy.error("LoanServiceError", LOGGER, "getLoanHistoryInfoError ", e);
            // 返回一个固定errorCode
            JSONObject ret = new JSONObject();
            ret.put("status", "000001");
            ret.put("message", "处理失败");
            return ret.toJSONString();
        }

        return result;
    }

    @Override
    public String getLoanHistoryInfoNew(LoanRequestDTO requestDTO) {
        String userKey = requestDTO.getPartnerUserId();
        String sourceSystem = requestDTO.getPartner();

        String result;
        String isDelayCheckFlag = requestDTO.getIsDelayCheckFlag();
        if (StringUtils.isNotEmpty(isDelayCheckFlag) && "1".equals(isDelayCheckFlag)) {
            result = accountSystemService.getLoanHistoryNewDelayCheckFromAccountByUserKey(userKey, sourceSystem);
        } else {
            result = accountSystemService.getLoanHistoryNewFromAccountByUserKey(userKey, sourceSystem);
        }

        return result;
    }

    @Override
    public String getLoanSourceFromAccountByUserKey(LoanRequestDTO requestDTO) {
        String userKey = requestDTO.getPartnerUserId();
        String sourceSystem = requestDTO.getPartner();
        return accountSystemService.getLoanSourceFromAccountByUserKey(userKey, sourceSystem);
    }

    @Override
    public String getLoanHistoryInfoByLoanKeys(LoanRequestDTO requestDTO) {
        boolean flag = ApolloClientAdapter.getBooleanConfig(ApolloNamespaceEnum.DC_INSIDE_SPACE, "userpay.detail.flag", false);

        String partnerLoanNos = requestDTO.getPartnerLoanNos();
        String sourceSystem = requestDTO.getPartner();
        // 手动开启日志打印
        String logFlag = requestDTO.getLogFlag();
        if (flag || "1".equals(logFlag)) {
            flag = true;
            LoggerProxy.info("LoanService", LOGGER, "getLoanHistoryInfoByLoanKeys request={},", JSON.toJSONString(requestDTO));
        }

        String result = accountSystemService.getLoanHistoryFromAccountByLoanKeys(partnerLoanNos, sourceSystem);

        try {
            JSONObject json = JSONObject.parseObject(result);
            String code = (String) json.get("status");
            if (!"040001".equals(code) && !"000000".equals(code)) {
                return result;
            }
            JSONObject data = json.getJSONObject("data");
            if (data == null || data.isEmpty()) {
                return result;
            }

            // 合并hbase数据
            JSONObject originData = new JSONObject();
            originData.putAll(data);
            List<String> loanKeys = JSONArray.parseArray(partnerLoanNos, String.class);
            String mergeData = mergeWithHbaseData(loanKeys, data, flag);
            // 新旧数据对比 result 和 mergeData
            if (flag) {
                LoggerProxy.info("LoanService", LOGGER, "getLoanHistoryInfoByLoanKeys dataLog result={}, mergeData={},", originData.toJSONString(), mergeData);
            }
        } catch (Exception e) {
            LoggerProxy.error("LoanServiceError", LOGGER, "getLoanHistoryInfoByLoanKeysError ", e);
        }


        return result;
    }

    /**
     * 通过userkey获取userPay信息
     * TODO 是否需要加上时间
     * @param userKey
     * @return
     */
    private List<Map<String, Object>> getHBaseByUserKey(String userKey) {
        RiskUserPayHB riskUserPayHB = new RiskUserPayHB(userKey);
        riskUserPayHB.setMinStamp(UserPayConstants.USER_PAY_MARK_TIME);
        riskUserPayHB.setMaxStamp(System.currentTimeMillis());

        try {
            boolean slaveFlag = ApolloClientAdapter.getBooleanConfig(ApolloNamespaceEnum.COMMON_SPACE, "userpay.slave.flag", false);
            boolean slaveAllFlag = ApolloClientAdapter.getBooleanConfig(ApolloNamespaceEnum.RISK_ALL_SPACE, "hbase.slave.flag", false);
            if (slaveFlag || slaveAllFlag) {
                LoggerProxy.info("LoanService", LOGGER, "scanSlaveByRowKeyPrefix riskUserPayHB={} ", JSON.toJSONString(riskUserPayHB));
                hbaseService.scanSlaveByRowKeyPrefix(riskUserPayHB);
                return riskUserPayHB.getResultList();
            }
        } catch (Exception ex) {
            LoggerProxy.error("LoanServiceError", LOGGER, "getHBaseByUserKey error获取备份集群错误 userKey:{}",userKey, ex);
        }

        hbaseService.scanByRowKeyPrefix(riskUserPayHB);
        return riskUserPayHB.getResultList();
    }

    /**
     * 获取用户指定天数之前的借款单
     *
     * @param userKey
     * @param partnerLoanNos
     * @return key->partnerLoanNo, value->List
     */
    public Map<String, List<Map<String, Object>>> getDayAgoUserPayByPartnerLoanNos(String userKey, List<String> partnerLoanNos, long days, boolean flag) {
        if (userKey == null || CollectionUtils.isEmpty(partnerLoanNos)) {
            return Collections.EMPTY_MAP;
        }
        // 从hbase获取信息
        List<Map<String, Object>> list = getHBaseByUserKey(userKey);
        if (flag) {
            LoggerProxy.info("LoanService", LOGGER, "userList={} ", JSON.toJSONString(list));
        }
        if (CollectionUtils.isEmpty(list)) {
            return Collections.EMPTY_MAP;
        }

        Long dateMark = this.getPreviousDate(days);

        List<Map<String, Object>> filterResult = list.stream()
                .filter(m -> m.get("create_time") != null && m.get("partner_loan_no") != null && m.get("account_loan_no") != null)
                .filter(m -> m.get("partner_id") != null && Objects.equals(5L, Long.parseLong(m.get("partner_id").toString())))
                .filter(m -> DBTABLE_STATUS_SUCCESS.equals(m.get("payment_confirm_status").toString()))
                .filter(m -> DBTABLE_STATUS_SUCCESS.equals(m.get("account_report_status").toString()))
                .filter(m -> {
                    Date date = DateUtil.parseDate(m.get("create_time").toString(), DateUtil.LONG_WEB_FORMAT);
                    if (date == null) {
                        return false;
                    }
                    // 因为之前user_pay表切分过只保留了这个时间点后的数据
                    return date.getTime() <= dateMark && date.getTime() > UserPayConstants.USER_PAY_MARK_TIME;
                })
                .filter(m -> partnerLoanNos.contains(m.get("partner_loan_no").toString()))
                .map(m -> {
                    // 将\\N 转为null
                    for (String key: m.keySet()) {
                        if ("\\N".equals(m.get(key).toString())) {
                            m.put(key, "");
                        }
                    }

                    return m;
                })
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(filterResult)) {
            return Collections.EMPTY_MAP;
        }

        // 将payParam 参数替换
        updateUserPayByProxySerialNo(filterResult);
        return filterResult.stream().collect(Collectors.groupingBy(m -> m.get("account_loan_no").toString()));
    }

    public void updateUserPayByProxySerialNo(List<Map<String, Object>> userPayList) {
        if (CollectionUtils.isEmpty(userPayList)) {
            return;
        }
        List<String> proxySerialNoList = userPayList.stream().map(map -> map.get("proxy_serial_no").toString()).distinct()
                .collect(Collectors.toList());
        List<MongoUserPay> mongoUserPayList = Lists.newArrayList();
        List<MongoUserPay> mongoUserPayListNew = Lists.newArrayList();

        List<List<String>> serialNos = Lists.partition(proxySerialNoList, 100);
        for (List<String> nos : serialNos) {
            Query query = new Query();
            Criteria criteria = new Criteria(PROXY_SERIAL_NO).in(nos);
            query.addCriteria(criteria);
            List<MongoUserPay> payList = accountMongoTemplate.find(query, MongoUserPay.class, COLLECTION_USER_PAY);
            List<MongoUserPay> payListNew = accountMongoTemplate.find(query, MongoUserPay.class, COLLECTION_USER_PAY_NEW);
            if (CollectionUtils.isNotEmpty(payList)) {
                mongoUserPayList.addAll(payList);
            }

            if (CollectionUtils.isNotEmpty(payListNew)) {
                mongoUserPayListNew.addAll(payListNew);
            }
        }

        if (CollectionUtils.isEmpty(mongoUserPayList) && CollectionUtils.isEmpty(mongoUserPayListNew)) {
            return;
        }

        Map<String, MongoUserPay> payParamMap = null;
        Map<String, MongoUserPay> payParamMapNew = null;
        if (CollectionUtils.isNotEmpty(mongoUserPayList)) {
            payParamMap = mongoUserPayList.stream().collect(Collectors.toMap(MongoUserPay::getProxySerialNo, Function.identity()));
        }
        if (CollectionUtils.isNotEmpty(mongoUserPayListNew)) {
            payParamMapNew = mongoUserPayListNew.stream().collect(Collectors.toMap(MongoUserPay::getProxySerialNo, Function.identity()));
        }

        for (Map<String, Object> userPay : userPayList) {
            String proxySerialNo = userPay.get("proxy_serial_no").toString();
            if (MapUtils.isNotEmpty(payParamMap) && payParamMap.containsKey(proxySerialNo)) {
                MongoUserPay mongoUserPay = payParamMap.get(proxySerialNo);
                userPay.put("pay_param", mongoUserPay.getPayParam());
                if (StringUtils.isNotBlank(mongoUserPay.getRepayCoupon())) {
                    userPay.put("repay_coupon", mongoUserPay.getRepayCoupon());
                }
            }
            if (MapUtils.isNotEmpty(payParamMapNew) && payParamMapNew.containsKey(proxySerialNo)) {
                MongoUserPay mongoUserPay = payParamMapNew.get(proxySerialNo);
                userPay.put("pay_param", mongoUserPay.getPayParam());
                if (StringUtils.isNotBlank(mongoUserPay.getRepayCoupon())) {
                    userPay.put("repay_coupon", mongoUserPay.getRepayCoupon());
                }
            }
        }
    }

    /**
     * 与hbase中的数据进行合并,只需要合并details中的数据即可
     * hbase 存的数据为T-1，返回的data数据为近三个月数据
     */
    private String mergeWithHbaseData(List<String> loanKeys, JSONObject data, boolean flag) {
        JSONObject originData = new JSONObject();
        originData.putAll(data);
        JSONArray loans = data.getJSONArray("loans");
        if (loans == null || loans.isEmpty()) {
            return data.toJSONString();
        }

        Map<String, List<Object>> userMap = loans.stream().collect(Collectors.groupingBy(loan -> ((JSONObject) loan).getString("partnerUserId")));
        LinkedHashSet<String> userKeys = loans.stream().map(loan -> ((JSONObject) loan).getString("partnerUserId")).collect(Collectors.toCollection(LinkedHashSet::new));
        if (CollectionUtils.isEmpty(userKeys)) {
            return data.toJSONString();
        }

        // 只要获取1个月前的就可以了，然后去重做合并
        for (String userKey : userKeys) {
            Map<String, List<Map<String, Object>>> userPayMap = this.getDayAgoUserPayByPartnerLoanNos(userKey, loanKeys, 0L, flag);
            if (org.apache.commons.collections.MapUtils.isEmpty(userPayMap)) {
                continue;
            }

            JSONArray array = JSONArray.parseArray(JSON.toJSONString(userMap.get(userKey)));
            mergeData(array, userPayMap, flag);
        }

        return data.toJSONString();
    }

    /**
     * 与hbase中的数据进行合并,只需要合并details中的数据即可
     * hbase 存的数据为T-1，返回的data数据为近三个月数据
     */
    private String mergeWithHbaseData(String userKey, JSONObject data, boolean flag) {
        JSONArray loans = data.getJSONArray("loans");
        if (loans == null || loans.isEmpty()) {
            return data.toJSONString();
        }

        List<String> partnerLoanNos = loans.stream().map(loan -> ((JSONObject) loan).getString("partnerLoanNo")).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(partnerLoanNos)) {
            return data.toJSONString();
        }

        // 只要获取1个月前的就可以了，然后去重做合并
        Integer days = ApolloClientAdapter.getIntConfig(ApolloNamespaceEnum.DC_INSIDE_SPACE, "userpay.days", 0);
        Map<String, List<Map<String, Object>>> userPayMap = this.getDayAgoUserPayByPartnerLoanNos(userKey, partnerLoanNos, days.longValue(), flag);
        if (org.apache.commons.collections.MapUtils.isEmpty(userPayMap)) {
            return data.toJSONString();
        }

        mergeData(loans, userPayMap, flag);
        return data.toJSONString();
    }

    private void mergeData(JSONArray loans, Map<String, List<Map<String, Object>>> userPayMap,  boolean flag) {
        if (flag) {
            LoggerProxy.info("LoanService", LOGGER, "userPayMap userPayMap={} ", JSON.toJSONString(userPayMap));
        }

        // 路径 periods/payedInfo/details
        for (Object loan : loans) {
            JSONArray periods = ((JSONObject) loan).getJSONArray("periods");
            String loanKey = ((JSONObject) loan).getString("loanKey");
            List<Map<String, Object>> userPayList = userPayMap.get(loanKey);

            if (CollectionUtils.isEmpty(userPayList)) {
                continue;
            }

            for (Object period : periods) {
                if (!((JSONObject) period).containsKey("payedInfo")) {
                    continue;
                }

                JSONObject payedInfo = ((JSONObject) period).getJSONObject("payedInfo");
                JSONArray details = new JSONArray();
                if (flag) {
                    LoggerProxy.info("LoanService", LOGGER, "userPay userPay:{},", JSON.toJSONString(userPayList));
                }
                for (Map<String, Object> userPay : userPayList) {
                    JSONObject detail = getUserPayAuditInfo(userPay, (JSONObject) period);
                    if (detail != null) {
                        details.add(detail);
                    }
                }

                // 说明旧接口已经返回了detail
                String detailStr = "";
                if (flag) {
                    LoggerProxy.info("LoanService", LOGGER, "periods detail:{},", details);
                }
                if (payedInfo.containsKey("details")) {
                    detailStr = payedInfo.getJSONArray("details").toJSONString();
                    details = mergeDetailInfo(payedInfo.getJSONArray("details"), details);
                }
                if (flag) {
                    LoggerProxy.info("LoanService", LOGGER, "merge detail:{},", details);
                    LoggerProxy.info("LoanService", LOGGER, "detailStr", detailStr);
                }
                if (details.size() > 0) {
                    payedInfo.put("details", details);
                }
            }
        }
    }

    /**
     * 合并去重
     *
     * @param originDetails
     * @param details
     */
    private JSONArray mergeDetailInfo(JSONArray originDetails, JSONArray details) {
        if (originDetails == null || originDetails.isEmpty()) {
            return details;
        }

        if (details.isEmpty()) {
            return originDetails;
        }

        JSONArray result = new JSONArray();
        // 去重并保证顺序,原始的originDetails 和detail 都是按照id升序的，originDetails 为近两个月，detail为三个月
        for (int i = 0; i < details.size(); i++) {
            int j = 0;
            for (; j < originDetails.size(); j++) {
                // 表示重复
                if (originDetails.getJSONObject(j).getString(AMOUNT).equals(details.getJSONObject(i).getString(AMOUNT))
                        && originDetails.getJSONObject(j).getString(PAY_TYPE).equals(details.getJSONObject(i).getString(PAY_TYPE))
                        && originDetails.getJSONObject(j).getString(PAY_LAUNCH_TYPE).equals(details.getJSONObject(i).getString(PAY_LAUNCH_TYPE))
                        && originDetails.getJSONObject(j).getLong(PAYMENT_CONFIRM_TIME).equals(details.getJSONObject(i).getLong(PAYMENT_CONFIRM_TIME))) {
                    break;
                }
            }

            // 说明在originDetails 中没有
            if (j == originDetails.size()) {
                result.add(details.getJSONObject(i));
            }
        }

        result.addAll(originDetails);

        return result;
    }

    /**
     * 根据还款计划的期次为审核添加还款信息
     *
     * @param userPay
     * @param payPlanPeriod
     * @return
     */
    public JSONObject getUserPayAuditInfo(Map<String, Object> userPay, JSONObject payPlanPeriod) {
        try {
            JSONObject payParamJson = new JSONObject();
            String payParam = userPay.get("pay_param").toString();
            if (org.apache.commons.lang3.StringUtils.isNotBlank(payParam)) {
                payParamJson = JSONObject.parseObject(payParam);
            }
            List<DerateItemVo> derateItems = Lists.newArrayList();

            if (payParamJson.containsKey(USER_PAY_DERATE_ITEMS)
                    && payParamJson.containsKey(USER_PAY_DERATE_TYPE)) {
                derateItems = payParamJson.getJSONArray(USER_PAY_DERATE_ITEMS).toJavaList(DerateItemVo.class);
            }
            BigDecimal calAmount = getRealRepayAmount(new BigDecimal(userPay.get("amount").toString()), userPay.get("reduce_amount").toString(),
                    userPay.get("repay_coupon").toString(), null, derateItems);
            boolean toAdd = false;
            if (BeforePayStatus.COMMON == BeforePayStatus.getEnum(String.valueOf(userPay.get("before_pay")))) {
                if (org.apache.commons.lang3.StringUtils.isNotBlank(payParam)) {
                    JSONArray periods = PayParamUtils.getPeriods(payParam);
                    if (periods != null && periods.contains(payPlanPeriod.getIntValue("periodNo"))) {
                        toAdd = true;
                    }
                }
            } else {
                if ("IN_REPAY".equals(payPlanPeriod.getString("repayType"))
                        || "HELP_IN_REPAY".equals(payPlanPeriod.getString("repayType"))) {
                    toAdd = true;
                }
            }
            if (toAdd) {
                JSONObject ret = new JSONObject();
                PayLaunchType launchType = PayLaunchType.getEnmu(Integer.parseInt(userPay.get("pay_launch_type").toString()));
                PayType payType = PayType.getEnum(Integer.parseInt(userPay.get("pay_type").toString()));
                ret.put(PAY_LAUNCH_TYPE, launchType != null ? launchType.name() : PayLaunchType.HAND.name());
                ret.put(PAY_TYPE, payType != null ? payType.name() : PayType.COMMON.name());
                ret.put(AMOUNT, calAmount.toString());

                Long paymentConfirmTime;
                Date date = DateUtil.parseDate(userPay.get("payment_confirm_time").toString(), DateUtil.LONG_WEB_FORMAT);
                if (date != null) {
                    paymentConfirmTime = date.getTime();
                } else {
                    paymentConfirmTime = DateUtil.parseDate(userPay.get("create_time").toString(), DateUtil.LONG_WEB_FORMAT).getTime();
                }

                ret.put(PAYMENT_CONFIRM_TIME, paymentConfirmTime);
                return ret;
            }
        } catch (Exception e) {
            LoggerProxy.error("LoanServiceError", LOGGER, "getUserPayAuditInfoError 减免金额超出应还总额", JSON.toJSONString(userPay), payPlanPeriod.toJSONString(), e);
        }
        return null;
    }

    public BigDecimal getRealRepayAmount(BigDecimal repayAmount, String inRepayReduceAmt, String repayCoupon,
                                         String couponDerateAmtStr, List<DerateItemVo> derateItems) throws Exception {

        BigDecimal realRepayAmount = repayAmount;

        if (org.apache.commons.lang3.StringUtils.isNotBlank(inRepayReduceAmt)) {
            realRepayAmount = realRepayAmount.subtract(new BigDecimal(inRepayReduceAmt.trim()));
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(repayCoupon) || org.apache.commons.lang3.StringUtils.isNotBlank(couponDerateAmtStr)) {
            BigDecimal couponDerateAmount;

            if (org.apache.commons.lang3.StringUtils.isBlank(couponDerateAmtStr)) {
                couponDerateAmount = this.calculateDerateAmount(repayCoupon);
            } else {
                couponDerateAmount = new BigDecimal(couponDerateAmtStr);
            }
            realRepayAmount = realRepayAmount.subtract(couponDerateAmount);
        }

        if (CollectionUtils.isNotEmpty(derateItems)) {
            BigDecimal derateAmt = derateItems.stream().flatMap(
                    itemVo -> itemVo.getPlanDerateDetail().stream().map(feeVo -> feeVo.getFeeDetail().getAmount()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            realRepayAmount = BigDecimalUtils.subtract(realRepayAmount, derateAmt);
        }

        if (realRepayAmount.compareTo(BigDecimal.ZERO) < 0) {
            throw new Exception("减免金额超出应还总额");
        }
        return realRepayAmount;
    }

    /**
     * 根据减免信息汇总减免总额
     */
    public BigDecimal calculateDerateAmount(String repayCoupon) {
        JSONObject couponInfo = JSONObject.parseObject(repayCoupon);
        List<PlanDerateInfoVo> derateItemList = JSONArray.parseArray(couponInfo.getString("derateDetail"),
                PlanDerateInfoVo.class);

        BigDecimal amount = BigDecimal.ZERO;

        if (!CollectionUtils.isEmpty(derateItemList)) {
            for (PlanDerateInfoVo derateItermVo : derateItemList) {
                List<DerateFeeVo> derateFee = derateItermVo.getDerateFee();

                for (DerateFeeVo fee : derateFee) {
                    amount = amount.add(new BigDecimal(fee.getDerateAmount()));
                }
            }
        }
        return amount;
    }

    /**
     * 获取当前时间减对应天数毫秒数
     *
     * @return
     */
    private long getPreviousDate(Long day) {
        return LocalDateTime.of(LocalDate.now().minusDays(day), LocalTime.MIN).toEpochSecond(ZoneOffset.of("+8")) * 1000;
    }
}
