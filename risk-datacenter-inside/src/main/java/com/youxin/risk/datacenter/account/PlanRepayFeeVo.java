package com.youxin.risk.datacenter.account;

import java.io.Serializable;
import java.math.BigDecimal;

public class PlanRepayFeeVo implements Serializable {

    private static final long serialVersionUID = 6555205522930433411L;

    /** 期次号 */
    private Integer period;

    /** 还款计划Key */
    private String repayPlanKey;

    /** 指定还款金额明细 */
    private FeeVo feeDetail;

    /** 总资金方罚息 */
    private BigDecimal totalOverdueInterest;

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public String getRepayPlanKey() {
        return repayPlanKey;
    }

    public void setRepayPlanKey(String repayPlanKey) {
        this.repayPlanKey = repayPlanKey;
    }

    public FeeVo getFeeDetail() {
        return feeDetail;
    }

    public void setFeeDetail(FeeVo feeDetail) {
        this.feeDetail = feeDetail;
    }

    public BigDecimal getTotalOverdueInterest() {
        return totalOverdueInterest;
    }

    public void setTotalOverdueInterest(BigDecimal totalOverdueInterest) {
        this.totalOverdueInterest = totalOverdueInterest;
    }
}
