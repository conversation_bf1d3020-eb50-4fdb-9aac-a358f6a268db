package com.youxin.risk.datacenter.service.impl;

import com.youxin.risk.commons.dao.datacenter.DcSubmitCarMapper;
import com.youxin.risk.commons.model.datacenter.DcSubmitCar;
import com.youxin.risk.datacenter.service.SubmitCarService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Desc 获取车产信息
 * <AUTHOR>
 * @date 2022-10-17 22:00
 */

@Service
public class SubmitCarServiceImpl extends AbstractBatchQueryService<DcSubmitCar> implements SubmitCarService {

    @Autowired
    private DcSubmitCarMapper dcSubmitCarMapper;

    @Override
    public DcSubmitCar getLastByUserKey(String userKey) {
        return dcSubmitCarMapper.getByUserKey(userKey);
    }
}
