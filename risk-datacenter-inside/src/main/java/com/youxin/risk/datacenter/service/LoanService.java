package com.youxin.risk.datacenter.service;

import com.youxin.risk.datacenter.dto.LoanRequestDTO;

public interface LoanService {

    /**
     * 获取用户历史借款信息
     * @param requestDTO
     * @return
     */
    String getLoanHistoryInfo(LoanRequestDTO requestDTO);

    /**
     * 获取用户历史借款信息
     *
     * @param requestDTO
     * @return
     */
    String getLoanHistoryInfoNew(LoanRequestDTO requestDTO);

    /**
     * 获取用户历史借款对应来源
     *
     * @param requestDTO
     * @return
     */
    String getLoanSourceFromAccountByUserKey(LoanRequestDTO requestDTO);

    /**
     * 通过loanke获取用户历史借款信息
     *
     * @param requestDTO
     * @return
     */
    String getLoanHistoryInfoByLoanKeys(LoanRequestDTO requestDTO);
}
