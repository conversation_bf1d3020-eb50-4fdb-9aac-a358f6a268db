package com.youxin.risk.datacenter.delayqueue;

import com.youxin.risk.common.delayqueue.DelayMessageHandler;
import com.youxin.risk.common.delayqueue.SubmitDelayMessage;
import com.youxin.risk.commons.utils.JacksonUtil;
import com.youxin.risk.commons.utils.LoggerProxy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.concurrent.TimeUnit;

public class SubmitAmountDelayService {

	private final static Logger logger = LoggerFactory.getLogger(SubmitAmountDelayService.class);

	@Autowired
	private SubmitAmountRedisDelayQueue submitAmountRedisDelayQueue;

	public void schedule(Object param, Long delay,Class<? extends DelayMessageHandler> cls){
		if (cls == null) {
			throw new IllegalArgumentException("handle is null");
		}
		send(new SubmitDelayMessage(param,cls,delay));
	}

	private void send(SubmitDelayMessage delayMessage){
		LoggerProxy.info("send",logger,"delay task:{}", JacksonUtil.toJson(delayMessage));
		try {
			submitAmountRedisDelayQueue.offer(delayMessage,delayMessage.getDelay(), TimeUnit.SECONDS);
		}catch (Exception ex){
			LoggerProxy.info("send",logger,"delay task:{} ,error", JacksonUtil.toJson(delayMessage),ex);
		}
	}

}
