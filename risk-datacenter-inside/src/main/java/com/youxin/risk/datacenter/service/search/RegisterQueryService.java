package com.youxin.risk.datacenter.service.search;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Preconditions;
import com.youxin.risk.commons.model.datacenter.DcSubmitRegister;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.ObjectTransferUtils;
import com.youxin.risk.datacenter.service.SubmitRegisterService;
import com.youxin.risk.datacenter.service.impl.HfqUserInfoService;
import com.youxin.risk.datacenter.service.search.annotation.DcServiceCode;
import com.youxin.risk.verify.service.VerifySubmitService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;


@Component
public class RegisterQueryService implements DcQueryService<DcSubmitRegister> {


    private static final Logger logger = LoggerFactory.getLogger(RegisterQueryService.class);

    @Resource
    private SubmitRegisterService submitRegisterService;

    @Resource
    private HfqUserInfoService hfqUserInfoService;

    @Autowired
    @Qualifier("verifySubmitServImpl")
    private VerifySubmitService verifySubmitService;

    @DcServiceCode(name = {"REGISTER", "findSubmitRegisterByUserKey"})
    public DcSubmitRegister getByUserKey(Map<String, Object> params) {
        String userKey = (String) params.get("userKey");
        String apiLoanSource = (String) params.get("apiLoanSource");
        Preconditions.checkArgument(!StringUtils.isBlank(userKey));

        DcSubmitRegister dcSubmitRegister = submitRegisterService.getByUserKey(userKey, apiLoanSource);
        if (StringUtils.isNotBlank(apiLoanSource)) {
            return dcSubmitRegister;
        }

        if (null == dcSubmitRegister) {

            try {
                //调用好分期接口获取用户信息
                JSON userInfo = hfqUserInfoService.getUserInfo(userKey);

                dcSubmitRegister = ObjectTransferUtils.transferObject(userInfo, DcSubmitRegister.class);

                LoggerProxy.info("return lost rePoll dcSubmitRegister", logger, "userKey={}", userKey);

            } catch (Exception e) {
                LoggerProxy.error("lost register insert error", logger, "userKey={}", userKey, e);
            }
        }

        return dcSubmitRegister;
    }

    @DcServiceCode(name = "REGISTER_LIST")
    public List<DcSubmitRegister> getListByUserKey(Map<String, Object> params) {
        String userKey = (String) params.get("userKey");
        String apiLoanSource = (String) params.get("apiLoanSource");
        Preconditions.checkArgument(!StringUtils.isBlank(userKey));
        return submitRegisterService.getListByUserKey(userKey, apiLoanSource);
    }
}