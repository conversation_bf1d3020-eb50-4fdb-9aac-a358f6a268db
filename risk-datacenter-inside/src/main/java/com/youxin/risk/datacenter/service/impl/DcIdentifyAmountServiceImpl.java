package com.youxin.risk.datacenter.service.impl;

import com.youxin.risk.commons.model.datacenter.common.OperationType;
import com.youxin.risk.commons.utils.DateUtil;
import com.youxin.risk.datacenter.mapper.DcIdentifyAmountMapper;
import com.youxin.risk.datacenter.model.DcIdentifyAmount;
import com.youxin.risk.datacenter.service.DcIdentityAmountService;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Service
public class DcIdentifyAmountServiceImpl implements DcIdentityAmountService {

    @Autowired
    private DcIdentifyAmountMapper dcIdentityAmountMapper;

    @Override
    public boolean queryRecordInFifteenDays(String userKey, String certificationItem, int day) {
        Date date = new DateTime().minusDays(day).withMillisOfDay(0).toDate();
        String dateTime = DateUtil.format(date, DateUtil.LONG_WEB_FORMAT);
        Integer id;
        // 天下信用的提额类型，单独使用
        if (OperationType.AMOUNT_TIANXIAXINYONG.name().equals(certificationItem) || OperationType.AMOUNT_TIANXIAXINYONG_EDUCATION.name().equals(certificationItem)) {
            id = dcIdentityAmountMapper.queryTxxyRecordInFifteenDays(userKey, certificationItem, dateTime);
        }
        // 其他类型共用一个提额锁定期
        else {
            id = dcIdentityAmountMapper.queryOtherRecordInFifteenDays(userKey, certificationItem, dateTime);
        }
        boolean result = false;
        if (id != null) {
            result = true;
        }
        return result;
    }

    @Override
    public void insert(DcIdentifyAmount dcIdentifyAmount) {
        dcIdentityAmountMapper.insert(dcIdentifyAmount);
    }
}
