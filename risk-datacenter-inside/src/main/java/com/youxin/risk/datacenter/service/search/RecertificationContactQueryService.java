package com.youxin.risk.datacenter.service.search;

import com.google.common.base.Preconditions;
import com.youxin.risk.commons.model.datacenter.DcSubmitContactInfo;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.datacenter.service.SubmitContactInfoService;
import com.youxin.risk.datacenter.service.search.annotation.DcServiceCode;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

import static com.youxin.risk.commons.utils.StringUtils.replaceBlank;

@Component
public class RecertificationContactQueryService implements DcQueryService<DcSubmitContactInfo> {

    private static final Logger LOGGER = LoggerFactory.getLogger(RecertificationContactQueryService.class);

    @Resource
    private SubmitContactInfoService contactInfoService;

    @DcServiceCode(name = "RECERTIFICATION_CONTACT")
    public List<DcSubmitContactInfo> queryByUserKey(Map<String, Object> params) {

        String userKey = (String) params.get("userKey");
        Preconditions.checkArgument(!StringUtils.isBlank(userKey));
        List<DcSubmitContactInfo> dcSubmitContactInfoList = this.contactInfoService.queryReCertificationByUserKey(userKey);
        if(CollectionUtils.isNotEmpty(dcSubmitContactInfoList)){
            for (DcSubmitContactInfo dcSubmitContactInfo : dcSubmitContactInfoList) {
                String pre = dcSubmitContactInfo.getMobile();
                LoggerProxy.info("queryByUserKey",LOGGER, "replaceBlank_mobile_pre={}", pre);
                String post = replaceBlank(pre);
                LoggerProxy.info("queryByUserKey",LOGGER, "replaceBlank_mobile_post={}", post);
                dcSubmitContactInfo.setMobile(post);
            }
        }
        return dcSubmitContactInfoList;
    }
}