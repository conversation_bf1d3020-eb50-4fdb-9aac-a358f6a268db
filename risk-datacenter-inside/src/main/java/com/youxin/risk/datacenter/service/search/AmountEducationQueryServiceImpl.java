package com.youxin.risk.datacenter.service.search;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Preconditions;
import com.weicai.caesar.CaesarUtil;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.datacenter.mapper.DcSubmitAmountEducationMapper;
import com.youxin.risk.datacenter.model.DcSubmitAmountCreditCard;
import com.youxin.risk.datacenter.model.DcSubmitAmountEducation;
import com.youxin.risk.datacenter.service.search.annotation.DcServiceCode;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;


/**
 * <AUTHOR>
 */
@Component
public class AmountEducationQueryServiceImpl implements DcQueryService<DcSubmitAmountEducation> {


    private static final Logger logger = LoggerFactory.getLogger(AmountEducationQueryServiceImpl.class);


    @Autowired
    DcSubmitAmountEducationMapper dcSubmitAmountEducationMapper;


    @DcServiceCode(name = {"AMOUNT_EDUCATION"})
    public DcSubmitAmountEducation getByUserKey(Map<String, Object> params) {

        String userKey = (String) params.get("userKey");
        Preconditions.checkArgument(!StringUtils.isBlank(userKey));

        DcSubmitAmountEducation dcSubmitAmountEducation = dcSubmitAmountEducationMapper.getByUserKey(userKey);


        return dcSubmitAmountEducation;
    }

}