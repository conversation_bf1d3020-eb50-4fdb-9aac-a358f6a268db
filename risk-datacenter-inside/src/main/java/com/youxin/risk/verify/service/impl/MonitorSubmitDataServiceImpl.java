package com.youxin.risk.verify.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.xxl.job.core.util.DateUtil;
import com.youxin.risk.commons.dao.datacenter.DcOperationLogMapper;
import com.youxin.risk.commons.exception.RiskRuntimeException;
import com.youxin.risk.commons.utils.HttpResult;
import com.youxin.risk.commons.utils.HttpUtils;
import com.youxin.risk.verify.redis.RedisService;
import com.youxin.risk.verify.service.MonitorSubmitDataService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.youxin.risk.ra.enums.DataPlatformConstants.header;

@Service
public class MonitorSubmitDataServiceImpl implements MonitorSubmitDataService {

    private static Logger LOGGER = LoggerFactory.getLogger(MonitorSubmitDataServiceImpl.class);

    /**
     * 超时时间50分钟，单位秒
     */
    public static final Integer SYNC_EXPIRE = 3000;

    /**
     * 正在同步key
     */
    public static final String IS_SYNCING = "monitor:isSyncing";

    /**
     * 正在同步值
     */
    public static final String IS_SYNCING_1 = "1";
    public static final String IS_SYNCING_0 = "0";

    @Autowired
    @Qualifier("datacenterCacheRedisService")
    private RedisService datacenterRedisService;

    @Autowired
    private DcOperationLogMapper dcOperationLogMapper;

    @Value("${datastatistics.url}")
    private String datastatisticsUrl;

    @Override
    public void monitorSubmitDataCount(String startTime, String endTime, String recordTypes) {
        datacenterRedisService.setex(IS_SYNCING, SYNC_EXPIRE, IS_SYNCING_1);
        List<Map<String, Long>> results = dcOperationLogMapper.getMonitorData(startTime, endTime, Lists.newArrayList(recordTypes.split(",")));
        List<Map<String, Object>> params = Lists.newArrayList();
        results.forEach(result -> {
            Map<String, Object> param = new HashMap<>();
            param.put("count", result.get("count"));
            param.put("dataType", result.get("operation_type"));
            param.put("endTime", DateUtil.parseDateTime(endTime).getTime());
            param.put("startTime", DateUtil.parseDateTime(startTime).getTime());
            params.add(param);
        });
        if (CollectionUtils.isEmpty(params)) {
            LOGGER.warn("MonitorSubmitDataServiceImpl params is empty, startTime: {}, endTime: {}, types: {}", startTime, endTime, recordTypes);
            return;
        }
        header.put("X-Token", "T03P000000392829A000001001OPN6ZP");
        final HttpResult httpResult = HttpUtils.post(String.format("%s%s", datastatisticsUrl, "/dataStatistics/add"), header, JSON.toJSONString(params));
        if (httpResult != null && httpResult.getStatus() != 200) {
            LOGGER.error("MonitorSubmitDataServiceImpl send error, startTime: {}, httpResult: {}", startTime, JSONObject.toJSONString(httpResult));
            throw new RiskRuntimeException("发送统计信息异常:" + httpResult.getMessage());
        }
    }

}
