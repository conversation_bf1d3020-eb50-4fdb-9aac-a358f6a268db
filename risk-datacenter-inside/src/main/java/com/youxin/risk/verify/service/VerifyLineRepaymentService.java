package com.youxin.risk.verify.service;


import com.youxin.risk.commons.model.verify.VerifyLineRepayment;
import com.youxin.risk.verify.vo.VerifyLineRepayMentVo;

/**
 * <AUTHOR>
 */
public interface VerifyLineRepaymentService {

    /**
     * 根据transId和userKey 查询 VerifyLineRepayment
     * @param transId transId
     * @param userKey userKey
     * @return result
     */
    VerifyLineRepayment findRecordByTransId(String transId, String userKey);

    /**
     * 保存或更新repayment
     * @param repayment  repayment
     */
    void saveOrUpdateRec(VerifyLineRepayment repayment);

    void updateFinshStatus(VerifyLineRepayMentVo repayMentVo, Integer isFinished);
}
