package com.youxin.risk.verify.vo;

import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

@Document(collection="UserLineInputXml")
public class UserLineInputXmlVo {
    
    @Indexed
    private Integer userLineId;

    @Indexed
    private Integer midVerifyRsultId;
    
    private String inputXml;

    private String strategyType;

	@Indexed(expireAfterSeconds=259200)
    private Date createTime;
    
    public Integer getMidVerifyRsultId() {
		return midVerifyRsultId;
	}

	public void setMidVerifyRsultId(Integer midVerifyRsultId) {
		this.midVerifyRsultId = midVerifyRsultId;
	}

	public String getStrategyType() {
		return strategyType;
	}

	public void setStrategyType(String strategyType) {
		this.strategyType = strategyType;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Integer getUserLineId() {
		return userLineId;
	}

	public void setUserLineId(Integer userLineId) {
		this.userLineId = userLineId;
	}


	public String getInputXml() {
        return inputXml;
    }


    public void setInputXml(String inputXml) {
        this.inputXml = inputXml;
    }
    

}
