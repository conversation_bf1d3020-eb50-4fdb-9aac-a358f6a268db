package com.youxin.risk.verify.model;

import java.io.Serializable;
import java.util.Date;

public class VerifySubmitIdcard implements Serializable {

    /**
	 * 
	 */
    private static final long serialVersionUID = -373076704432937173L;

    private Integer id;

    private Integer operationLogId;

    private String userKey;

    private String idcardNumber;

    private String idcardName;

    private String idcardAddress;

    private String pidPositiveUrl;

    private String pidNegativeUrl;

    private String faceUrl;

    private String curveFaceUrl;

    private Double faceScore;

    private Boolean pidAuth;

    private String faceThreshold;

    private Integer faceChannel;

    private Integer positiveChannel;

    private Integer negativeChannel;

    private Double liveRate;

    private Date updateTime;

    private Date createTime;

    private Integer version;

    public void setUpdateTime() {
        this.updateTime = new Date();
    }

    public void setCreateTime() {
        this.createTime = new Date();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOperationLogId() {
        return operationLogId;
    }

    public void setOperationLogId(Integer operationLogId) {
        this.operationLogId = operationLogId;
    }

    public String getUserKey() {
        return userKey;
    }

    public void setUserKey(String userKey) {
        this.userKey = userKey;
    }

    public String getIdcardNumber() {
        return idcardNumber;
    }

    public void setIdCardNumber(String idcardNumber) {
        this.idcardNumber = idcardNumber;
    }

    public String getIdCardName() {
        return idcardName;
    }

    public void setIdCardName(String idcardName) {
        this.idcardName = idcardName;
    }

    public String getPidPositiveUrl() {
        return pidPositiveUrl;
    }

    public void setPidPositiveUrl(String pidPositiveUrl) {
        this.pidPositiveUrl = pidPositiveUrl;
    }

    public String getPidNegativeUrl() {
        return pidNegativeUrl;
    }

    public void setPidNegativeUrl(String pidNegativeUrl) {
        this.pidNegativeUrl = pidNegativeUrl;
    }

    public String getFaceUrl() {
        return faceUrl;
    }

    public void setFaceUrl(String faceUrl) {
        this.faceUrl = faceUrl;
    }

    public String getCurveFaceUrl() {
        return curveFaceUrl;
    }

    public void setCurveFaceUrl(String curveFaceUrl) {
        this.curveFaceUrl = curveFaceUrl;
    }

    public Double getFaceScore() {
        return faceScore;
    }

    public void setFaceScore(Double faceScore) {
        this.faceScore = faceScore;
    }

    public Boolean getPidAuth() {
        return pidAuth;
    }

    public void setPidAuth(Boolean pidAuth) {
        this.pidAuth = pidAuth;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getIdcardName() {
        return idcardName;
    }

    public void setIdcardName(String idcardName) {
        this.idcardName = idcardName;
    }

    public String getIdcardAddress() {
        return idcardAddress;
    }

    public void setIdcardAddress(String idcardAddress) {
        this.idcardAddress = idcardAddress;
    }

    public String getFaceThreshold() {
        return faceThreshold;
    }

    public void setFaceThreshold(String faceThreshold) {
        this.faceThreshold = faceThreshold;
    }

    public void setIdcardNumber(String idcardNumber) {
        this.idcardNumber = idcardNumber;
    }

    public Integer getFaceChannel() {
        return faceChannel;
    }

    public void setFaceChannel(Integer faceChannel) {
        this.faceChannel = faceChannel;
    }

    public Integer getPositiveChannel() {
        return positiveChannel;
    }

    public void setPositiveChannel(Integer positiveChannel) {
        this.positiveChannel = positiveChannel;
    }

    public Integer getNegativeChannel() {
        return negativeChannel;
    }

    public void setNegativeChannel(Integer negativeChannel) {
        this.negativeChannel = negativeChannel;
    }

    public Double getLiveRate() {
        return liveRate;
    }

    public void setLiveRate(Double liveRate) {
        this.liveRate = liveRate;
    }
}
