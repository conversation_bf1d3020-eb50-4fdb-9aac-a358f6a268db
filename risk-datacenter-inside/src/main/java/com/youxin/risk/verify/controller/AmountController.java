package com.youxin.risk.verify.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.utils.LogUtil;
import com.youxin.risk.datacenter.pojo.JsonResultVo;
import com.youxin.risk.verify.dto.AmountDto;
import com.youxin.risk.verify.service.AmountUpdateService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/dc")
public class AmountController {

    private static final Logger LOG = LoggerFactory.getLogger(VerifyController.class);

    @Autowired
    @Qualifier("amountUpdateServiceImpl")
    private AmountUpdateService amountUpdateService;

    @RequestMapping("/updateAmount")
    public JsonResultVo updateAmount(@RequestBody AmountDto amountDto) {
        String sessionId = amountDto.getSessionId();
        LogUtil.bindLogId(sessionId);
        try {
            LOG.info("logId={},update user amount, request param={}", sessionId, JSON.toJSONString(amountDto));
            boolean result;
            result = amountUpdateService.updateUserAmount(amountDto);
            return JsonResultVo.success().addData("result", result);
        }catch (Exception e){
            LOG.error("update user amount error,userKey:{}", amountDto.getUserKey(), e);
            return JsonResultVo.error();
        }finally {
            LogUtil.unbindLogId();
        }
    }

    @RequestMapping("/fixAmount")
    public JsonResultVo fixAmount(String userKey, Integer c) {
        if (c == null) {
            c = 300;
        }
        boolean result = amountUpdateService.fixAmount(userKey, c);
        return JsonResultVo.success().addData("result", result);
    }

    @RequestMapping("/fixAmountExt")
    public JsonResultVo fixAmountExt(@RequestBody List<String> userKeyList) {
        List<JSONObject> result = amountUpdateService.fixAmountExt(userKeyList);
        return JsonResultVo.success().addData("result", result);
    }

    @RequestMapping("/updateAmountExt")
    public JsonResultVo updateAmountExt(String userKey) {
        boolean result = amountUpdateService.updateAmountExt(userKey);
        return JsonResultVo.success().addData("result", result);
    }
}
