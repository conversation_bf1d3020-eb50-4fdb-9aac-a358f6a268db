package com.youxin.risk.verify.sharding.mapper;

import com.youxin.risk.commons.model.verify.ApiUserLineDetail;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ApiUserLineDetailMapper {

    Long save(ApiUserLineDetail info);

    List<ApiUserLineDetail> getByUserKey(@Param("userKey") String userKey);

    ApiUserLineDetail getByUserKeyAndCreateTime(@Param("userKey") String userKey, @Param("time") String time);

}
