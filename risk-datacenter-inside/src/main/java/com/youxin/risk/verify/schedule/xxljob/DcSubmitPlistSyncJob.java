package com.youxin.risk.verify.schedule.xxljob;

import com.alibaba.fastjson.JSON;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.youxin.apollo.client.NacosClient;
import com.youxin.risk.commons.constants.ApolloNamespace;
import com.youxin.risk.commons.dao.datacenter.DcSubmitPlistMapper;
import com.youxin.risk.commons.dao.datacenter.DcSubmitPlistShardingMapper;
import com.youxin.risk.commons.model.datacenter.DcSubmitPlist;
import com.youxin.risk.commons.tools.redis.RetryableJedis;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.xxl.job.XxlJobBase;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;


/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2021/01/19
 * @Version 1.0
 */
@Component
public class DcSubmitPlistSyncJob implements XxlJobBase {

    private static final Logger logger = LoggerFactory.getLogger(DcSubmitPlistSyncJob.class);

    @Resource
    private DcSubmitPlistMapper dcSubmitPlistMapper;


    @Resource
    private DcSubmitPlistShardingMapper dcSubmitPlistShardingMapper;


    @Resource
    private RetryableJedis retryableJedis;

    //同步到的idkey
    static String dcSubmitPlistSyncIdKey = "dcSubmitPlistSyncId";

    //自定义任务线程池
    private ExecutorService executorService = new ThreadPoolExecutor(50, 60,
            5, TimeUnit.MINUTES, new ArrayBlockingQueue<>(100), new ExecutorThreadFactory("syncPlistShardingPool-"), new ThreadPoolExecutor.CallerRunsPolicy());


    @XxlJob(value = "dcSubmitPlistSyncJob")
    @Override
    public ReturnT<String> execJobHandler(String param) {


        LoggerProxy.info("dcSubmitPlistSyncStart", logger, "startTime={}", new Date());

        int threadCount = 45;

        CountDownLatch countDownLatch = new CountDownLatch(threadCount);

        //启动48个线程，每个线程同步1亿的数据
        for (int i = 1; i <= threadCount; i++) {
            executorService.execute(new SyncTask(i, (i - 1) * 100000000L, i * 100000000L, countDownLatch));
//            executorService.execute(new SyncTask(i, (i - 1) * 10L, i * 10L));
        }

        try {
            countDownLatch.await(20, TimeUnit.SECONDS);
        } catch (InterruptedException ignored) {
        }


        LoggerProxy.info("dcSubmitPlistSyncEnd", logger, "endTime={}", new Date());
        return ReturnT.SUCCESS;
    }


    class SyncTask implements Runnable {


        private Integer taskId;
        private Long minId;
        private Long maxId;
        private CountDownLatch countDownLatch;

        SyncTask(Integer taskId, Long startId, Long maxId, CountDownLatch countDownLatch) {
            this.taskId = taskId;
            this.minId = startId;
            this.maxId = maxId;
            this.countDownLatch = countDownLatch;
        }


        @Override
        public void run() {

            LoggerProxy.info("dcSubmitPlistSyncStart", logger, "startTime={}", new Date());
            long startTime = System.currentTimeMillis();

            Long startId = null;


            String redisKey = dcSubmitPlistSyncIdKey + "_" + taskId;
            // 查询上次同步到的id
            try {
                startId = dcSubmitPlistMapper.selectMaxSyncId(maxId);
            } catch (Exception e) {
                LoggerProxy.error("selectMaxSyncIdError", logger, "error : ", e);
            }

            // 超过 id最大值，就不属于这个任务的同步范畴
            if (startId == null || startId >= maxId) {
                return;
            }

            int limit = 100;


            try {
                String limitStr = NacosClient.getByNameSpace(ApolloNamespace.commonSpace, "plist.sharding.sync.limit", "100");
                limit = Integer.parseInt(limitStr);
            } catch (NumberFormatException ignored) {
            }

            int successCount = 0;
            List<Integer> errorIdList = new ArrayList<>();

            try {

                //每次查询100条记录同步到sharding表中
                List<DcSubmitPlist> dcSubmitPlistList = dcSubmitPlistMapper.selectAfterId(startId, limit);
                if (CollectionUtils.isEmpty(dcSubmitPlistList)) {
                    return;
                }

                for (DcSubmitPlist dcSubmitPlist : dcSubmitPlistList) {
                    try {
                        // 超过 id最大值，就不属于这个任务的同步范畴
                        if (dcSubmitPlist.getId() >= maxId) {
                            break;
                        }

                        dcSubmitPlistShardingMapper.insert(dcSubmitPlist);
                        startId = dcSubmitPlist.getId();
                        successCount++;

                        // 超过 id最大值，就不属于这个任务的同步范畴
                        if (startId >= maxId) {
                            break;
                        }
                    } catch (DuplicateKeyException e1) {
                        //如果id 重复也要更新
                        // 不然如果都重复了，id会停留在原地
                        startId = dcSubmitPlist.getId();
                        LoggerProxy.warn("insertDcSubmitPlistShardingDuplicateKeyException", logger, "taskId={},insertId={},error : ", taskId, startId, e1);
                    } catch (Exception e) {
                        LoggerProxy.error("insertIntoDcSubmitPlistShardingError", logger, "error : ", e);
                    }
                }

            } catch (DuplicateKeyException e1) {
                LoggerProxy.warn("dcSubmitPlistSyncDuplicateKeyException", logger, "error : ", e1);
            } catch (Exception e) {
                LoggerProxy.error("dcSubmitPlistSyncError", logger, "error : ", e);
            } finally {
                //任务的起始Id更新到redis中
                retryableJedis.set(redisKey, String.valueOf(startId));
                countDownLatch.countDown();
            }


            LoggerProxy.info("dcSubmitPlistSyncSuccess", logger, "taskId={},successCount={},cost={},errorIdList={}",
                    taskId, successCount, System.currentTimeMillis() - startTime, JSON.toJSON(errorIdList));


        }
    }


    static class ExecutorThreadFactory implements ThreadFactory {
        private static final AtomicInteger poolNumber = new AtomicInteger(1);
        private final ThreadGroup group;
        private final AtomicInteger threadNumber = new AtomicInteger(1);
        private final String namePrefix;

        ExecutorThreadFactory(String poolName) {
            SecurityManager s = System.getSecurityManager();
            group = (s != null) ? s.getThreadGroup() :
                    Thread.currentThread().getThreadGroup();
            namePrefix = poolName +
                    poolNumber.getAndIncrement() +
                    "-thread-";
        }

        @Override
        public Thread newThread(Runnable r) {
            Thread t = new Thread(group, r,
                    namePrefix + threadNumber.getAndIncrement(),
                    0);
            if (t.isDaemon()) {
                t.setDaemon(false);
            }

            if (t.getPriority() != Thread.NORM_PRIORITY) {
                t.setPriority(Thread.NORM_PRIORITY);
            }
            return t;
        }
    }


}
