package com.youxin.risk.verify.service.impl;

import com.youxin.risk.datacenter.pojo.UserLineManagementHistory;
import com.youxin.risk.verify.mapper.UserLineManagementHistoryMapper;
import com.youxin.risk.verify.service.UserLineManagementHistoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class UserLineManagementHistoryServiceImpl implements UserLineManagementHistoryService {

    @Autowired
    private UserLineManagementHistoryMapper userLineManagementHistoryMapper;

    @Override
    public Long insert(UserLineManagementHistory userLineManagementHistory) {
        userLineManagementHistoryMapper.insert(userLineManagementHistory);
        return userLineManagementHistory.getId();
    }
}
