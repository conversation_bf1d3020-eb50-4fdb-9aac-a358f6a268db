package com.youxin.risk.verify.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.youxin.risk.commons.model.verify.VerifyUserLineManagement;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.utils.StringUtils;
import com.youxin.risk.commons.vo.BooleanResult;
import com.youxin.risk.verify.redis.RedisService;
import com.youxin.risk.verify.service.AmountResultProcesser;
import com.youxin.risk.verify.sharding.mapper.Irr24VerifyUserLineManagementShardingMapper;
import com.youxin.risk.verify.vo.VerifyLineAssignVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Service
public class AssignAmountProcesser extends BaseAmountProcesser implements AmountResultProcesser {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired(required = false)
    @Qualifier("cacheRedisService")
    private RedisService redisService;

    @Autowired
    private VerifyUserLineManagementService userLineManagementService;
    @Resource
    private Irr24VerifyUserLineManagementShardingMapper irr24userLineManagementMapper;

    @Override
    public BooleanResult process(JSONObject param) {
        try {
            if ("IRR24_BATCH_AMOUNT".equals(param.getString("strategyType"))
                    || ("haohuanVerifyTest".equals(param.getString("eventCode")))) {
                LoggerProxy.info("isIrr24_before", logger, "loanKey={},strategyType={}, param={}", param.getString("loanKey"), param.getString("strategyType"), param);
                BooleanResult booleanResult = handleIrr24Amount(param);
                LoggerProxy.info("isIrr24_after", logger, "loanKey={},result={},strategyType={}", param.getString("loanKey"), booleanResult.isSuccess(),param.getString("strategyType"));
                return booleanResult;
            } else {
                LoggerProxy.info("isNotIrr24", logger, "loanKey={}", param.getString("loanKey"));
            }
        } catch (Exception e) {
            LoggerProxy.error("parseIsIrr24Error", logger, "loanKey={}", param.getString("loanKey"));
        }
        BooleanResult booleanResult = new BooleanResult(false);
        try {
            // check幂等；保存结果；更新幂等信息
            JSONObject record = param.getJSONObject("amountParams");
            String transId = record.getString("transId");
            String userKey = record.getString("userKey");
            String sessionId = record.getString("sessionId");
            // redis幂等,有效期一天,key transId+userKey, value 1是0否
            String redisKey = transId + userKey;
            String value = redisService.get(redisKey);
            if (StringUtils.isNotEmpty(value) && "1".equals(value)) {
                logger.info("current user amount record exist, transId={}", transId);
                booleanResult.setSuccess(true);
                return booleanResult.message("update amount success");
            } else {
                redisService.setex(redisKey, LOCK_TIME_OUT, "0");
            }
            VerifyLineAssignVo lineAssignVo = JSON.parseObject(record.toJSONString(), VerifyLineAssignVo.class);
            VerifyUserLineManagement userLineRec = userLineManagementService.getByUserKey(lineAssignVo.getSourceSystem(), lineAssignVo.getUserKey());
            if (userLineRec != null) {
                JSONObject verifyResult = param.getJSONObject("verifyResult");
                // add version
                verifyResult.put("version", userLineRec.getVersion());
            }
            booleanResult = saveUserAmount(param, lineAssignVo.getSourceSystem(), lineAssignVo.getUserKey(),
                    lineAssignVo.getLoanKey(), lineAssignVo.getLoanId(),sessionId);
            if (!booleanResult.isSuccess()) {
                return booleanResult;
            }
            redisService.setex(redisKey,LOCK_TIME_OUT, "1");
        } catch (Exception e) {
            logger.error("process assign amount result failed,param={}", param.toJSONString(), e);
            booleanResult.setSuccess(false);
            booleanResult.setMessage("update amount failed");
        }
        return booleanResult;
    }

    /**
     * 处理irr24流程用户额度---用户额度只落临时表
     *          --irr24流程验证完毕可去除
     * @param param
     * @return
     */
    private BooleanResult handleIrr24Amount(JSONObject param) {
        BooleanResult booleanResult = new BooleanResult(false);
        try {
            // check幂等；保存结果；更新幂等信息
            JSONObject record = param.getJSONObject("amountParams");
            String transId = record.getString("transId");
            String userKey = record.getString("userKey");

            VerifyLineAssignVo lineAssignVo = JSON.parseObject(record.toJSONString(), VerifyLineAssignVo.class);
            JSONObject verifyResult = param.getJSONObject("verifyResult");
            String strategyType = param.getString("strategyType");
            VerifyUserLineManagement userLineManagement = JSON.parseObject(verifyResult.toJSONString(), VerifyUserLineManagement.class);
            // id置空
            userLineManagement.setId(null);
            userLineManagement.setUserKey(userKey);
            userLineManagement.setSourceSystem(lineAssignVo.getSourceSystem());
            userLineManagement.setLoanId(lineAssignVo.getLoanId());
            userLineManagement.setLoanKey(lineAssignVo.getLoanKey());
            userLineManagement.setStrategyType(strategyType);

            userLineManagement.setStatus(true);
            userLineManagement.setLineAssignTime(new Date());
            userLineManagement.setCreateTime(new Date());
            userLineManagement.setUpdateTime(new Date());
            if (userLineManagement.getTestCode().length() > 512) {
                userLineManagement.setTestCode(userLineManagement.getTestCode().substring(0, 512));
            }
            if (userLineManagement.getSegmentCode().length() > 512) {
                userLineManagement.setSegmentCode(userLineManagement.getSegmentCode().substring(0, 512));
            }
            // 给irr24表里查一条记录
            Long aLong = irr24userLineManagementMapper.saveUserLineManagemert(userLineManagement);
            if (aLong > 0) {
                booleanResult.setSuccess(true);
            }
        } catch (Exception e) {
            logger.error("process assign amount result failed,param={}", param.toJSONString(), e);
            booleanResult.setSuccess(false);
            booleanResult.setMessage("update amount failed");
        }
        return booleanResult;
    }
}
