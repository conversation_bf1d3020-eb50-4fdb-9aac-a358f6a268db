package com.youxin.risk.verify.utils;

import java.io.ByteArrayInputStream;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.youxin.risk.verify.annotion.XmlEntity;
import com.youxin.risk.verify.annotion.XmlNode;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.fastjson.JSONObject;

public class MiddXmlUtils {

	private static final Logger LOG = LoggerFactory.getLogger(MiddXmlUtils.class);;


	@SuppressWarnings("unchecked")
    public static Map xml2map(String xmlStr, boolean needRootKey)
            throws DocumentException {
        Document doc = DocumentHelper.parseText(xmlStr);
        Element root = doc.getRootElement();
        Map<String, Object> map = MiddXmlUtils.xml2map(root);
        if (root.elements().size() == 0 && root.attributes().size() == 0) {
            return map;
        }
        if (needRootKey) {
            //在返回的map里加根节点键（如果需要）
            Map<String, Object> rootMap = new HashMap<String, Object>();
            rootMap.put(root.getName(), map);
            return rootMap;
        }
        return map;
    }

    /**
     * xml转map 不带属性
     *
     * @param e
     * @return
     */

    private static Map xml2map(Element e) {
        Map map = new JSONObject();
        List list = e.elements();
        if (list.size() > 0) {
            for (int i = 0; i < list.size(); i++) {
                Element iter = (Element) list.get(i);
                List mapList = new ArrayList();

                if (iter.elements().size() > 0) {
                    Map m = MiddXmlUtils.xml2map(iter);
                    if (map.get(iter.getName()) != null) {
                        Object obj = map.get(iter.getName());
                        if (!(obj instanceof List)) {
                            mapList = new ArrayList();
                            mapList.add(obj);
                            mapList.add(m);
                        }
                        if (obj instanceof List) {
                            mapList = (List) obj;
                            mapList.add(m);
                        }
                        map.put(iter.getName(), mapList);
                    } else {
						map.put(iter.getName(), m);
					}
                } else {
                    if (map.get(iter.getName()) != null) {
                        Object obj = map.get(iter.getName());
                        if (!(obj instanceof List)) {
                            mapList = new ArrayList();
                            mapList.add(obj);
                            mapList.add(iter.getText());
                        }
                        if (obj instanceof List) {
                            mapList = (List) obj;
                            mapList.add(iter.getText());
                        }
                        map.put(iter.getName(), mapList);
                    } else {
						map.put(iter.getName(), iter.getText());
					}
                }
            }
        } else {
			map.put(e.getName(), e.getText());
		}
        return map;
    }

    public static String toXmlString(Object obj)
            throws IllegalArgumentException, IllegalAccessException {
    	 if (obj == null) {
             return "Invalid";
         }
         StringBuilder sb = new StringBuilder();
         Class<?> c = obj.getClass();
         if (obj instanceof Map) {
         	Set<?> set = ((Map<?, ?>) obj).keySet();
         	for (Object name : set) {
         		String key = (String) name;
         		sb.append(String.format("<%s>", key));
         		sb.append(MiddXmlUtils.toXmlString(((Map<?, ?>) obj).get(key)));
         		sb.append(String.format("</%s>", key));
         	}
         } else if (obj instanceof List) {
             for (Object o : (List<Object>) obj) {
                 sb.append(MiddXmlUtils.toXmlString(o));
             }
         } else if (c.isAnnotationPresent(XmlEntity.class)) {
             XmlEntity[] XmlEntitys = c.getAnnotationsByType(XmlEntity.class);
             String className = null;
             if (XmlEntitys != null && XmlEntitys.length > 0) {
                 for (XmlEntity xmlEntity : XmlEntitys) {
                     if (!StringUtils.isEmpty(xmlEntity.name())) {
                         className = xmlEntity.name();
                         break;
                     }
                 }
             }
             if (className != null) {
                 sb.append(String.format("<%s>", className));
             }

             for (Field field : c.getDeclaredFields()) {
                 XmlNode[] annotations = field
                     .getDeclaredAnnotationsByType(XmlNode.class);
                 if (annotations != null && annotations.length > 0) {
                     String nodeName = null;
                     for (XmlNode xmlNode : annotations) {
                         if (!StringUtils.isEmpty(xmlNode.name())) {
                             nodeName = xmlNode.name();
                             break;
                         }
                     }

                     field.setAccessible(true);

                     if (nodeName != null && field.get(obj) != null) {
                         if (field.getType() == List.class) {
                             sb.append(String.format("<%s>", nodeName));
                             List<Object> list = (List<Object>) field.get(obj);
                             for (Object o : list) {
                                 sb.append(MiddXmlUtils.toXmlString(o));
                             }
                             sb.append(String.format("</%s>", nodeName));
                         } else {
                             sb.append(String.format("<%s>%s</%s>", nodeName,
                             		MiddXmlUtils.toXmlString(field.get(obj)), nodeName));
                         }
                     }
                 }
             }
             if (className != null) {
                 sb.append(String.format("</%s>", className));
             }
         } else if (obj instanceof Boolean) {
         	if (Boolean.TRUE.equals(obj)) {
         		sb.append("True");
         	} else {
         		sb.append("False");
         	}
         } else {
             sb.append(MiddXmlUtils.replaceXmlIllegal(obj.toString()));
         }
         return sb.toString();
    }

    public static String replaceXmlIllegal(String xml) {
		if (StringUtils.isBlank(xml)) {
			return xml;
		}

		return xml.replaceAll("&", "&amp;");
	}

    /**
	 * 将XML合并入已有的XML
	 *
	 * @param xmlReport
	 * @param newXml
	 * @param nodePath
	 * @return
	 */
    public static String mergeXmlToXml(String xmlReport, String newXml,
			String nodePath) {
		Document document = null;
		try {
			document = new SAXReader().read(new ByteArrayInputStream(xmlReport.getBytes()));
		} catch (DocumentException e) {
			LOG.error("合并xml失败，", e);
		}
		if (document == null) {
			return xmlReport;
		}

		String[]  nodes = nodePath.split("/");
		if (nodes == null || nodes.length == 0) {
			LOG.error("feature路径{}无效", nodePath);
			return xmlReport;
		}

		Element root = document.getRootElement();
		Element parent = root;
		for(int i = 1; i < nodes.length; i++) {
			Element element = parent.element(nodes[i]);
			if (element == null) {
				element = parent.addElement(nodes[i]);
			}
			parent = element;
		}

		if (newXml != null) {
			try {
				Document featureDocument = new SAXReader().read(new ByteArrayInputStream(newXml.getBytes()));
				Element featureRoot = featureDocument.getRootElement();
				String name = featureRoot.getName();
				Element oldElemnet = parent.element(name);
				if(oldElemnet != null){
					parent.remove(oldElemnet);
				}

				parent.add(featureRoot);
			} catch (DocumentException e) {
				LOG.error("xml合并失败，", e);
			}
		}

		String xml = document.asXML();
		xml = xml.replace("<?xml version=\"1.0\" encoding=\"UTF-8\"?>", "");
		xml = xml.replace("\r", "");
		xml = xml.replace("\n", "");
		return xml;
	}


    public static String getPointXmlPart(String xml,String label) {
        String context = "Invalid";
        //正则表达式
        String rgex = "<" + label + ">(.*?)</" + label + ">";
        Pattern pattern = Pattern.compile(rgex);// 匹配的模式    
        Matcher m = pattern.matcher(xml);
        //匹配的有多个
        List<String> list = new ArrayList<String>();
        while (m.find()) {
            int i = 1;
            list.add(m.group(i));
            i++;
        }
        //只要匹配的第一个
        if (list.size() > 0) {
            context = list.get(0);
        }
        return context;
    }


}
