package com.youxin.risk.verify.service.impl;

import com.youxin.risk.commons.model.verify.VerifyUserPreloan;
import com.youxin.risk.commons.utils.ObjectTransferUtils;
import com.youxin.risk.verify.constants.VerifyConstants;
import com.youxin.risk.verify.service.GatewaySystemService;
import com.youxin.risk.verify.service.VerifyAmountService;
import com.youxin.risk.verify.service.VerifyLineMangService;
import com.youxin.risk.verify.service.VerifyUserPreloanService;
import com.youxin.risk.verify.vo.VerifyLineCommonVo;
import com.youxin.risk.verify.vo.VerifyUserPreloanVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class VerifyAmountServiceImpl implements VerifyAmountService {

    private static final Logger LOG = LoggerFactory.getLogger(VerifyAmountServiceImpl.class);

    @Autowired
    private VerifyUserPreloanService preloanService;

    @Autowired
    private VerifyLineMangService verifyLineMangService;

    @Autowired
    private GatewaySystemService gatewaySystemService;

    /**
     * 额度管控-放款
     */
    @Override
    public void verifyPreloan(VerifyUserPreloanVo vo) {
        // 流程：1. 将业务端提交的请求存入数据库中；2.调用账务系统，获取账单和还款计划；3.调用策略计算额度4.返回给业务端
        try {
            // 根据loanId获取loanKey
            String loanKey = verifyLineMangService.getLoanKeyFromVerifyResult(vo.getUserKey(), vo.getLoanId(), vo.getLoanKey());
            vo.setLoanKey(loanKey);
            VerifyUserPreloan verifyUserPreloan = ObjectTransferUtils.transferObject(vo, VerifyUserPreloan.class);
            this.preloanService.saveOrUpdateRec(verifyUserPreloan);
            splitFlow(vo, VerifyConstants.EVENT_CODE_LEND);
        } catch (Exception e) {
            LOG.error("verify preloan error", e);
        }
    }

    @Override
    public Boolean splitFlow(VerifyLineCommonVo verifyLineCommonVo, String eventCode) {
        verifyLineCommonVo.setFlowType(VerifyConstants.FLOW_TYPE_SPLIT_AMOUNT);
        callGateWay(verifyLineCommonVo, eventCode);
        return true;
    }

    private void callGateWay(VerifyLineCommonVo verifyLineCommonVo, String eventCode) {
        try {
            verifyLineCommonVo.setEventCode(eventCode);
            Boolean success = this.gatewaySystemService.postData(verifyLineCommonVo, VerifyConstants.GW_ICODE_AMOUNT);
            if (success) {
                LOG.info("call gateway success,userKey={},loanId={}", verifyLineCommonVo.getUserKey(), verifyLineCommonVo.getLoanId());
            } else {
                LOG.warn("call gateway error,userKey={},loanId={}", verifyLineCommonVo.getUserKey(), verifyLineCommonVo.getLoanId());
            }
        } catch (Exception e) {
            LOG.warn("call gateway exception,userKey={},loanId={}", verifyLineCommonVo.getUserKey(), verifyLineCommonVo.getLoanId());
        }
    }

}
