package com.youxin.risk.verify.hbase;

import java.util.Date;

import com.youxin.risk.commons.model.verify.VerifyAnalysisReport;
import com.youxin.risk.hbase.HbaseTable;
import com.youxin.risk.hbase.utils.HbaseUtil;
import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.hbase.HConstants;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.util.Bytes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;


public class VerifyAnalysisReportHB extends VerifyAnalysisReport implements HbaseTable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	private static Logger LOG = LoggerFactory
	        .getLogger(VerifyAnalysisReportHB.class);
	
	public VerifyAnalysisReportHB(VerifyAnalysisReport report) {
		BeanUtils.copyProperties(report, this);
	}
	
	public VerifyAnalysisReportHB() {
	}
	
	
	public String family = "data";
	
	public byte[] familyBytes = Bytes.toBytes(family);

	@Override
	public byte[] getRowkey() {
		return Bytes.toBytes(this.getLoanKey());
	}
	

	@Override
	public String getTableName() {
		return "verify_analysis_report_hb";
	}

	@Override
	public Put buildPut() {
		preBuild();
		byte[] rowkey = Bytes.toBytes(reverseLoanKey(this.getLoanKey()));
		Put put = new Put(rowkey);
		if(this.getId() != null) {
			put.addColumn(familyBytes,
		            Bytes.toBytes("id"), Bytes.toBytes(this.getId()));
		}
        
        if(this.getLoanId() != null) {
        	put.addColumn(familyBytes,
                    Bytes.toBytes("loanId"), Bytes.toBytes(this.getLoanId()));
        }
        
        if(!StringUtils.isBlank(this.getLoanKey())) {
        	put.addColumn(familyBytes,
                    Bytes.toBytes("loanKey"), Bytes.toBytes(this.getLoanKey()));
        }
        
        if(!StringUtils.isBlank(this.getUserKey())) {
        	put.addColumn(familyBytes,
                    Bytes.toBytes("userKey"), Bytes.toBytes(this.getUserKey()));
        }
        
        if(!StringUtils.isBlank(this.getAnalysisReport())) {
        	put.addColumn(familyBytes,
                    Bytes.toBytes("analysisReport"), Bytes.toBytes(this.getAnalysisReport()));
        }
        if(this.getIsRetry() != null) {
        	put.addColumn(familyBytes,
                    Bytes.toBytes("isRetry"), Bytes.toBytes(this.getIsRetry()));
        }
        if(!StringUtils.isBlank(this.getRaReportId())) {
        	put.addColumn(familyBytes,
                    Bytes.toBytes("raReportId"), Bytes.toBytes(this.getRaReportId()));
        }
        if(this.getUpdateTime() != null) {
        	put.addColumn(familyBytes,
                    Bytes.toBytes("updateTime"), Bytes.toBytes(this.getUpdateTime().getTime()));
        }
        
        if(this.getCreateTime() != null) {
        	put.addColumn(familyBytes,
                    Bytes.toBytes("createTime"), Bytes.toBytes(this.getCreateTime().getTime()));
        }
        if(this.getVersion() != null) {
        	put.addColumn(familyBytes,
                    Bytes.toBytes("version"), Bytes.toBytes(this.getVersion()));
        }
        
        return put;
	}
	
	
	private void preBuild() {
		if(this.getCreateTime() == null) {
			this.setCreateTime(new Date());
		}
		
		if(this.getUpdateTime() == null) {
			this.setUpdateTime(new Date());
		}
	}
	
	@Override
	public void readValue(Result value) {
		this.setId(HbaseUtil.getValueInt(value.getValue(familyBytes, Bytes.toBytes("id"))));
		this.setLoanId(HbaseUtil.getValueInt(value.getValue(familyBytes, Bytes.toBytes("loanId"))));
		String loanKey = HbaseUtil.getValue(value.getValue(familyBytes, Bytes.toBytes("loanKey")));
		this.setLoanKey(loanKey);
		this.setUserKey(HbaseUtil.getValue(value.getValue(familyBytes, Bytes.toBytes("userKey"))));
		this.setAnalysisReport(HbaseUtil.getValue(value.getValue(familyBytes, Bytes.toBytes("analysisReport"))));
		this.setIsRetry(HbaseUtil.getValueBoolean(value.getValue(familyBytes, Bytes.toBytes("isRetry"))));
		this.setRaReportId(HbaseUtil.getValue(value.getValue(familyBytes, Bytes.toBytes("raReportId"))));
		this.setUpdateTime(HbaseUtil.getValueDate(value.getValue(familyBytes, Bytes.toBytes("updateTime"))));
		this.setCreateTime(HbaseUtil.getValueDate(value.getValue(familyBytes, Bytes.toBytes("createTime"))));
		this.setVersion(HbaseUtil.getValueInt(value.getValue(familyBytes, Bytes.toBytes("version"))));
		
	}
	
	@Override
	public Get buildGet(String rowkeyStr) {
		//反转操作
		byte[] rowkey = Bytes.toBytes(reverseLoanKey(rowkeyStr));
        Get get = new Get(rowkey);
        get.setPriority(HConstants.HIGH_QOS);
        get.setLoadColumnFamiliesOnDemand(true);
        try {
            get.setMaxVersions(1);
        }catch (Exception e) {
			LOG.info("get.setMaxVersions(1) error",e);
		}
        get.addFamily(familyBytes);
        return get;
    }

	@Override
	public void readValues(ResultScanner resultScanner) {

	}

	@Override
	public Scan buildScan() {
		return null;
	}

	@Override
	public String getRowKey() {
		return null;
	}


	/**
	 * 反转操作
	 * @param loanKey
	 */
	private String reverseLoanKey(String loanKey) {
		char[] chars = loanKey.toCharArray();
		StringBuilder builder = new StringBuilder();
		for(int i = chars.length-1 ; i>=0 ; i--) {
			builder.append(chars[i]);
		}
		
		return builder.toString();
	}
	
	
	
	

}
