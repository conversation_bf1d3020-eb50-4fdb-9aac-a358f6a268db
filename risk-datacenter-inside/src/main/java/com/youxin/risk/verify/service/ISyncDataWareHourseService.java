package com.youxin.risk.verify.service;

public interface ISyncDataWareHourseService {

    /**
     * 同步方法
     *
     * @param date
     * @param dataType
     * @param dataTypeDesc
     * @param hbaseTableName
     * @param rowKeyColumns
     * @param columns
     * @param hiveCountSql
     * @param hiveQuerySql
     * @param slave
     */
    void syncToDB(String date, String dataType, String dataTypeDesc, String hbaseTableName,
                  String rowKeyColumns, String columns, String hiveCountSql, String hiveQuerySql, boolean slave);

    /**
     * 同步到mysql
     *
     * @param date
     * @param dataType
     * @param hiveQuerySql
     */
    default void syncToMysql(String date, String dataType, String hiveQuerySql) {
    }


}
