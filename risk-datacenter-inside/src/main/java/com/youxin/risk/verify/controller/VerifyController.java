package com.youxin.risk.verify.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.youxin.risk.commons.model.verify.VerifyUserLineManagement;
import com.youxin.risk.commons.utils.JsonUtils;
import com.youxin.risk.commons.utils.LogUtil;
import com.youxin.risk.commons.utils.LoggerProxy;
import com.youxin.risk.commons.vo.BooleanResult;
import com.youxin.risk.commons.vo.verify.LoanAuditStrategyResultVo;
import com.youxin.risk.datacenter.pojo.JsonResultVo;
import com.youxin.risk.verify.service.*;
import com.youxin.risk.verify.service.impl.ApiUserLineManagementService;
import com.youxin.risk.verify.service.impl.UserLineShardingService;
import com.youxin.risk.verify.service.impl.VerifyUserLineManagementService;
import com.youxin.risk.verify.utils.ApiCheck;
import com.youxin.risk.verify.vo.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;


@RestController
@RequestMapping("/verify")
public class VerifyController extends BaseController {

    private static final Logger LOG = LoggerFactory.getLogger(VerifyController.class);

    @Autowired
    private VerifyLendService verifyLendService;

    @Autowired
    private VerifyLineMangService verifyLineMangService;

    @Autowired
    private VerifyAmountService amountService;

    @Autowired
    private VerifyStrategyService verifyStrategyService;

    @Resource(name = "amountResultProcesserProxy")
    private AmountResultProcesser amountResultProcesserProxy;

    @Autowired
    private VerifyUserLineManagementService verifyUserLineManagementService;

    @Resource
    private UserLineShardingService userLineShardingService;

    @Resource
    private ApiUserLineManagementService apiUserLineManagementService;

    @ResponseBody
    @RequestMapping("/notifyLendTemporary")
    public JsonResultVo handleLendTemporary() {
        try {
            LOG.info("notifyLendTemporary request ");
            this.verifyLendService.handleTemporaryLendRequest();

            return JsonResultVo.success();
        } catch (Exception e) {
            LOG.error("notifyLendTemporary error", e);
            return JsonResultVo.error();
        }
    }


    /**
     * 引擎通知放款审核结果，为避免小流量时数仓抽取不到数据
     *
     * @param loanAuditVo
     * @return
     */
    @RequestMapping("/lendResult")
    public JsonResultVo saveLendResult(@RequestBody LoanAuditStrategyResultVo loanAuditVo) {
        try {
            LOG.info("lend audit result request param={}", JsonUtils.toJson(loanAuditVo));
            this.verifyLendService.loanAuditStrategyResult(loanAuditVo);
            return JsonResultVo.success();
        } catch (Exception e) {
            LOG.error("lend audit result save error", e);
            return JsonResultVo.error();
        }

    }

    /**
     * 好分期老系统额度更新接口
     *
     * @param param param
     * @return result
     */
    @RequestMapping("/updateAmount")
    public JsonResultVo updateAmount(@RequestBody JSONObject param) {
        try {
            String logId = UUID.randomUUID().toString();
            LogUtil.bindLogId(logId);
            BooleanResult result = amountResultProcesserProxy.process(param);
            if (!result.isSuccess()){
                LOG.info("logId={},saveUserAmountRecord request param={}", logId, param.toJSONString());
            }
            return JsonResultVo.success(result.getMessage()).addData("result", result.isSuccess());
        } catch (Exception e) {
            LOG.error("saveUserAmountRecord deal error,param={}", param.toJSONString(), e);
            return JsonResultVo.error();
        } finally {
            LogUtil.unbindLogId();
        }
    }

    /**
     * API额度定制插入接口
     *
     * @param param param
     * @return result
     */
    @RequestMapping("/updateApiAmount")
    public JsonResultVo updateApiAmount(@RequestBody JSONObject param) {
        try {
            String logId = UUID.randomUUID().toString();
            LogUtil.bindLogId(logId);
            BooleanResult result = apiUserLineManagementService.updateApiAmount(param);
            if (!result.isSuccess()){
                LOG.info("logId={},updateApiAmount request param={}", logId, param.toJSONString());
            }
            return JsonResultVo.success(result.getMessage()).addData("result", result.isSuccess());
        } catch (Exception e) {
            LOG.error("updateApiAmount deal error,param={}", param.toJSONString(), e);
            return JsonResultVo.error();
        } finally {
            LogUtil.unbindLogId();
        }
    }

    /**
     * 黑卡、融担接收微财推送的跑批结果并回调到对应的业务线
     */
    @RequestMapping("/remote/updateAmount")
    public JsonResultVo remoteUpdateAmount(@RequestBody JSONObject param) {
        try {
            String logId = UUID.randomUUID().toString();
            LogUtil.bindLogId(logId);
            BooleanResult result = amountResultProcesserProxy.processAndNotify(param);
            if (!result.isSuccess()){
                LOG.info("logId={},saveUserAmountRecord request param={}", logId, param.toJSONString());
            }
            return JsonResultVo.success(result.getMessage()).addData("result", result.isSuccess());
        } catch (Exception e) {
            LOG.error("saveUserAmountRecord deal error,param={}", param.toJSONString(), e);
            return JsonResultVo.error();
        } finally {
            LogUtil.unbindLogId();
        }
    }

    @ResponseBody
    @RequestMapping("/sufexpire")
    public JsonResultVo verifySufexpire(@RequestBody VerifyLineExpireVo vo) {
        try {
            // 校验参数
            List<String> nullParamList = ApiCheck.checkNullParameters(vo);
            if (nullParamList.size() != 0) {
                LOG.info("lineMang sufexpire has null param [{}]", nullParamList);
                return JsonResultVo.error().addData("missingParameters", nullParamList);
            }
            LOG.info("lineMang sufexpire params={}", JSON.toJSONString(vo));
            this.verifyLineMangService.verifySufexpire(vo);
            return JsonResultVo.success();
        } catch (Exception e) {
            LOG.error("LineMang sufexpire error", e);
            return JsonResultVo.error();
        }
    }

    /**
     * 放款
     *
     * <AUTHOR>
     */
    @ResponseBody
    @RequestMapping("/preloan")
    public JsonResultVo verifyPreloan(@RequestBody VerifyUserPreloanVo vo) {
        String logId = UUID.randomUUID().toString();
        LogUtil.bindLogId(logId);
        // 放款
        LOG.info("logId={},verify preloan param : {}", logId, JsonUtils.toJson(vo));
        try {
            this.checkChannelCode(vo);
            // 校验参数
            List<String> nullParamList = ApiCheck.checkNullParameters(vo);
            if (nullParamList.size() != 0) {
                LOG.info("verify preloan has null param [{}]", nullParamList);
                return JsonResultVo.error().addData("missingParameters", nullParamList);
            }
            this.amountService.verifyPreloan(vo);
            return JsonResultVo.success();
        } catch (Exception e) {
            LOG.error("verify preloan error", e);
            return JsonResultVo.error();
        } finally {
            LogUtil.unbindLogId();
        }
    }


    /**
     * 研究平台同步上线策略
     */
    @RequestMapping("/strategy/online")
    @ResponseBody
    public JsonResultVo online(@RequestBody StrategySubmitVo submitVo) {
        try {
            LOG.info("submit StrategySubmitVo:{},", JSON.toJSONString(submitVo));
            StrategyVo strategy = submitVo.getStrategy();
            StrategyCodeVo strategyCode = submitVo.getStrategyCode();

            this.verifyStrategyService.saveStrategy(strategyCode.getStrategyCode(), strategy.getRemark(), strategy.getType(),
                    strategy.getDeveloper(), strategy.getModelId(), strategy.getRiskApproval(), strategy.getBusinessApproval(), strategy.getManagerApproval(), strategyCode.getId().intValue());
            LOG.info("online success, id:{}", strategyCode.getId());
            return JsonResultVo.success();
        } catch (Throwable e) {
            LOG.error("online error.", e);
            throw e;
        }
    }

    /**
     * 借款页提额特征
     */
    @RequestMapping(value = "/getLoanPageAmountChange", method = RequestMethod.POST)
    public JsonResultVo getLoanPageAmountChange(@RequestBody JSONObject params) {
        LoggerProxy.info("getLoanPageAmountChange", LOG, "request message={}", params.toJSONString());
        String userKey = params.getString("userKey");

        Map<String, Object> map = verifyUserLineManagementService.getLoanPageAmountChange(userKey);

        LoggerProxy.info("getLoanPageAmountChange", LOG, "response message={}", map);

        return JsonResultVo.success(map);
    }

    /**
     * 获取用户额度分表数据
     *
     * @param params userKey
     * @return VerifyUserLineManagement
     */
    @RequestMapping(value = "/getVerifyUserLineManagement", method = RequestMethod.POST)
    public JsonResultVo getVerifyUserLineManagement(@RequestBody JSONObject params){
        LoggerProxy.info("getVerifyUserLineManagement", LOG, "request message={}", params.toJSONString());
        String userKey = params.getString("userKey");
        String sourceSystem = params.getString("sourceSystem");
        VerifyUserLineManagement verifyUserLineManagement = verifyUserLineManagementService.getByUserKey(sourceSystem,userKey);
        if (verifyUserLineManagement == null) {
            LoggerProxy.warn("userLineNotFound", LOG, "user line not found, userKey={}", userKey);
            return JsonResultVo.success("userLineNotFound");
        }
        Map<String,Object> result = Maps.newHashMap();
        result.put("userLine",verifyUserLineManagement);
        return JsonResultVo.success(result);
    }

     /**
       * 获取用户最近一笔授信
       *
       * <AUTHOR>
       * @since 2024/4/9 14:28
       */
    @RequestMapping(value = "/getLastVerify", method = RequestMethod.POST)
    public JsonResultVo getLastVerify(@RequestBody JSONObject params){
        LoggerProxy.info("getLastVerify", LOG, "request message={}", params.toJSONString());
        String userKey = params.getString("userKey");
        Assert.notNull(userKey,"userKey不能为空");
        Map<String,Object> result = userLineShardingService.selectLastVerifyByUserKey(userKey);
        return JsonResultVo.success(result);
    }

    /**
     * 获取进件审核额度策略类型
     */
    @RequestMapping(value = "/getStrategyType", method = RequestMethod.POST)
    public JsonResultVo getStrategyType(@RequestBody JSONObject params){
        LoggerProxy.info("getStrategyType", LOG, "request message={}", params.toJSONString());
        String userKey = params.getString("userKey");
        VerifyUserLineManagement verifyUserLineManagement = verifyUserLineManagementService.getStrategyType(userKey);
        if (verifyUserLineManagement == null) {
            LoggerProxy.warn("userLineNotFound", LOG, "user line not found, userKey={}", userKey);
            return JsonResultVo.success();
        }
        Map<String,Object> result = Maps.newHashMap();
        result.put("strategyType",verifyUserLineManagement.getStrategyType());
        result.put("lendBranchOfNewloan", getLendBranchOfNewloan(verifyUserLineManagement.getStrategyType()));
        return JsonResultVo.success(result);
    }

    private String getLendBranchOfNewloan(String strategyType) {
        List<String> irr36StrategyType = Lists.newArrayList("IRR36_VERIFY_AMOUNT", "AMOUNT_ASSIGN_IRR_ALL_FL_36", "WHITE_LIST_VERIFY_AMOUNT", "AMOUNT_ASSIGN_ALL_WHITE");
        List<String> irr24StrategyType = Lists.newArrayList("IRR24_VERIFY_AMOUNT", "AMOUNT_ASSIGN_IRR_ALL_FL");
        if (irr36StrategyType.contains(strategyType)) {
            return "36";
        } else if (irr24StrategyType.contains(strategyType)) {
            return "24";
        }

        return null;
    }
}
