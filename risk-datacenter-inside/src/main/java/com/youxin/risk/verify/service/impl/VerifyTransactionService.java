package com.youxin.risk.verify.service.impl;

import com.youxin.risk.verify.model.VerifyTransaction;
import com.youxin.risk.verify.service.VerifyNotifyService;
import com.youxin.risk.verify.vo.TransManualResultVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;


/**
 * 交易审核
 *
 * <AUTHOR>
 * @version 创建时间：2018年7月18日 下午9:40:42
 */
@Service
public class VerifyTransactionService {

    private static final Logger LOG = LoggerFactory.getLogger(VerifyTransactionService.class);

    @Autowired
    private VerifyTransactionDataService verifyTransactionDataService;

    @Autowired
    private VerifyNotifyService verifyNotifyService;

    /**
     * 交易人工审核结果处理
     *
     * @param vo
     */
    public void handleManualVerifyResult(TransManualResultVo vo) {
        long startTime = System.currentTimeMillis();
        //change status
        VerifyTransaction trans = this.verifyTransactionDataService.get(vo.getTransactionId());
        if(trans == null) {
            LOG.error("can not find transaction in manual verify by id={},orderNo={}",vo.getTransactionId(),vo.getOrderNo());
            return;
        }
        if(!Boolean.TRUE.equals(trans.getIsAutoPassed())) {
            LOG.warn("autopassed status is not true in manual trans notify,autopassed={},orderNo={}",trans.getIsAutoPassed(),trans.getOrderNo());
            return;
        }
        //check final status
        if(trans.getIsFinalPassed() != null) {
            LOG.warn("trans verify has final status yet,transctionId={},orderNo={}",vo.getTransactionId(),vo.getOrderNo());
            return;
        }
        trans.setIsManualPassed(vo.getIsPass());
        trans.setIsFinalPassed(vo.getIsPass());
        trans.setFinalVerifyTime(new Date());
        this.verifyTransactionDataService.update(trans);

        LOG.info("trans time cost type=manualHandle cost={} orderNo={}",System.currentTimeMillis() - startTime,vo.getOrderNo());
        //callback business
        this.verifyNotifyService.notifyTransAudit(trans);

        trans.setIsNotified(true);
        this.verifyTransactionDataService.update(trans);
    }

}
