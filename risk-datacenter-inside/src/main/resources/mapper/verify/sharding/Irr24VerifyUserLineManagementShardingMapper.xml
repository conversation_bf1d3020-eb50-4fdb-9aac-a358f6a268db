<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.verify.sharding.mapper.Irr24VerifyUserLineManagementShardingMapper">

    <resultMap id="BaseResultMap" type="com.youxin.risk.commons.model.verify.VerifyUserLineManagement">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="user_key" property="userKey"/>
        <result column="source_system" property="sourceSystem"/>
        <result column="credit_line" property="creditLine"/>
        <result column="avail_line" property="availLine"/>
        <result column="util_line" property="utilLine"/>
        <result column="loan_line" property="loanLine"/>
        <result column="loan_avail_line" property="loanAvailLine"/>
        <result column="loan_actual_line" property="loanActualLine"/>
        <result column="loan_util_line" property="loanUtilLine"/>
        <result column="loan_rate" property="loanRate"/>
        <result column="loan_period" property="loanPeriod"/>
        <result column="bt_line" property="btLine"/>
        <result column="bt_avail_line" property="btAvailLine"/>
        <result column="bt_actual_line" property="btActualLine"/>
        <result column="bt_util_line" property="btUtilLine"/>
        <result column="bt_rate" property="btRate"/>
        <result column="bt_period" property="btPeriod"/>
        <result column="shop_line" property="shopLine"/>
        <result column="shop_avail_line" property="shopAvailLine"/>
        <result column="shop_actual_line" property="shopActualLine"/>
        <result column="shop_util_line" property="shopUtilLine"/>
        <result column="shop_rate" property="shopRate"/>
        <result column="shop_period" property="shopPeriod"/>
        <result column="period_line_rate" property="periodLineRate"/>
        <result column="account_status" property="accountStatus"/>
        <result column="is_closed" property="isClosed"/>
        <result column="user_point" property="userPoint"/>
        <result column="user_level" property="userLevel"/>
        <result column="line_assign_time" property="lineAssignTime"/>
        <result column="strategy_id" property="strategyId"/>
        <result column="strategy_type" property="strategyType"/>
        <result column="is_active" property="isActive"/>
        <result column="last_id" property="lastId"/>
        <result column="loan_id" property="loanId"/>
        <result column="loan_key" property="loanKey"/>
        <result column="reason_code" property="reasonCode"/>
        <result column="segment_code" property="segmentCode"/>
        <result column="test_code" property="testCode"/>
        <result column="status" property="status"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="version" property="version"/>
        <result column="ext1" property="ext1"/>
        <result column="tmp_shop_line_end_time" property="tmpShopLineEndTime"/>
        <result column="tmp_loan_line_end_time" property="tmpLoanLineEndTime"/>
    </resultMap>


    <sql id="Base_Column_List">
        id,user_key, source_system, credit_line, avail_line, util_line, loan_line, loan_avail_line, loan_actual_line,
        loan_util_line, loan_rate, loan_period, bt_line, bt_avail_line, bt_actual_line, bt_util_line, bt_rate,
        bt_period, shop_line, shop_avail_line, shop_actual_line, shop_util_line, shop_rate, shop_period,
        period_line_rate, account_status, is_closed, user_point, user_level, line_assign_time, strategy_id,
        strategy_type, is_active, last_id, loan_id, loan_key, reason_code, segment_code, test_code, status,
        remark, create_time, update_time, version, ext1, tmp_shop_line_end_time, tmp_loan_line_end_time
    </sql>

    <sql id="table_name">
        verify_user_line_management_irr24_temp
    </sql>

    <insert id="saveUserLineManagemert" parameterType="com.youxin.risk.commons.model.verify.VerifyUserLineManagement">
        insert into <include refid="table_name"/>
        (
        id,user_key, source_system, credit_line, avail_line, util_line, loan_line, loan_avail_line, loan_actual_line,
        loan_util_line, loan_rate, loan_period, bt_line, bt_avail_line, bt_actual_line, bt_util_line, bt_rate,
        bt_period, shop_line, shop_avail_line, shop_actual_line, shop_util_line, shop_rate, shop_period,
        period_line_rate, account_status, is_closed, user_point, user_level, line_assign_time, strategy_id,
        strategy_type, is_active, last_id, loan_id, loan_key, reason_code, segment_code, test_code, status,
        remark, create_time, update_time, version, ext1, tmp_shop_line_end_time, tmp_loan_line_end_time
        )
        values
        (
        #{id},#{userKey}, #{sourceSystem}, #{creditLine}, #{availLine}, #{utilLine}, #{loanLine}, #{loanAvailLine}, #{loanActualLine},
        #{loanUtilLine}, #{loanRate}, #{loanPeriod}, #{btLine}, #{btAvailLine}, #{btActualLine}, #{btUtilLine}, #{btRate},
        #{btPeriod}, #{shopLine}, #{shopAvailLine}, #{shopActualLine},#{shopUtilLine}, #{shopRate}, #{shopPeriod},
        #{periodLineRate},#{accountStatus}, #{isClosed}, #{userPoint}, #{userLevel}, #{lineAssignTime},#{strategyId},
        #{strategyType},#{isActive},#{lastId},#{loanId},#{loanKey},#{reasonCode},#{segmentCode},#{testCode},#{status},
        #{remark}, #{createTime}, #{updateTime},#{version},#{ext1},#{tmpShopLineEndTime},#{tmpLoanLineEndTime}
        )
    </insert>
</mapper>