<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.verify.mapper.VerifyTransactionMapper">
    <resultMap id="BaseResultMap" type="com.youxin.risk.verify.model.VerifyTransaction">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="user_key" jdbcType="VARCHAR" property="userKey"/>
        <result column="source_system" jdbcType="VARCHAR" property="sourceSystem"/>
        <result column="order_no" jdbcType="VARCHAR" property="orderNo"/>
        <result column="loan_id" jdbcType="INTEGER" property="loanId"/>
        <result column="loan" jdbcType="VARCHAR" property="loan"/>
        <result column="app_version" jdbcType="VARCHAR" property="appVersion"/>
        <result column="is_auto_passed" jdbcType="BOOLEAN" property="isAutoPassed"/>
        <result column="auto_verify_time" jdbcType="TIMESTAMP" property="autoVerifyTime"/>
        <result column="is_manual" jdbcType="BOOLEAN" property="isManual"/>
        <result column="is_manual_passed" jdbcType="BOOLEAN" property="isManualPassed"/>
        <result column="is_final_passed" jdbcType="BOOLEAN" property="isFinalPassed"/>
        <result column="final_verify_time" jdbcType="TIMESTAMP" property="finalVerifyTime"/>
        <result column="is_notified" jdbcType="BOOLEAN" property="isNotified"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="version" jdbcType="INTEGER" property="version"/>
    </resultMap>


    <sql id="table_name">
      verify_transaction
    </sql>

    <select id="get" resultMap="BaseResultMap">
        select * from
        <include refid="table_name"/>
        where id = #{id}
    </select>

    <update id="update" parameterType="com.youxin.risk.verify.model.VerifyTransaction">
        update
        <include refid="table_name"/>
        <set>
            <if test="userKey != null">
                user_key = #{userKey},
            </if>
            <if test="sourceSystem != null">
                source_system = #{sourceSystem},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo},
            </if>
            <if test="loanId != null">
                loan_id = #{loanId},
            </if>
            <if test="loan != null">
                loan = #{loan},
            </if>
            <if test="appVersion != null">
                app_version = #{appVersion},
            </if>
            <if test="isAutoPassed != null">
                is_auto_passed = #{isAutoPassed},
            </if>
            <if test="autoVerifyTime != null">
                auto_verify_time = #{autoVerifyTime},
            </if>
            <if test="isManual != null">
                is_manual = #{isManual},
            </if>
            <if test="isManualPassed != null">
                is_manual_passed = #{isManualPassed},
            </if>
            <if test="isFinalPassed != null">
                is_final_passed = #{isFinalPassed},
            </if>
            <if test="finalVerifyTime != null">
                final_verify_time = #{finalVerifyTime},
            </if>
            <if test="isNotified != null">
                is_notified = #{isNotified},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="version != null">
                version = #{version},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="findByUserKey" resultMap="BaseResultMap">
        select * from
        <include refid="table_name"/>
        where user_key = #{userKey} ORDER BY id DESC
        limit 1;
    </select>

</mapper>