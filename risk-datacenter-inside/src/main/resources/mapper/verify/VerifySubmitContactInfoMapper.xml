<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.verify.mapper.VerifySubmitContactInfoMapper">
    <resultMap id="BaseResultMap" type="com.youxin.risk.verify.model.VerifySubmitContactInfo">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="user_key" jdbcType="VARCHAR" property="userKey" />
        <result column="relation" jdbcType="VARCHAR" property="relation" />
        <result column="operation_log_id" jdbcType="INTEGER" property="operationLogId" />
        <result column="contact_name" jdbcType="VARCHAR" property="contactName"
                typeHandler="com.youxin.risk.commons.mybatis.ContactMaskHandler"/>
        <result column="mobile" jdbcType="VARCHAR" property="mobile"
                typeHandler="com.youxin.risk.commons.mybatis.ContactMaskHandler"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="version" jdbcType="INTEGER" property="version" />
    </resultMap>


    <sql id="table_name">
      submit_contact_info
    </sql>
    <sql id="Base_Column_List">
        id,operation_log_id,user_key,relation,contact_name,mobile,update_time,create_time,version
    </sql>


    <insert id="insert" parameterType="com.youxin.risk.verify.model.VerifySubmitContactInfo">
        insert into
        <include refid="table_name"/>
        (user_key,relation,operation_log_id,contact_name,mobile,update_time,create_time,version)
        values
        (#{userKey},#{relation},#{operationLogId},#{contactName,typeHandler=com.youxin.risk.commons.mybatis.ContactMaskHandler},
            #{mobile,typeHandler=com.youxin.risk.commons.mybatis.ContactMaskHandler},now(),now(),#{version})
    </insert>

    <select id="findLastSubmitContactInfoByUserKey" resultMap="BaseResultMap">
        select * from <include refid="table_name"/>
        WHERE user_key = #{userKey}
    </select>

    <select id="findSubmitContactInfoByOperationLogId" resultMap="BaseResultMap" >
        select
        <include refid="Base_Column_List" />
        FROM submit_contact_info WHERE operation_log_id = #{operationLogId}
        ORDER BY id DESC
    </select>

    <select id="queryById" parameterType="map" resultType="java.util.HashMap">
        select id,mobile,contact_name from submit_contact_info where id between #{start} and #{end}
    </select>

    <update id="updateById" >
        update submit_contact_info set contact_name=#{contact_name}, mobile=#{mobile}
        WHERE id = #{id}
    </update>

</mapper>