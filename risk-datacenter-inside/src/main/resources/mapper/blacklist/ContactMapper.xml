<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.blacklist.mapper.ContactMapper" >

    <sql id="table_name">
        contact
    </sql>

    <select id="queryById" parameterType="map" resultType="java.util.HashMap">
        select id, phonenum from <include refid="table_name"/> where id between #{start} and #{end}
    </select>

    <update id="updateById" >
        update <include refid="table_name"/> set phonenum = #{phonenum} WHERE id = #{id}
    </update>

</mapper>