<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.datacenter.mapper.DcDevicePriceMapper">
    <resultMap id="BaseResultMap" type="com.youxin.risk.datacenter.model.DcDevicePrice">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="device_name" property="deviceName" jdbcType="VARCHAR"/>
        <result column="actual_name" property="actualName" jdbcType="VARCHAR"/>
        <result column="device_price" property="devicePrice" jdbcType="DECIMAL"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="table_name">
      dc_device_price
    </sql>

    <sql id="save_Base_Column_List">
        device_name,actual_name,device_price,create_time,update_time
    </sql>

    <sql id="Base_Column_List">
        id, <include refid="save_Base_Column_List"/>
    </sql>


    <insert id="insert" parameterType="com.youxin.risk.datacenter.model.DcDevicePrice">
        insert into <include refid="table_name"/> (
        device_name,actual_name,device_price,create_time,update_time
        )
        values
        (
        #{deviceName},
        #{actualName},
        #{devicePrice},
        #{createTime},
        #{updateTime}
        )
    </insert>

    <update id="updateByName" parameterType="com.youxin.risk.datacenter.model.DcDevicePrice">
        update <include refid="table_name"/>
        <set >
            <if test="devicePrice != null" >
                device_price = #{devicePrice,jdbcType=DECIMAL},
            </if>
            <if test="actualName != null" >
                actual_name = #{actualName,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null" >
                update_time = #{updateTime,jdbcType=INTEGER},
            </if>
        </set>
        where device_name = #{deviceName,jdbcType=VARCHAR}
    </update>


    <select id="getByName" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from <include refid="table_name"/>
        where device_name = #{deviceName}
    </select>

    <select id="getById" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from <include refid="table_name"/>
        where id = #{id}
    </select>

    <select id="getList" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from <include refid="table_name"/>
        <![CDATA[
        where id > #{startId}
        limit #{limit}
        ]]>
    </select>

</mapper>