<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.youxin.risk.datacenter.mapper.DcSubmitAmountCreditCardMapper">

    <resultMap id="BaseResultMap" type="com.youxin.risk.datacenter.model.DcSubmitAmountCreditCard">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="operation_log_id" property="operationLogId" jdbcType="INTEGER"/>
        <result column="user_key" property="userKey" jdbcType="VARCHAR"/>
        <result column="user_name" property="userName" jdbcType="VARCHAR"/>
        <result column="idcard_number" property="idcardNumber" jdbcType="VARCHAR"/>
        <result column="card_no" property="cardNo" jdbcType="VARCHAR"/>
        <result column="bank_name" property="bankName" jdbcType="VARCHAR"/>
        <result column="reserved_mobile" property="reservedMobile" jdbcType="VARCHAR"/>
        <result column="credit_line" property="creditLine" jdbcType="INTEGER"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, operation_log_id, user_key,user_name,idcard_number, card_no, bank_name, reserved_mobile, credit_line, update_time, create_time
    </sql>


    <insert id="insert" parameterType="com.youxin.risk.commons.model.datacenter.DcSubmitCreditcard">
        <selectKey resultType="java.lang.Long" order="AFTER" keyProperty="id">
            SELECT LAST_INSERT_ID() AS id
        </selectKey>
        insert into dc_submit_amount_creditcard (operation_log_id, user_key,user_name,idcard_number,card_no, bank_name,reserved_mobile, credit_line, update_time, create_time)
        values (#{operationLogId}, #{userKey}, #{userName}, #{idcardNumber},#{cardNo}, #{bankName}, #{reservedMobile}, #{creditLine}, #{updateTime}, #{createTime})
    </insert>


    <select id="getByUserKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from dc_submit_amount_creditcard
        where user_key = #{userKey,jdbcType=VARCHAR} order  by id DESC limit 1
    </select>

    <select id="getLatestByUserKey" resultMap="BaseResultMap">
        select user_name,idcard_number,card_no,bank_name,reserved_mobile,credit_line,create_time
        from dc_submit_amount_creditcard
        where user_key = #{userKey,jdbcType=VARCHAR} order  by id DESC limit 1
    </select>

</mapper>