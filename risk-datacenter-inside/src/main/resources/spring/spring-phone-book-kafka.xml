<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
     http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
     http://www.springframework.org/schema/tx
     http://www.springframework.org/schema/tx/spring-tx-3.0.xsd
     http://www.springframework.org/schema/jee
     http://www.springframework.org/schema/jee/spring-jee-3.0.xsd
     http://www.springframework.org/schema/context
     http://www.springframework.org/schema/context/spring-context-3.0.xsd">


    <!-- spring和kafka集成相关配置 -->
    <bean id="phoneBookConsumerProperties" class="java.util.HashMap">
        <constructor-arg>
            <map>
                <entry key="bootstrap.servers" value="${kafka.dp.hosts}"/>
                <entry key="group.id" value="youxin_risk_datacenter_Group_57495827e957c6263asdffffffffff"/>
                <entry key="enable.auto.commit" value="false"/>
                <entry key="auto.commit.interval.ms" value="1000"/>
                <entry key="session.timeout.ms" value="30000"/>
                <entry key="key.deserializer" value="org.apache.kafka.common.serialization.StringDeserializer"/>
                <entry key="value.deserializer" value="org.apache.kafka.common.serialization.StringDeserializer"/>
            </map>
        </constructor-arg>
    </bean>

    <bean id="phoneBookConsumerFactory" class="org.springframework.kafka.core.DefaultKafkaConsumerFactory">
        <constructor-arg>
            <ref bean="phoneBookConsumerProperties"/>
        </constructor-arg>
    </bean>

    <bean id="phoneBookContainerProperties" class="org.springframework.kafka.listener.config.ContainerProperties">
        <constructor-arg value="${kafka.dp.topic}"/>
        <property name="messageListener" ref="phoneBookDpMsgListener"/>
        <property name="ackMode" value="MANUAL_IMMEDIATE"/>
    </bean>

    <bean id="phoneBookMessageListenerContainer" class="org.springframework.kafka.listener.KafkaMessageListenerContainer"
          init-method="doStart">
        <constructor-arg ref="phoneBookConsumerFactory"/>
        <constructor-arg ref="phoneBookContainerProperties"/>
    </bean>

    <!-- risk kafka api -->
    <bean id="phoneBookDpMsgListener" class="com.youxin.risk.commons.kafkav2.RiskKafkaAcknowledgingMessageListener">
        <property name="filters">
            <list>
                <ref bean="dpMsgPaserFilter"/>
            </list>
        </property>
        <property name="handler" ref="phoneBookDpMsgHander"/>
    </bean>

    <!-- 消息处理器，继承BaseKafKaMessageHandler，注入RetryableJedis，会进行判重操作，重复的请求不再处理 -->
    <bean id="phoneBookDpMsgHander" class="com.youxin.risk.datacenter.service.DataPlatformPhoneBookMessageHandler">
    	<property name="retryableJedis" ref="retryableJedis"/>
    </bean>



</beans>