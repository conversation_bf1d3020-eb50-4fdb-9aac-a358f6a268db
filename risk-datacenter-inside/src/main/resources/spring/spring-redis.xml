<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="
		http://www.springframework.org/schema/beans
		http://www.springframework.org/schema/beans/spring-beans-3.1.xsd">

    <bean id="genericObjectPoolConfig" class="org.apache.commons.pool2.impl.GenericObjectPoolConfig">
        <property name="maxTotal" value="${redis.maxTotal}" />
        <property name="maxIdle" value="${redis.maxIdle}"/>
        <property name="minIdle" value="${redis.minIdle}"/>
        <property name="maxWaitMillis" value="${redis.maxWaitMillis}"/>
        <property name="testOnBorrow" value="${redis.testOnBorrow}" />
    </bean>
    <bean id="jedisCluster" class="com.youxin.risk.commons.tools.redis.JedisClusterFactory">
        <property name="genericObjectPoolConfig" ref="genericObjectPoolConfig"/>
        <property name="connectionTimeout" value="${redis.cluster.connectionTimeout}"/>
        <property name="soTimeout" value="${redis.cluster.soTimeout}"/>
        <property name="maxAttempts" value="${redis.cluster.maxAttempts}"/>
        <property name="password" value="${redis.cluster.password}"/>
        <property name="nodes" value="${redis.cluster.nodes}"/>
    </bean>

    <bean id="retryableJedis" class="com.youxin.risk.commons.tools.redis.RetryableJedis">
        <property name="jedisCluster" ref="jedisCluster"/>
    </bean>

    <bean id="jedisPoolConfig" class="redis.clients.jedis.JedisPoolConfig">
        <property name="maxTotal" value="1000" />
        <property name="maxIdle" value="5" />
        <property name="maxWaitMillis" value="5000" />
        <property name="testOnBorrow" value="false" />
    </bean>

    <!-- jedisCluster config -->
    <bean id="raCacheRedisService" class="com.youxin.risk.ra.service.impl.RedisClusterServiceImpl">
        <constructor-arg index="0">
            <set>
                <bean class="redis.clients.jedis.HostAndPort">
                    <constructor-arg type="java.lang.String" value="${ra.redis.cluster.node1.host}"/>
                    <constructor-arg type="int" value="${ra.redis.cluster.node1.port}"/>
                </bean>
                <bean class="redis.clients.jedis.HostAndPort">
                    <constructor-arg type="java.lang.String" value="${ra.redis.cluster.node2.host}"/>
                    <constructor-arg type="int" value="${ra.redis.cluster.node2.port}"/>
                </bean>
                <bean class="redis.clients.jedis.HostAndPort">
                    <constructor-arg type="java.lang.String" value="${ra.redis.cluster.node3.host}"/>
                    <constructor-arg type="int" value="${ra.redis.cluster.node3.port}"/>
                </bean>
                <bean class="redis.clients.jedis.HostAndPort">
                    <constructor-arg type="java.lang.String" value="${ra.redis.cluster.node4.host}"/>
                    <constructor-arg type="int" value="${ra.redis.cluster.node4.port}"/>
                </bean>
                <bean class="redis.clients.jedis.HostAndPort">
                    <constructor-arg type="java.lang.String" value="${ra.redis.cluster.node5.host}"/>
                    <constructor-arg type="int" value="${ra.redis.cluster.node5.port}"/>
                </bean>
                <bean class="redis.clients.jedis.HostAndPort">
                    <constructor-arg type="java.lang.String" value="${ra.redis.cluster.node6.host}"/>
                    <constructor-arg type="int" value="${ra.redis.cluster.node6.port}"/>
                </bean>
            </set>
        </constructor-arg>
        <constructor-arg index="1" value="5000" />
        <constructor-arg index="2" value="60000" />
        <constructor-arg index="3" value="3" />
        <constructor-arg index="4" value="${ra.redis.cluster.password}" />
        <constructor-arg index="5" ref="jedisPoolConfig" />
    </bean>


    <bean id="cacheRedisService" class="com.youxin.risk.verify.redis.RedisClusterServiceImpl">
        <constructor-arg index="0">
            <set>
                <bean class="redis.clients.jedis.HostAndPort">
                    <constructor-arg type="java.lang.String" value="${redis.cluster.node1.host}"/>
                    <constructor-arg type="int" value="${redis.cluster.node1.port}"/>
                </bean>
                <bean class="redis.clients.jedis.HostAndPort">
                    <constructor-arg type="java.lang.String" value="${redis.cluster.node2.host}"/>
                    <constructor-arg type="int" value="${redis.cluster.node2.port}"/>
                </bean>
                <bean class="redis.clients.jedis.HostAndPort">
                    <constructor-arg type="java.lang.String" value="${redis.cluster.node3.host}"/>
                    <constructor-arg type="int" value="${redis.cluster.node3.port}"/>
                </bean>
                <bean class="redis.clients.jedis.HostAndPort">
                    <constructor-arg type="java.lang.String" value="${redis.cluster.node4.host}"/>
                    <constructor-arg type="int" value="${redis.cluster.node4.port}"/>
                </bean>
                <bean class="redis.clients.jedis.HostAndPort">
                    <constructor-arg type="java.lang.String" value="${redis.cluster.node5.host}"/>
                    <constructor-arg type="int" value="${redis.cluster.node5.port}"/>
                </bean>
                <bean class="redis.clients.jedis.HostAndPort">
                    <constructor-arg type="java.lang.String" value="${redis.cluster.node6.host}"/>
                    <constructor-arg type="int" value="${redis.cluster.node6.port}"/>
                </bean>
            </set>
        </constructor-arg>
        <constructor-arg index="1" value="5000" />
        <constructor-arg index="2" value="60000" />
        <constructor-arg index="3" value="3" />
        <constructor-arg index="4" value="${redis.cluster.verify.password}" />
        <constructor-arg index="5" ref="jedisPoolConfig" />
    </bean>

    <bean id="datacenterCacheRedisService" class="com.youxin.risk.verify.redis.RedisClusterServiceImpl">
        <constructor-arg index="0">
            <set>
                <bean class="redis.clients.jedis.HostAndPort">
                    <constructor-arg type="java.lang.String" value="${redis.cluster.cache.node1.host}"/>
                    <constructor-arg type="int" value="${redis.cluster.cache.node1.port}"/>
                </bean>
                <bean class="redis.clients.jedis.HostAndPort">
                    <constructor-arg type="java.lang.String" value="${redis.cluster.cache.node2.host}"/>
                    <constructor-arg type="int" value="${redis.cluster.cache.node2.port}"/>
                </bean>
                <bean class="redis.clients.jedis.HostAndPort">
                    <constructor-arg type="java.lang.String" value="${redis.cluster.cache.node3.host}"/>
                    <constructor-arg type="int" value="${redis.cluster.cache.node3.port}"/>
                </bean>
                <bean class="redis.clients.jedis.HostAndPort">
                    <constructor-arg type="java.lang.String" value="${redis.cluster.cache.node4.host}"/>
                    <constructor-arg type="int" value="${redis.cluster.cache.node4.port}"/>
                </bean>
                <bean class="redis.clients.jedis.HostAndPort">
                    <constructor-arg type="java.lang.String" value="${redis.cluster.cache.node5.host}"/>
                    <constructor-arg type="int" value="${redis.cluster.cache.node5.port}"/>
                </bean>
                <bean class="redis.clients.jedis.HostAndPort">
                    <constructor-arg type="java.lang.String" value="${redis.cluster.cache.node6.host}"/>
                    <constructor-arg type="int" value="${redis.cluster.cache.node6.port}"/>
                </bean>
            </set>
        </constructor-arg>
        <constructor-arg index="1" value="5000" />
        <constructor-arg index="2" value="60000" />
        <constructor-arg index="3" value="3" />
        <constructor-arg index="4" value="${redis.cluster.cache.datacenter.password}" />
        <constructor-arg index="5" ref="jedisPoolConfig" />
    </bean>

</beans>