package com.youxin.risk.relation.graph;

import com.google.common.collect.Maps;
import com.youxin.risk.datacenter.service.RelationGraphService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Map;

@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration("classpath:spring/spring-config.xml")
public class RelationGraphServiceTest {

    @Autowired
    private RelationGraphService relationGraphService;

    @Test
    public void graphBlack() {
        Map<String, Object> map = Maps.newHashMap();
        map.put("loanKey", "hh_20231018171710_5328188685691062187");
        map.put("userKey", "07820d4f545110dec4edf691aebf231a");
        map.put("system_id", "HAO_HUAN");
        Map<String, Object> resultMap = relationGraphService.graphBlack(map);
        log.info("查询结果" + resultMap);
    }
}
